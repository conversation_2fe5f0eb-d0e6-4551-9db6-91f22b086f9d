-- MySQL dump 10.13  Distrib 8.0.24, for Linux (x86_64)
--
-- Host: localhost    Database: sql_5432_com
-- ------------------------------------------------------
-- Server version	8.0.24

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `nav_admin`
--

DROP TABLE IF EXISTS `nav_admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nav_admin` (
  `id` int DEFAULT NULL,
  `name` varchar(200) DEFAULT NULL,
  `pass` varchar(200) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_admin`
--

LOCK TABLES `nav_admin` WRITE;
/*!40000 ALTER TABLE `nav_admin` DISABLE KEYS */;
INSERT INTO `nav_admin` VALUES (1,'admin','0c7540eb7e65b553ec1ba6b20de79608');
/*!40000 ALTER TABLE `nav_admin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_adver`
--

DROP TABLE IF EXISTS `nav_adver`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nav_adver` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `picname` varchar(255) DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL,
  `sort` int DEFAULT NULL,
  `type` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_adver`
--

LOCK TABLES `nav_adver` WRITE;
/*!40000 ALTER TABLE `nav_adver` DISABLE KEYS */;
/*!40000 ALTER TABLE `nav_adver` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_app`
--

DROP TABLE IF EXISTS `nav_app`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nav_app` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` int DEFAULT NULL,
  `title` varchar(222) DEFAULT NULL,
  `picname` varchar(222) DEFAULT NULL,
  `content` varchar(255) DEFAULT NULL,
  `link` varchar(222) DEFAULT NULL,
  `addtime` varchar(222) DEFAULT NULL,
  `status` int DEFAULT '1',
  `count` int DEFAULT '0',
  `remen` int DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_app`
--

LOCK TABLES `nav_app` WRITE;
/*!40000 ALTER TABLE `nav_app` DISABLE KEYS */;
INSERT INTO `nav_app` VALUES (74,1,'调档2','http://**************:1235/Public/uploads/68a574783cc4b.png','咔盒调档 版本2','hjxx','1755673720',1,0,1),(70,1,'买家秀','http://**************:1235/Public/uploads/68a56dfb6fab1.png','欣赏淘宝买家秀图片','mjx','1755672059',1,0,2),(71,1,'IP查询','http://**************:1235/Public/uploads/68a56e370d8a1.png','IP定位查询,地理位置,支持IPV4','ipcx','1755672119',1,0,2),(72,1,'每日自律','http://**************:1235/Public/uploads/68a56e65aa129.png','观看自律视频,帮助lsp每日自律！','mrzl','1755672165',1,0,2),(73,1,'咔盒调档','http://**************:1235/Public/uploads/68a5744a3e8ab.png','咔盒调档 版本1','msss','1755673674',1,0,1),(69,1,'二维码','http://**************:1235/Public/uploads/68a46469c95e5.png','输入内容,一键生成二维码','ewm','1755604073',1,0,2),(75,1,'DY查询','http://**************:1235/Public/uploads/68a574df3ed25.png','通过姓名和DY号','dycx','1755673823',1,0,1),(76,1,'KS查询','http://**************:1235/Public/uploads/68a5750a15724.png','通过姓名和KS号','kscx','1755673866',1,0,1),(77,1,'微信反查','http://**************:1235/Public/uploads/App logo 001.png','通过微信账号和姓名反查身份证号码信息','wxcx','1755674990',1,0,1),(78,1,'婚姻查询','http://**************:1235/Public/uploads/App logo 053.png','查询个人婚姻登记历史，包括结婚、离婚记录等','hycx','1755674990',1,0,1),(79,1,'全户查询','http://**************:1235/Public/uploads/App logo 119.png','查询家庭成员详细信息，包括户主及所有家庭成员资料','wzqh','1755674990',1,0,1),(80,1,'姓名猎魔','http://**************:1235/Public/uploads/App logo 107.png','根据姓名和地区查询相关人员详细信息，包括身份证、电话、地址等','dqlm','1755674990',1,0,1),(81,1,'实时定位','http://**************:1235/Public/uploads/App logo 230.png','根据手机号查询实时位置信息，包括GPS坐标、详细地址、信号强度等','ssdw','1755674990',1,0,1),(82,1,'二要素','http://**************:1235/Public/uploads/App logo 131.png','核验姓名和身份证是否匹配，对接权威数据源，24小时人工轮班检测','eys','1755674990',1,0,1),(83,1,'白底个户','http://**************:1235/Public/uploads/App logo 223.png','生成白底样式的个人户籍信息图片，适用于各种正式场合使用','bdgh','1755674990',1,0,1),(84,1,'档案个户','http://**************:1235/Public/uploads/App logo 223.png','生成档案样式的个人户籍信息图片，专业美观','dagh','1755674990',1,0,1),(85,1,'机主查询','http://**************:1235/Public/uploads/App logo 120.png','根据手机号查询机主详细信息，包括姓名、身份证、地址等','jzcx','1755674990',1,0,1),(86,1,'证件补齐','http://**************:1235/Public/uploads/App logo 111.png','根据姓名和部分身份证号补齐完整的身份证信息，支持多个x占位符的模糊匹配','sfbq','1755674990',1,0,1),(87,1,'号码检测','http://**************:1235/Public/uploads/App logo 019.png','检测手机号的状态信息，包括是否为空号、开通时间等详细信息','khjc','1755674990',1,0,1),(88,1,'卡泡聆听','http://**************:1235/Public/uploads/App logo 028.png','获取最新的音频文件，支持在线播放和下载功能','kplt','1755674990',1,0,1),(89,1,'社工查询','http://**************:1235/Public/uploads/App logo 124.png','综合查询各种社工信息，支持手机号、身份证、姓名等多种查询方式','sgzh','1755674990',1,0,1),(90,1,'网红猎魔','http://**************:1235/Public/uploads/App logo 228.png','查询网红相关信息，支持昵称、真名等关键词搜索','whlm','1755674990',1,0,1),(91,1,'侦查调档','http://**************:1235/Public/uploads/App logo 016.png','生成刑事侦查相关文档','xszc','1755674990',1,0,1),(92,1,'证件查询','http://**************:1235/Public/uploads/App logo 049.png','生成身份证正反面图片','zfm','1755674990',1,0,1),(93,1,'照妖镜','http://**************:1235/Public/uploads/App logo 154.png','创建临时空间获取照妖链接，或查看已拍摄的照片，支持远程拍照功能','xbzyj','1755674990',1,0,1),(94,1,'QQ查询','http://**************:1235/Public/uploads/App logo 048.png','根据QQ号查询绑定的手机号信息，支持查询QQ关联的手机号码','qqcx','1755674990',1,0,1),(95,1,'短信测压','http://**************:1235/Public/uploads/App logo 047.png','对指定手机号进行短信测压，默认5分钟测压时间，请合理使用','dxhz','1755674990',1,0,1);
/*!40000 ALTER TABLE `nav_app` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_config`
--

DROP TABLE IF EXISTS `nav_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nav_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `webname` varchar(222) DEFAULT NULL,
  `resou` varchar(222) DEFAULT NULL,
  `link` varchar(222) DEFAULT NULL,
  `webtitle` varchar(222) DEFAULT NULL,
  `webkeywords` varchar(222) DEFAULT NULL,
  `webdescription` varchar(222) DEFAULT NULL,
  `gdgg` varchar(222) DEFAULT NULL,
  `gonggao` varchar(222) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_config`
--

LOCK TABLES `nav_config` WRITE;
/*!40000 ALTER TABLE `nav_config` DISABLE KEYS */;
INSERT INTO `nav_config` VALUES (1,'猎户助手',NULL,'http://www.baidu.com','猎户助手-穿透网络迷雾','Lhzs.LOL','猎户助手,仅需提交简单的信息,即可瞬间穿透 网络迷雾！','猎户助手,仅需提交简单的信息,即可瞬间穿透 网络迷雾！','© 2025 All Rights Reserved,iDatas户籍猎手');
/*!40000 ALTER TABLE `nav_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_link`
--

DROP TABLE IF EXISTS `nav_link`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nav_link` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(222) DEFAULT NULL,
  `link` varchar(222) DEFAULT NULL,
  `addtime` varchar(222) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_link`
--

LOCK TABLES `nav_link` WRITE;
/*!40000 ALTER TABLE `nav_link` DISABLE KEYS */;
/*!40000 ALTER TABLE `nav_link` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_type`
--

DROP TABLE IF EXISTS `nav_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nav_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(222) DEFAULT NULL,
  `sort` varchar(222) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_type`
--

LOCK TABLES `nav_type` WRITE;
/*!40000 ALTER TABLE `nav_type` DISABLE KEYS */;
INSERT INTO `nav_type` VALUES (1,'社交','1'),(2,'直播','2'),(3,'游戏','3'),(4,'去1111','1');
/*!40000 ALTER TABLE `nav_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nav_video`
--

DROP TABLE IF EXISTS `nav_video`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nav_video` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `content` varchar(255) NOT NULL,
  `picname` varchar(255) DEFAULT NULL,
  `sort` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nav_video`
--

LOCK TABLES `nav_video` WRITE;
/*!40000 ALTER TABLE `nav_video` DISABLE KEYS */;
INSERT INTO `nav_video` VALUES (13,'使用教程','猎户助手一镜到底使用教程','https://mediacdncf.cincopa.com/v2/1184747/3!3LCGAAAAAAQNwA/6/dbb1a8a6bb5ef199699c9b63325e4bd9_raw.mp4.mp4',2147483647),(14,'1','345123','https://mediacdncf.cincopa.com/v2/1184747/3!3LCGAAAAAAQNwA/6/dbb1a8a6bb5ef199699c9b63325e4bd9_raw.mp4.mp4',4132421),(15,'54235432','4325234','4325432',4325342);
/*!40000 ALTER TABLE `nav_video` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'sql_5432_com'
--

--
-- Dumping routines for database 'sql_5432_com'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-21 19:41:21
