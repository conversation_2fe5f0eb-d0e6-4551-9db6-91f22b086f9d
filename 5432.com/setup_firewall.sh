#!/bin/bash

# 防火墙和安全设置脚本

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== VPN代理防火墙配置脚本 ===${NC}"
echo ""

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    echo -e "${RED}错误: 此脚本需要root权限运行${NC}"
    echo "请使用: sudo $0"
    exit 1
fi

# 需要开放的端口
PORTS=(************** 8083)

# 检测系统类型
detect_system() {
    if [[ -f /etc/redhat-release ]]; then
        SYSTEM="centos"
    elif cat /etc/issue | grep -Eqi "debian"; then
        SYSTEM="debian"
    elif cat /etc/issue | grep -Eqi "ubuntu"; then
        SYSTEM="ubuntu"
    else
        SYSTEM="unknown"
    fi
    echo -e "${BLUE}检测到系统类型: $SYSTEM${NC}"
}

# 配置UFW防火墙 (Ubuntu/Debian)
configure_ufw() {
    echo -e "${BLUE}配置UFW防火墙...${NC}"

    # 安装UFW (如果未安装)
    if ! command -v ufw >/dev/null 2>&1; then
        echo "安装UFW..."
        apt update
        apt install -y ufw
    fi

    # 设置默认策略
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing

    # 允许SSH (防止锁定)
    ufw allow ssh
    ufw allow 22/tcp

    # 允许代理端口
    for port in "${PORTS[@]}"; do
        echo "开放端口: $port"
        ufw allow $port/tcp
    done

    # 启用UFW
    ufw --force enable

    echo -e "${GREEN}✓ UFW防火墙配置完成${NC}"
    ufw status verbose
}

# 配置firewalld防火墙 (CentOS/RHEL)
configure_firewalld() {
    echo -e "${BLUE}配置firewalld防火墙...${NC}"

    # 安装firewalld (如果未安装)
    if ! command -v firewall-cmd >/dev/null 2>&1; then
        echo "安装firewalld..."
        yum install -y firewalld
    fi

    # 启动firewalld
    systemctl start firewalld
    systemctl enable firewalld

    # 允许代理端口
    for port in "${PORTS[@]}"; do
        echo "开放端口: $port"
        firewall-cmd --permanent --add-port=$port/tcp
    done

    # 重载配置
    firewall-cmd --reload

    echo -e "${GREEN}✓ firewalld防火墙配置完成${NC}"
    firewall-cmd --list-all
}

# 配置iptables防火墙
configure_iptables() {
    echo -e "${BLUE}配置iptables防火墙...${NC}"

    # 安装iptables-persistent (Debian/Ubuntu)
    if [[ "$SYSTEM" == "debian" ]] || [[ "$SYSTEM" == "ubuntu" ]]; then
        if ! dpkg -l | grep -q iptables-persistent; then
            echo "安装iptables-persistent..."
            apt update
            DEBIAN_FRONTEND=noninteractive apt install -y iptables-persistent
        fi
    fi

    # 清除现有规则
    iptables -F
    iptables -X
    iptables -t nat -F
    iptables -t nat -X
    iptables -t mangle -F
    iptables -t mangle -X

    # 设置默认策略
    iptables -P INPUT DROP
    iptables -P FORWARD DROP
    iptables -P OUTPUT ACCEPT

    # 允许本地回环
    iptables -A INPUT -i lo -j ACCEPT
    iptables -A OUTPUT -o lo -j ACCEPT

    # 允许已建立的连接
    iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

    # 允许SSH
    iptables -A INPUT -p tcp --dport 22 -j ACCEPT

    # 允许代理端口
    for port in "${PORTS[@]}"; do
        echo "开放端口: $port"
        iptables -A INPUT -p tcp --dport $port -j ACCEPT
    done

    # 保存规则
    if [[ "$SYSTEM" == "centos" ]]; then
        service iptables save 2>/dev/null || {
            iptables-save > /etc/sysconfig/iptables
        }
    else
        iptables-save > /etc/iptables/rules.v4 2>/dev/null || {
            mkdir -p /etc/iptables
            iptables-save > /etc/iptables/rules.v4
        }
    fi

    echo -e "${GREEN}✓ iptables防火墙配置完成${NC}"
    iptables -L -n
}

# 优化系统参数
optimize_system() {
    echo -e "${BLUE}优化系统参数...${NC}"

    # 创建系统优化配置
    cat > /etc/sysctl.d/99-vpn-optimization.conf << EOF
# VPN代理优化参数

# 网络优化
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216

# TCP优化
net.ipv4.tcp_congestion_control = bbr
net.core.default_qdisc = fq
net.ipv4.tcp_fastopen = 3
net.ipv4.tcp_no_metrics_save = 1
net.ipv4.tcp_ecn = 0
net.ipv4.tcp_frto = 0
net.ipv4.tcp_mtu_probing = 0
net.ipv4.tcp_rfc1337 = 0
net.ipv4.tcp_sack = 1
net.ipv4.tcp_fack = 1
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_adv_win_scale = 1
net.ipv4.tcp_moderate_rcvbuf = 1

# 连接数优化
net.core.somaxconn = 32768
net.core.netdev_max_backlog = 32768
net.ipv4.tcp_max_orphans = 32768
net.ipv4.tcp_max_syn_backlog = 32768
net.ipv4.tcp_timestamps = 0
net.ipv4.tcp_synack_retries = 2
net.ipv4.tcp_syn_retries = 2
net.ipv4.tcp_tw_recycle = 0
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_mem = 25600 51200 102400
net.ipv4.tcp_fin_timeout = 10
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_intvl = 15
net.ipv4.tcp_keepalive_probes = 5

# 文件描述符限制
fs.file-max = 1000000
fs.inotify.max_user_instances = 8192

# 安全参数
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.secure_redirects = 0
net.ipv4.conf.default.secure_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.default.rp_filter = 1
net.ipv4.conf.all.rp_filter = 1
net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
net.ipv4.conf.all.log_martians = 1
net.ipv4.conf.default.log_martians = 1
net.ipv4.ip_forward = 1
EOF

    # 应用配置
    sysctl -p /etc/sysctl.d/99-vpn-optimization.conf

    # 设置文件描述符限制
    cat >> /etc/security/limits.conf << EOF

# VPN代理文件描述符限制
* soft nofile 1000000
* hard nofile 1000000
root soft nofile 1000000
root hard nofile 1000000
EOF

    echo -e "${GREEN}✓ 系统参数优化完成${NC}"
}

# 主函数
main() {
    detect_system

    echo ""
    echo -e "${YELLOW}选择防火墙配置方式:${NC}"
    echo "1. UFW (推荐用于Ubuntu/Debian)"
    echo "2. firewalld (推荐用于CentOS/RHEL)"
    echo "3. iptables (通用)"
    echo "4. 跳过防火墙配置"
    echo ""
    read -p "请选择 [1-4]: " choice

    case $choice in
        1)
            configure_ufw
            ;;
        2)
            configure_firewalld
            ;;
        3)
            configure_iptables
            ;;
        4)
            echo -e "${YELLOW}跳过防火墙配置${NC}"
            ;;
        *)
            echo -e "${RED}无效选择，使用默认配置${NC}"
            if [[ "$SYSTEM" == "ubuntu" ]] || [[ "$SYSTEM" == "debian" ]]; then
                configure_ufw
            elif [[ "$SYSTEM" == "centos" ]]; then
                configure_firewalld
            else
                configure_iptables
            fi
            ;;
    esac

    echo ""
    read -p "是否优化系统参数? (y/N): " optimize
    if [[ $optimize =~ ^[Yy]$ ]]; then
        optimize_system
    fi

    echo ""
    echo -e "${GREEN}=== 配置完成 ===${NC}"
    echo -e "${BLUE}已开放端口:${NC}"
    for port in "${PORTS[@]}"; do
        echo "  - $port/tcp (代理服务)"
    done
    echo "  - 22/tcp (SSH)"
    echo ""
    echo -e "${YELLOW}注意: 请确保您的云服务商安全组也开放了相应端口${NC}"
}

# 运行主函数
main "$@"