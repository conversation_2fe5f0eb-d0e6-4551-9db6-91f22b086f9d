<?php
/**
 * 数据库初始化脚本
 * 创建会员购买系统所需的数据表
 */

// 引入ThinkPHP框架
define("APP_PATH","./Application/");
require("./ThinkPHP/ThinkPHP.php");

// 创建数据库连接
$db = M();

try {
    // 创建订单状态跟踪表
    $sql = "CREATE TABLE IF NOT EXISTS `nav_order_status` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `order_id` varchar(100) NOT NULL COMMENT '订单ID',
        `product_type` varchar(10) NOT NULL COMMENT '产品类型：1=38天会员，2=永久会员',
        `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending=待处理，processing=处理中，completed=已完成，failed=失败',
        `token` varchar(50) DEFAULT NULL COMMENT '生成的会员Token',
        `reg_time` datetime DEFAULT NULL COMMENT '注册时间',
        `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
        `result_data` text COMMENT '完整的API返回数据',
        `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
        `client_ip` varchar(45) DEFAULT NULL COMMENT '客户端IP',
        `created_time` datetime NOT NULL COMMENT '创建时间',
        `updated_time` datetime NOT NULL COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `order_id` (`order_id`),
        KEY `status` (`status`),
        KEY `created_time` (`created_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单状态跟踪表';";
    
    $result = $db->execute($sql);
    
    if ($result !== false) {
        echo "✅ 订单状态跟踪表创建成功！\n";
    } else {
        echo "❌ 订单状态跟踪表创建失败！\n";
    }
    
    // 检查表是否存在
    $tables = $db->query("SHOW TABLES LIKE 'nav_order_status'");
    if ($tables) {
        echo "✅ 数据表验证成功，表已存在\n";
        
        // 显示表结构
        $structure = $db->query("DESCRIBE nav_order_status");
        echo "\n📋 表结构信息：\n";
        echo "字段名\t\t类型\t\t\t空值\t键\t默认值\t\t备注\n";
        echo "--------------------------------------------------------------------\n";
        foreach ($structure as $field) {
            printf("%-15s %-20s %-8s %-8s %-15s %s\n", 
                $field['Field'], 
                $field['Type'], 
                $field['Null'], 
                $field['Key'], 
                $field['Default'] ?: 'NULL',
                ''
            );
        }
    } else {
        echo "❌ 数据表验证失败，表不存在\n";
    }
    
    echo "\n🎉 数据库初始化完成！\n";
    echo "现在可以访问会员购买页面了：/Index/Utils/index\n";
    
} catch (Exception $e) {
    echo "❌ 数据库初始化失败：" . $e->getMessage() . "\n";
    echo "请检查数据库配置和权限\n";
}
?>
