<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>Token购买-Lhzs.LOL</title>
    <link rel="stylesheet" href="/Public/css/fontawesome.min.css">
    <script src="/Public/js/qrcode.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 页面滚动设置 - 隐藏滚动条但保持滚动功能 */
        html, body {
            overflow-x: hidden;
            overflow-y: auto;
        }

        /* 隐藏滚动条 */
        html::-webkit-scrollbar,
        body::-webkit-scrollbar,
        *::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        html, body, * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        body {
            font-family: 'Courier New', 'Monaco', '<PERSON>lo', monospace;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 10px 10px 20px 10px;
            color: #333;
            margin: 0;
            /* 禁用触摸缩放但保持滚动 */
            touch-action: pan-y;
            -webkit-touch-callout: none;
        }

        /* 允许垂直滚动但禁用缩放 */
        * {
            touch-action: pan-y;
        }

        /* 对于按钮等交互元素，允许点击 */
        button, .btn, .card-purchase-btn, .support-link, .membership-card {
            touch-action: manipulation;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 2px solid #4facfe;
            position: relative;
        }

        /* 返回首页按钮 - 左上角固定 */
        .home-btn {
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1000;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(79, 172, 254, 0.4);
            border: 2px solid #fff;
            font-family: 'Courier New', monospace;
        }

        .home-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(79, 172, 254, 0.6);
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 3px solid #fff;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 8px;
            text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.3);
            font-family: 'Courier New', monospace;
        }

        .header p {
            font-size: 1rem;
            opacity: 0.9;
            font-family: 'Courier New', monospace;
        }

        .content {
            padding: 20px;
        }

        .membership-cards {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 30px;
        }

        .membership-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 4px 4px 0px rgba(0, 0, 0, 0.2);
            border: 3px solid #e0e0e0;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            font-family: 'Courier New', monospace;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .card-content {
            display: flex;
            align-items: center;
            gap: 15px;
            min-height: 120px;
        }

        /* 卡片购买按钮 */
        .card-purchase-btn {
            width: 100%;
            margin-top: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: 2px solid #fff;
            padding: 12px 20px;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 3px 3px 0px rgba(79, 172, 254, 0.4);
            font-family: 'Courier New', monospace;
        }

        .card-purchase-btn:hover {
            transform: translate(-1px, -1px);
            box-shadow: 4px 4px 0px rgba(79, 172, 254, 0.6);
        }

        .membership-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            transform: scaleX(0);
            transition: transform 0.2s ease;
        }

        .membership-card:hover::before {
            transform: scaleX(1);
        }

        .membership-card:hover {
            transform: translate(-2px, -2px);
            box-shadow: 6px 6px 0px rgba(0, 0, 0, 0.3);
        }

        .card-left {
            flex: 0 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-width: 120px;
            padding-right: 15px;
            border-right: 2px solid #e0e0e0;
        }

        .card-right {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-left: 15px;
        }

        .card-icon {
            font-size: 2.5rem;
            margin-bottom: 8px;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.8);
        }

        .monthly .card-icon {
            color: #ff6b6b;
            border-color: #ff6b6b;
        }

        .lifetime .card-icon {
            color: #feca57;
            border-color: #feca57;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 6px;
            font-family: 'Courier New', monospace;
            border: 2px solid #333;
            padding: 4px 8px;
            border-radius: 3px;
            background: #f8f9fa;
        }

        .card-price {
            font-size: 1.3rem;
            font-weight: bold;
            color: #4facfe;
            font-family: 'Courier New', monospace;
            border: 2px solid #4facfe;
            padding: 4px 8px;
            border-radius: 3px;
            background: rgba(79, 172, 254, 0.1);
        }

        .card-features {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .card-features li {
            padding: 6px 8px;
            display: flex;
            align-items: center;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.1;
            border: 1px solid #e0e0e0;
            border-radius: 3px;
            background: #f8f9fa;
            transition: all 0.2s ease;
        }

        .card-features li:hover {
            border-color: #4facfe;
            background: rgba(79, 172, 254, 0.1);
        }

        .card-features li::before {
            content: '✓';
            color: #27ae60;
            font-weight: bold;
            margin-right: 6px;
            font-size: 0.8rem;
            flex-shrink: 0;
            width: 12px;
            height: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #27ae60;
            border-radius: 2px;
            background: rgba(39, 174, 96, 0.1);
        }

        .card-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            border: 1px solid #fff;
        }

        .purchase-section {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: 2px solid #fff;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 3px 3px 0px rgba(79, 172, 254, 0.4);
            font-family: 'Courier New', monospace;
            margin: 5px;
        }

        .btn:hover {
            transform: translate(-1px, -1px);
            box-shadow: 4px 4px 0px rgba(79, 172, 254, 0.6);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            background: #ccc;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 3px 3px 0px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary:hover {
            box-shadow: 4px 4px 0px rgba(102, 126, 234, 0.6);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            box-shadow: 3px 3px 0px rgba(255, 107, 107, 0.4);
        }

        .btn-danger:hover {
            box-shadow: 4px 4px 0px rgba(255, 107, 107, 0.6);
        }

        .support-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            border: 2px solid #e0e0e0;
            font-family: 'Courier New', monospace;
        }

        .support-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 12px;
        }

        .support-link {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background: white;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
            box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
            border: 2px solid #ddd;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .support-link:hover {
            transform: translate(-1px, -1px);
            box-shadow: 3px 3px 0px rgba(0, 0, 0, 0.3);
        }

        .support-link i {
            margin-right: 6px;
            font-size: 1rem;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 4px 4px 0px rgba(0, 0, 0, 0.4);
            animation: modalSlideIn 0.3s ease;
            border: 3px solid #4facfe;
            position: relative;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.8; transform: translateY(-50%) scale(1); }
            50% { opacity: 1; transform: translateY(-50%) scale(1.05); }
        }

        .modal-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 5px 5px 0 0;
            text-align: center;
            position: relative;
            font-family: 'Courier New', monospace;
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 20px;
            text-align: center;
            font-family: 'Courier New', monospace;
        }

        .payment-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 15px 0;
        }

        .payment-method {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-family: 'Courier New', monospace;
            box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.1);
        }

        .payment-method:hover {
            border-color: #4facfe;
            background: #f8f9ff;
            transform: translate(-1px, -1px);
            box-shadow: 3px 3px 0px rgba(0, 0, 0, 0.2);
        }

        .payment-method.selected {
            border-color: #4facfe;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            box-shadow: 2px 2px 0px rgba(79, 172, 254, 0.4);
        }

        .qr-container {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
        }

        .qr-code {
            margin: 15px 0;
        }

        .order-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
            border: 2px solid #e0e0e0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid #e0e0e0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .copy-btn {
            background: #4facfe;
            color: white;
            border: 1px solid #fff;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.7rem;
            font-family: 'Courier New', monospace;
            box-shadow: 1px 1px 0px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
        }

        .copy-btn:hover {
            background: #3a8bfd;
            transform: translate(-1px, -1px);
            box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.3);
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 5px 5px 15px 5px;
            }

            .container {
                margin: 0;
                border-radius: 8px;
            }

            .header {
                padding: 15px;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .content {
                padding: 15px;
            }

            .membership-card {
                padding: 12px;
                gap: 12px;
            }

            .card-content {
                flex-direction: row;
                gap: 12px;
                min-height: 100px;
            }

            .card-left {
                min-width: 80px;
                padding-right: 10px;
                border-right: 2px solid #e0e0e0;
                border-bottom: none;
                padding-bottom: 0;
            }

            .card-right {
                text-align: left;
                padding-left: 10px;
                padding-top: 0;
            }

            .card-purchase-btn {
                padding: 10px 16px;
                font-size: 0.9rem;
                margin-top: 12px;
            }

            .card-price {
                font-size: 1.8rem;
            }

            .support-links {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .payment-methods {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .home-btn {
                top: 10px;
                left: 10px;
                padding: 6px 12px;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 5px 5px 10px 5px;
            }

            .header {
                padding: 12px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .content {
                padding: 12px;
            }

            .membership-card {
                padding: 12px;
                gap: 12px;
            }

            .card-left {
                min-width: 70px;
                padding-right: 8px;
                border-right: 2px solid #e0e0e0;
                border-bottom: none;
                padding-bottom: 0;
            }

            .card-right {
                padding-left: 8px;
                padding-top: 0;
            }

            .card-icon {
                font-size: 1.8rem;
                width: 40px;
                height: 40px;
                margin-bottom: 4px;
            }

            .card-title {
                font-size: 0.9rem;
                padding: 2px 6px;
                margin-bottom: 4px;
            }

            .card-price {
                font-size: 1.1rem;
                padding: 3px 6px;
            }

            .card-features {
                gap: 2px;
            }

            .card-features li {
                font-size: 0.7rem;
                padding: 4px 6px;
                line-height: 1;
            }

            .card-features li::before {
                width: 10px;
                height: 10px;
                font-size: 0.7rem;
                margin-right: 4px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
                margin: 3px;
            }

            .modal-body {
                padding: 15px;
            }

            .home-btn {
                top: 5px;
                left: 5px;
                padding: 4px 8px;
                font-size: 0.7rem;
            }

            .card-purchase-btn {
                padding: 8px 12px;
                font-size: 0.8rem;
                margin-top: 10px;
            }
        }

        /* 限时优惠动画效果 */
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 3px 3px 0px rgba(255, 71, 87, 0.4);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 4px 4px 0px rgba(255, 71, 87, 0.6);
            }
            100% {
                transform: scale(1);
                box-shadow: 3px 3px 0px rgba(255, 71, 87, 0.4);
            }
        }

        @keyframes flash {
            0%, 50%, 100% {
                opacity: 1;
            }
            25%, 75% {
                opacity: 0.5;
            }
        }

        .card-badge {
            animation: flash 2s infinite;
        }

        /* 价格闪烁效果 */
        .card-price span[style*="color: #ff4757"] {
            animation: flash 1.5s infinite;
        }
    </style>
    
</head>
<body>
    <!-- 返回首页按钮 -->
    <button class="home-btn" onclick="goHome()">
        <i class="fas fa-home"></i> ←回到首页
    </button>

    <div class="container">
        <!-- 限时优惠横幅 -->
        <div style="background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%); color: white; text-align: center; padding: 15px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3); animation: pulse 2s infinite;">
            <div style="font-size: 1.2em; font-weight: bold; margin-bottom: 5px;">
                🔥 限时特惠活动进行中 🔥
            </div>
            <div style="font-size: 0.9em;">
                全场最低5折起！仅限24小时，错过再等一年！立即下单享受史上最低价格！
            </div>
            <div style="font-size: 0.8em; margin-top: 8px; opacity: 0.9;">
                ⏰ 活动倒计时：<span id="countdown" style="font-weight: bold; color: #ffeb3b;">23:59:59</span>
            </div>
        </div>

        <div class="header">
            <h1><i class="fas fa-crown"></i> Token购买-Lhzs.LOL</h1>
            <p>一切稳定,放心下单,客服全天售后在线！</p>
        </div>

        <div class="content">
            <div class="membership-cards">
                <!-- 月度会员卡片 -->
                <div class="membership-card monthly" data-product-id="85" data-product-type="1">
                    <div class="card-badge">🔥限时特价</div>
                    <div class="card-content">
                        <div class="card-left">
                            <div class="card-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="card-title">月度会员</div>
                            <div class="card-price">
                                <span style="text-decoration: line-through; color: #999; font-size: 0.8em;">¥28.05</span>
                                <span style="color: #ff4757; font-weight: bold; margin-left: 8px;">¥13.14</span>
                            </div>
                            <div style="color: #ff4757; font-size: 0.8em; margin-top: 5px;">⏰ 限时5折，仅剩24小时！</div>
                        </div>
                        <div class="card-right">
                            <ul class="card-features">
                                <li>买一月送一周</li>
                                <li>共计38天使用期</li>
                                <li>全功能无限制使用</li>
                                <li>优先技术支持</li>
                                <li>新功能抢先体验</li>
                            </ul>
                        </div>
                    </div>
                    <button class="card-purchase-btn" onclick="purchaseProduct('85', '1', '月度会员', '¥13.14')" style="background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%); animation: pulse 2s infinite;">
                        <i class="fas fa-flash"></i> 抢购月度会员 - 立省¥14.91
                    </button>
                </div>

                <!-- 永久会员卡片 -->
                <div class="membership-card lifetime" data-product-id="86" data-product-type="2">
                    <div class="card-badge">💎史上最低</div>
                    <div class="card-content">
                        <div class="card-left">
                            <div class="card-icon">
                                <i class="fas fa-infinity"></i>
                            </div>
                            <div class="card-title">永久会员</div>
                            <div class="card-price">
                                <span style="text-decoration: line-through; color: #999; font-size: 0.8em;">¥38.05</span>
                                <span style="color: #ff4757; font-weight: bold; margin-left: 8px;">¥23.24</span>
                            </div>
                            <div style="color: #ff4757; font-size: 0.8em; margin-top: 5px;">🚀 限时6折，错过再等一年！</div>
                        </div>
                        <div class="card-right">
                            <ul class="card-features">
                                <li>仅需多付¥10得永久</li>
                                <li>终身使用，永不到期</li>
                                <li>全功能无限制使用</li>
                                <li>VIP专属客服</li>
                                <li>所有未来功能免费</li>
                            </ul>
                        </div>
                    </div>
                    <button class="card-purchase-btn" onclick="purchaseProduct('86', '2', '永久会员', '¥23.24')" style="background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%); animation: pulse 2s infinite;">
                        <i class="fas fa-crown"></i> 抢购永久会员 - 立省¥14.81
                    </button>
                </div>
            </div>

            <div class="support-section">
                <h3><i class="fas fa-headset"></i> 需要帮助？</h3>
                <p>如有任何问题，请优先联系客服或投诉订单</p>
                <div class="support-links">
                    <a href="https://t.me/Dataso" target="_blank" class="support-link">
                        <i class="fab fa-telegram"></i> 联系客服
                    </a>
                    <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="support-link">
                        <i class="fas fa-exclamation-triangle"></i> 投诉订单
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 支付方式选择模态框 -->
    <div id="paymentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-credit-card"></i> 选择支付方式</h2>
                <button class="modal-close" onclick="closeModal('paymentModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="order-info">
                    <div class="info-row">
                        <span>商品名称：</span>
                        <span id="selectedProduct">-</span>
                    </div>
                    <div class="info-row">
                        <span>商品价格：</span>
                        <span id="selectedPrice">-</span>
                    </div>
                </div>

                <div class="payment-methods">
                    <div class="payment-method" data-pay-type="wxpay">
                        <i class="fab fa-weixin" style="color: #07c160; font-size: 1.5rem;"></i>
                        <span>微信支付</span>
                    </div>
                    <div class="payment-method" data-pay-type="alipay">
                        <i class="fab fa-alipay" style="color: #1677ff; font-size: 1.5rem;"></i>
                        <span>支付宝</span>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <button class="btn" id="confirmPaymentBtn" disabled>
                        <i class="fas fa-check"></i> 确认支付方式
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal('paymentModal')">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>

                <div class="support-section" style="margin-top: 20px;">
                    <p style="font-size: 0.9rem; color: #666;">
                        <i class="fas fa-info-circle"></i>
                        支付过程中遇到问题？请联系客服获取帮助
                    </p>
                    <div class="support-links">
                        <a href="https://t.me/Dataso" target="_blank" class="support-link">
                            <i class="fab fa-telegram"></i> 联系客服
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 支付二维码模态框 -->
    <div id="qrCodeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-qrcode"></i> 扫码支付</h2>
                <button class="modal-close" onclick="closeModal('qrCodeModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="order-info">
                    <div class="info-row">
                        <span>商品名称：</span>
                        <span id="qrProductName">-</span>
                        <button class="copy-btn" onclick="copyText('qrProductName')">复制</button>
                    </div>
                    <div class="info-row">
                        <span>下单时间：</span>
                        <span id="qrOrderTime">-</span>
                        <button class="copy-btn" onclick="copyText('qrOrderTime')">复制</button>
                    </div>
                    <div class="info-row">
                        <span>订单号：</span>
                        <span id="qrOrderId">-</span>
                        <button class="copy-btn" onclick="copyText('qrOrderId')">复制</button>
                    </div>
                    <div class="info-row">
                        <span>支付方式：</span>
                        <span id="qrPayType">-</span>
                    </div>
                </div>

                <div class="qr-container">
                    <p><i class="fas fa-mobile-alt"></i> 请使用手机<span id="payTypeText">微信/支付宝</span>扫码支付</p>
                    <div class="qr-code" id="qrCodeContainer">
                        <!-- 二维码将在这里生成 -->
                    </div>
                </div>

                <div id="paymentStatus"></div>

                <div style="margin-top: 20px;">
                    <button class="btn" id="checkPaymentBtn" onclick="checkPaymentStatus()">
                        <i class="fas fa-search"></i> 我已支付
                    </button>
                </div>

                <div class="support-section" style="margin-top: 20px;">
                    <p style="font-size: 0.9rem; color: #666;">
                        <i class="fas fa-shield-alt"></i>
                        支付安全提醒：请确认支付金额正确，如有疑问请联系客服
                    </p>
                    <div class="support-links">
                        <a href="https://t.me/Dataso" target="_blank" class="support-link">
                            <i class="fab fa-telegram"></i> 联系客服
                        </a>
                        <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="support-link">
                            <i class="fas fa-exclamation-triangle"></i> 投诉订单
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册成功模态框 -->
    <div id="successModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-check-circle"></i> 注册成功</h2>
                <button class="modal-close" onclick="closeModal('successModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="status-message status-success">
                    <i class="fas fa-party-horn"></i> 恭喜您！会员注册成功
                </div>

                <div class="order-info">
                    <div class="info-row">
                        <span>注册时间：</span>
                        <span id="successRegTime">-</span>
                        <button class="copy-btn" onclick="copyText('successRegTime')">复制</button>
                    </div>
                    <div class="info-row">
                        <span>到期时间：</span>
                        <span id="successExpireTime">-</span>
                        <button class="copy-btn" onclick="copyText('successExpireTime')">复制</button>
                    </div>
                    <div class="info-row">
                        <span>Token(卡密)：</span>
                        <span id="successToken" style="font-family: monospace; font-weight: bold; color: #e74c3c;">-</span>
                        <button class="copy-btn" onclick="copyText('successToken')">复制</button>
                    </div>
                </div>

                <div class="status-message status-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>重要提醒：</strong>Token(卡密)遗失后无法找回，请妥善保存！建议截图保存。
                </div>

                <div style="margin-top: 20px;">
                    <button class="btn" onclick="closeModal('successModal')">
                        <i class="fas fa-check"></i> 我已保存
                    </button>
                    <button class="btn btn-secondary" onclick="goHome()">
                        <i class="fas fa-home"></i> 返回首页
                    </button>
                </div>

                <div class="support-section" style="margin-top: 20px;">
                    <p style="font-size: 0.9rem; color: #666;">
                        <i class="fas fa-heart"></i>
                        感谢您的支持！如有任何问题，请随时联系客服
                    </p>
                    <div class="support-links">
                        <a href="https://t.me/Dataso" target="_blank" class="support-link">
                            <i class="fab fa-telegram"></i> 联系客服
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedPayType = null;
        let currentOrder = null;
        let currentProduct = null;
        let paymentCheckInterval = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            disableZoom();
        });

        // 禁用缩放功能但保持滚动
        function disableZoom() {
            // 禁用键盘缩放
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && (e.key === '+' || e.key === '-' || e.key === '0')) {
                    e.preventDefault();
                }
            });

            // 禁用鼠标滚轮缩放（但保持正常滚动）
            document.addEventListener('wheel', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                }
            }, { passive: false });

            // 禁用双击缩放但保持单击和滚动
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function(e) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    // 只阻止双击，不阻止滚动
                    if (e.touches.length === 0) {
                        e.preventDefault();
                    }
                }
                lastTouchEnd = now;
            }, false);

            // 禁用双指缩放手势
            document.addEventListener('gesturestart', function(e) {
                e.preventDefault();
            });

            document.addEventListener('gesturechange', function(e) {
                e.preventDefault();
            });

            document.addEventListener('gestureend', function(e) {
                e.preventDefault();
            });

            // 禁用多点触控缩放
            document.addEventListener('touchstart', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault();
                }
            }, { passive: false });
        }

        // 初始化页面
        function initializePage() {
            // 绑定支付方式点击事件
            document.querySelectorAll('.payment-method').forEach(method => {
                method.addEventListener('click', function() {
                    selectPaymentMethod(this);
                });
            });

            // 绑定确认支付方式按钮点击事件
            document.getElementById('confirmPaymentBtn').addEventListener('click', function() {
                if (selectedPayType && currentProduct) {
                    createOrder();
                }
            });

            // 添加安全提醒
            showSecurityReminder();
        }

        // 直接购买产品
        function purchaseProduct(productId, productType, productName, productPrice) {
            currentProduct = {
                id: productId,
                type: productType,
                name: productName,
                price: productPrice
            };
            showPaymentModal();
        }

        // 选择支付方式
        function selectPaymentMethod(method) {
            // 移除其他支付方式的选中状态
            document.querySelectorAll('.payment-method').forEach(m => {
                m.classList.remove('selected');
            });

            // 选中当前支付方式
            method.classList.add('selected');

            // 保存选中的支付方式
            selectedPayType = method.dataset.payType;

            // 启用确认按钮
            document.getElementById('confirmPaymentBtn').disabled = false;
        }

        // 显示支付方式选择模态框
        function showPaymentModal() {
            if (!currentProduct) {
                showMessage('产品信息错误', 'error');
                return;
            }

            // 更新模态框中的产品信息
            document.getElementById('selectedProduct').textContent = currentProduct.name;
            document.getElementById('selectedPrice').textContent = currentProduct.price;

            // 重置支付方式选择
            document.querySelectorAll('.payment-method').forEach(m => {
                m.classList.remove('selected');
            });
            selectedPayType = null;
            document.getElementById('confirmPaymentBtn').disabled = true;

            // 显示模态框
            document.getElementById('paymentModal').style.display = 'block';
        }

        // 创建订单
        async function createOrder() {
            if (!currentProduct || !selectedPayType) {
                showMessage('请选择商品和支付方式', 'error');
                return;
            }

            // 显示加载状态
            const btn = document.getElementById('confirmPaymentBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<span class="loading"></span>创建订单中...';
            btn.disabled = true;

            try {
                const response = await fetch('/Index/Utils/createOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        customer_contact: '<EMAIL>',
                        product_id: currentProduct.id,
                        pay_type: selectedPayType
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    currentOrder = data.data;
                    showQRCodeModal();
                    closeModal('paymentModal');
                } else {
                    showMessage(data.message || '订单创建失败', 'error');
                }
            } catch (error) {
                console.error('创建订单失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
                showSupportReminder();
            } finally {
                // 恢复按钮状态
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // 显示二维码支付模态框
        function showQRCodeModal() {
            if (!currentOrder) {
                showMessage('订单信息丢失', 'error');
                return;
            }

            // 更新订单信息
            document.getElementById('qrProductName').textContent = currentOrder.order_info.product_name;
            document.getElementById('qrOrderTime').textContent = new Date().toLocaleString('zh-CN');
            document.getElementById('qrOrderId').textContent = currentOrder.order_info.order_id;

            const payTypeText = selectedPayType === 'wxpay' ? '微信支付' : '支付宝';
            document.getElementById('qrPayType').textContent = payTypeText;
            document.getElementById('payTypeText').textContent = selectedPayType === 'wxpay' ? '微信' : '支付宝';

            // 生成二维码
            generateQRCode(currentOrder.payment_url);

            // 清除之前的状态消息
            document.getElementById('paymentStatus').innerHTML = '';

            // 显示模态框
            document.getElementById('qrCodeModal').style.display = 'block';
        }

        // 生成二维码
        function generateQRCode(url) {
            const container = document.getElementById('qrCodeContainer');
            container.innerHTML = '';

            try {
                const qr = qrcode(0, 'M');
                qr.addData(url);
                qr.make();

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = 300 / modules;

                canvas.width = 300;
                canvas.height = 300;
                canvas.style.border = '2px solid #e0e0e0';
                canvas.style.borderRadius = '10px';

                // 设置背景色为白色
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, 300, 300);

                // 绘制二维码
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                container.appendChild(canvas);
            } catch (error) {
                console.error('二维码生成失败:', error);
                container.innerHTML = '<p style="color: #e74c3c;">二维码生成失败，请联系客服</p>';
                showSupportReminder();
            }
        }

        // 检查支付状态
        async function checkPaymentStatus() {
            if (!currentOrder) {
                showMessage('订单信息丢失', 'error');
                return;
            }

            const btn = document.getElementById('checkPaymentBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<span class="loading"></span>检查支付状态...';
            btn.disabled = true;

            try {
                const response = await fetch('/Index/Utils/checkPayment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        order_id: currentOrder.order_info.order_id
                    })
                });

                const data = await response.json();

                if (data.status === 'success' && data.data.order_status === 'paid') {
                    // 支付成功，调用注册API
                    await registerVip();
                } else {
                    // 未支付
                    showPaymentError('未检测到支付，请先完成支付');
                }
            } catch (error) {
                console.error('检查支付状态失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
                showSupportReminder();
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // 注册VIP
        async function registerVip() {
            if (!currentOrder || !selectedProduct) {
                showMessage('订单或产品信息丢失', 'error');
                return;
            }

            try {
                const response = await fetch('/Index/Utils/registerVip', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        order_id: currentOrder.order_info.order_id,
                        product_type: currentProduct.type
                    })
                });

                const data = await response.json();

                if (data.code === 200) {
                    // 注册成功
                    showSuccessModal(data);
                    closeModal('qrCodeModal');
                } else if (data.code === 400 && data.message.includes('重复注册')) {
                    showPaymentError('该订单已经处理过，不能重复注册');
                } else {
                    // 注册失败
                    showMessage('注册失败：' + (data.message || '未知错误') + '，请联系客服处理', 'error');
                    showSupportReminder();
                }
            } catch (error) {
                console.error('注册VIP失败:', error);
                showMessage('注册服务异常，请联系客服处理', 'error');
                showSupportReminder();
            }
        }

        // 显示注册成功模态框
        function showSuccessModal(data) {
            document.getElementById('successRegTime').textContent = data.reg_time || '-';
            document.getElementById('successExpireTime').textContent = data.expire_time || '-';
            document.getElementById('successToken').textContent = data.token || '-';

            document.getElementById('successModal').style.display = 'block';
        }

        // 显示支付错误信息
        function showPaymentError(message) {
            const statusDiv = document.getElementById('paymentStatus');
            statusDiv.innerHTML = `
                <div class="status-message status-error">
                    <i class="fas fa-exclamation-circle"></i> ${message}
                </div>
            `;
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const className = type === 'error' ? 'status-error' :
                             type === 'success' ? 'status-success' :
                             type === 'warning' ? 'status-warning' : 'status-info';

            const icon = type === 'error' ? 'fas fa-exclamation-circle' :
                        type === 'success' ? 'fas fa-check-circle' :
                        type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = `status-message ${className}`;
            messageDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
            messageDiv.style.position = 'fixed';
            messageDiv.style.top = '20px';
            messageDiv.style.left = '50%';
            messageDiv.style.transform = 'translateX(-50%)';
            messageDiv.style.zIndex = '10000';
            messageDiv.style.maxWidth = '90%';

            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        // 显示支持提醒
        function showSupportReminder() {
            setTimeout(() => {
                showMessage('会员稳定,24h售后', 'warning');
            }, 1000);
        }

        // 显示安全提醒
        function showSecurityReminder() {
            setTimeout(() => {
                showMessage('会员稳定,24h售后', 'info');
            }, 2000);
        }

        // 复制文本
        function copyText(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showMessage('复制成功', 'success');
                }).catch(() => {
                    fallbackCopyText(text);
                });
            } else {
                fallbackCopyText(text);
            }
        }

        // 备用复制方法
        function fallbackCopyText(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showMessage('复制成功', 'success');
            } catch (err) {
                showMessage('复制失败，请手动复制', 'error');
            }

            document.body.removeChild(textArea);
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';

            // 如果关闭的是二维码模态框，清除定时器
            if (modalId === 'qrCodeModal' && paymentCheckInterval) {
                clearInterval(paymentCheckInterval);
                paymentCheckInterval = null;
            }
        }

        // 返回首页
        function goHome() {
            window.location.href = '/';
        }

        // 倒计时功能
        function startCountdown() {
            // 设置倒计时时间（24小时）
            let countdownTime = 24 * 60 * 60; // 24小时转换为秒

            const countdownElement = document.getElementById('countdown');
            if (!countdownElement) return;

            const timer = setInterval(function() {
                const hours = Math.floor(countdownTime / 3600);
                const minutes = Math.floor((countdownTime % 3600) / 60);
                const seconds = countdownTime % 60;

                const display =
                    (hours < 10 ? '0' + hours : hours) + ':' +
                    (minutes < 10 ? '0' + minutes : minutes) + ':' +
                    (seconds < 10 ? '0' + seconds : seconds);

                countdownElement.textContent = display;

                countdownTime--;

                if (countdownTime < 0) {
                    clearInterval(timer);
                    countdownElement.textContent = '活动已结束';
                }
            }, 1000);
        }

        // 页面加载完成后启动倒计时
        document.addEventListener('DOMContentLoaded', function() {
            startCountdown();
        });

        // 防止点击模态框外部关闭
        document.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    // 不关闭模态框，显示提示
                    showMessage('请使用按钮关闭窗口', 'warning');
                }
            });
        });

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            // 阻止ESC键关闭模态框
            if (event.key === 'Escape') {
                event.preventDefault();
                showMessage('请使用按钮关闭窗口', 'warning');
            }
        });

        // 页面离开前提醒
        window.addEventListener('beforeunload', function(event) {
            if (currentOrder && document.getElementById('qrCodeModal').style.display === 'block') {
                event.preventDefault();
                event.returnValue = '您有未完成的订单，确定要离开吗？';
                return '您有未完成的订单，确定要离开吗？';
            }
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时暂停检查
                if (paymentCheckInterval) {
                    clearInterval(paymentCheckInterval);
                    paymentCheckInterval = null;
                }
            } else {
                // 页面显示时恢复检查（如果有活跃订单）
                if (currentOrder && document.getElementById('qrCodeModal').style.display === 'block') {
                    showMessage('页面已恢复，请检查支付状态', 'info');
                }
            }
        });
    </script>
    <script> var _mtj = _mtj || []; (function () { var mtj = document.createElement("script"); mtj.src = "https://node94.aizhantj.com:21233/tjjs/?k=17jzo46p2e5"; var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(mtj, s); })(); </script>

</body>
</html>