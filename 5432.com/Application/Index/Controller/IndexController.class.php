<?php
namespace Index\Controller;
use Think\Controller;
class IndexController extends Controller {
    public function index(){

    	// 教程视频
    	$video = M("video")->order("sort desc")->select();
		$this->assign("video",$video);

    	// 付费工具：type=1且remen=1（未推荐的精品），显示全部
		$paid_tools = M("app")->where("type=1 AND remen=1")->order("addtime desc")->select();
		$this->assign("mod",$paid_tools);

		// 免费工具：remen=2（推荐的），显示全部
		$free_tools = M("app")->where("remen=2")->order("addtime desc")->select();
		$this->assign("hot",$free_tools);

		// 保留原有的广告数据
		$head = M("adver")->where("type=2")->order("sort desc")->select();
		$this->assign("head",$head);
		$foot = M("adver")->where("type=1")->order("sort desc")->select();
		$this->assign("foot",$foot);
		$con =M("config")->where("id=1")->find();
		$this->assign("con",$con);
		$this->display("index");
    }
    
	    public function type(){
	    
		    $id = $_GET['id'];
		    $mod = M("app")->where("type={$id}")->order("addtime desc")->select();
		    $this->assign("mod",$mod);
		    $type = M("type")->select();
	    	$this->assign("type",$type);
	    	$con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
		    $this->display("type");
	    
	    }
	    
	     public function h5(){
        	$type = M("type")->select();
    	  $this->assign("type",$type);
        	$m=M('type');  
			$m1=M('app');  
			$parent=$m->order('sort DESC')->select();  
			 foreach($parent as $n=> $val){  
				 $id = $val['id'];
				 $parent[$n]['list']=$m1->where("type='{$id}'")->order("addtime DESC")->limit(10)->select();  
			 } 
			 $this->assign("app",$parent);
			 $con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
         $this->display("h5");
    	}
    	
    	public function h5t(){
        	 $id = $_GET['id'];
		    $mod = M("app")->where("type={$id}")->order("addtime desc")->select();
		    $this->assign("mod",$mod);
		    $type = M("type")->select();
	    	$this->assign("type",$type);
	    	$con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
		    $this->display("h5t");
    	}
    	
}