<!DOCTYPE html>

<html>

<head>
  <meta charset="utf-8">
  <title>{$config}总后台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="__PUBLIC__/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="__PUBLIC__/admin/css/admin.min.css" media="all">
</head>

<body class="layui-layout-body">

  <div id="LAY_app">
    <div class="layui-layout layui-layout-admin">
      <div class="layui-header">
        <!-- 头部区域 -->
        <ul class="layui-nav layui-layout-left">
          <li class="layui-nav-item layadmin-flexible" lay-unselect>
            <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
              <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
            </a>
          </li>
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" layadmin-event="refresh" title="刷新">
              <i class="layui-icon layui-icon-refresh-3"></i>
            </a>
          </li>
        </ul>
        <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">

          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;">
              <cite>{$Think.session.adminuser.name}</cite>
            </a>
            <dl class="layui-nav-child">
              <dd>
                <a lay-href="__MODULE__/Admin/add">基本资料</a>
              </dd>
              <dd>
                <a lay-href="__MODULE__/Admin/add">修改密码</a>
              </dd>
			  <dd>
                <a lay-href="__MODULE__/Admin/admin">清理缓存</a>
              </dd>
              <hr>
              <dd style="text-align: center;">
                <a href="{:U('Public/loginOut')}">退出</a>
              </dd>
            </dl>
          </li>

          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;"><i class="layui-icon layui-icon-more-vertical"></i></a>
          </li>
        
        </ul>
      </div>

      <!-- 侧边菜单 -->
      <div class="layui-side layui-side-menu">
    <div class="layui-side-scroll">
        <div class="layui-logo" lay-href="">
            <span>{$config}总后台</span>
        </div>
        <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
            <li data-name="home" class="layui-nav-item layui-this">
                <a href="javascript:;" lay-href="__MODULE__/Index/home" lay-tips="主页" lay-direction="2">
                <i class="layui-icon layui-icon-home"></i>
                <cite>主页</cite>
              </a>
            </li>
			<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="系统设置" lay-direction="2">
                <i class="layui-icon layui-icon-set"></i>
                <cite>系统设置</cite>
              </a>
                <dl class="layui-nav-child">
                
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Config/add">基本设置</a>
                    </dd>
					
                     
                    
					
                </dl>
            </li>
			
			
			
            <li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="广告管理" lay-direction="2">
                <i class="layui-icon layui-icon-carousel"></i>
                <cite>广告管理</cite>
              </a>
                <dl class="layui-nav-child">
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Adver/add">添加广告</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Adver/index">广告列表</a>
                    </dd>
                </dl>
            </li>
            
			<!-- <li data-name="add" class="layui-nav-item"> -->
                <!-- <a href="javascript:;" lay-tips="分类管理" lay-direction="2"> -->
                <!-- <i class="layui-icon layui-icon-group"></i> -->
                <!-- <cite>分类管理</cite> -->
              <!-- </a> -->
                <!-- <dl class="layui-nav-child"> -->
					<!-- <dd> -->
                        <!-- <a href="javascript:;" lay-href="__MODULE__/Type/add">添加分类</a> -->
                    <!-- </dd> -->
					<!-- <dd> -->
                        <!-- <a href="javascript:;" lay-href="__MODULE__/Type/index">分类列表</a> -->
                    <!-- </dd> -->
					
                <!-- </dl> -->
            <!-- </li> -->
			<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="应用管理" lay-direction="2">
                <i class="layui-icon layui-icon-app"></i>
                <cite>应用管理</cite>
              </a>
                <dl class="layui-nav-child">
                
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/App/add">添加应用</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/App/index">应用列表</a>
                    </dd>
					
					
					
                </dl>
            </li>
            
            
            	<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="应用管理" lay-direction="2">
                <i class="layui-icon layui-icon-app"></i>
                <cite>视频管理</cite>
              </a>
                <dl class="layui-nav-child">
                
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Video/add">添加视频</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Video/index">视频列表</a>
                    </dd>
					
					
					
                </dl>
            </li>
            
            
            
            <!-- <li data-name="add" class="layui-nav-item"> -->
                <!-- <a href="javascript:;" lay-tips="友情连接" lay-direction="2"> -->
                <!-- <i class="layui-icon layui-icon-app"></i> -->
                <!-- <cite>友情连接</cite> -->
              <!-- </a> -->
                <!-- <dl class="layui-nav-child"> -->
                
                    <!-- <dd> -->
                        <!-- <a href="javascript:;" lay-href="__MODULE__/Link/add">添加</a> -->
                    <!-- </dd> -->
                    <!-- <dd> -->
                        <!-- <a href="javascript:;" lay-href="__MODULE__/Link/index">查看</a> -->
                    <!-- </dd> -->
					
					
					
                <!-- </dl> -->
            <!-- </li> -->
			
        </ul>
    </div>
</div>

      <!-- 页面标签 -->

      <!-- 主体内容 -->
      <div class="layui-body" id="LAY_app_body">
        <div class="layadmin-tabsbody-item layui-show">
          <iframe src="__MODULE__/Index/home" frameborder="0" class="layadmin-iframe"></iframe>
        </div>
      </div>

      <!-- 辅助元素，一般用于移动设备下遮罩 -->
      <div class="layadmin-body-shade" layadmin-event="shade"></div>
    </div>
  </div>

 <script src="__PUBLIC__/common/lib/layui/layui.js"></script>
 <script type="text/javascript" src="__PUBLIC__/common/lib/jquery/jquery-3.3.1.min.js"></script>
 <script type="text/javascript" src="__PUBLIC__/common/lib/jquery/jquery.cookie.js"></script>
  <script>
    layui.config({
      base: '__PUBLIC__/admin/js/' //静态资源所在路径
    }).extend({
      index: 'lib/index' //主入口模块
    }).use('index');
  </script>
</body>

</html>