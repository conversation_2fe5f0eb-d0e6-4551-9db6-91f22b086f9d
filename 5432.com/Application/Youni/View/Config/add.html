<!DOCTYPE html>

<html>

<head>
  <meta charset="utf-8">
  <title>{$config}总后台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="__PUBLIC__/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="__PUBLIC__/admin/css/admin.min.css" media="all">
   <script src="__PUBLIC__/js/jquery-1.9.1.min.js"></script>
</head>
<style>

.layui-form-item .layui-input-inline {
    float: left;
    width: 315px;
    margin-right: 10px;
}
.layui-form-label{
width:100px;
}
</style>
<body>
<script>
$(function(){
$('.editpic').click(function(){
	$('#file').after('<br/><input type="file" name="picname"/><br/');
})
})
</script>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">基本设置</div>
          <div class="layui-card-body" pad15>
            <form action="__URL__/update" method="POST" enctype="multipart/form-data">
            <div class="layui-form">

			<input type="hidden" name="id" value="1">
              <div class="layui-form-item">
                <label class="layui-form-label">站点名称</label>
                <div class="layui-input-inline">
                  <input type="text" name="webname" autocomplete="off" class="layui-input" value="{$mod.webname}">
                </div>
              </div>
			  
			 <div class="layui-form-item">
                <label class="layui-form-label">网站首页</label>
                <div class="layui-input-inline">
                  <input type="text" name="webtitle" autocomplete="off" class="layui-input" value="{$mod.webtitle}">
                </div>
              </div>
			  <div class="layui-form-item">
                <label class="layui-form-label">网站域名</label>
                <div class="layui-input-inline">
                  <input type="text" name="webkeywords" autocomplete="off" class="layui-input" value="{$mod.webkeywords}">
                </div>
              </div>
			  <div class="layui-form-item">
                <label class="layui-form-label">网站简介</label>
                <div class="layui-input-inline">
                  <input type="text" name="webdescription" autocomplete="off" class="layui-input" value="{$mod.webdescription}">
                </div>
              </div>
			  <div class="layui-form-item">
                <label class="layui-form-label">滚动公告</label>
                <div class="layui-input-inline">
                  <input type="text" name="gdgg" autocomplete="off" class="layui-input" value="{$mod.gdgg}">
                </div>
              </div>
			  <div class="layui-form-item">
                <label class="layui-form-label">底部公告</label>
                <div class="layui-input-inline">
                  <input type="text" name="gonggao" autocomplete="off" class="layui-input" value="{$mod.gonggao}">
                </div>
              </div>
			  
			  
			  
			  
              	
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="card-point-set">保存</button>
                </div>
              </div>
            </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
 <script src="__PUBLIC__/common/lib/layui/layui.js"></script>
 <script type="text/javascript" src="__PUBLIC__/common/lib/jquery/jquery-3.3.1.min.js"></script>
 <script type="text/javascript" src="__PUBLIC__/common/lib/jquery/jquery.cookie.js"></script>
    <script type="text/javascript">
layui.use(['form', 'layedit', 'laydate'], function(){
  var form = layui.form
  ,layer = layui.layer
  ,layedit = layui.layedit
  ,laydate = layui.laydate;

});
</script>
</body>
</html>