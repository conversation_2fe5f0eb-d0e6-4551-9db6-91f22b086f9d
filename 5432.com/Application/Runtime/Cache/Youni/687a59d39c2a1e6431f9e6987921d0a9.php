<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>

<html>

<head>
  <meta charset="utf-8">
  <title><?php echo ($config); ?>总后台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/Public/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/Public/admin/css/admin.min.css" media="all">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">添加</div>
          <div class="layui-card-body" pad15>
            <form action="/Youni/App/insert" method="POST" enctype="multipart/form-data">
            <div class="layui-form">
			<input type="hidden" name="type"  value="1" >
			 <div class="layui-form-item" pane=""> 
				 <label class="layui-form-label">分类</label> 
			 <div class="layui-input-block"> 
			
				  <input type="radio" name="type"  value="1" title="精品">
				  <!--<input type="radio" name="type"  value="2" title="热门">-->
				 </div> 
			   </div> 
              <div class="layui-form-item">
                <label class="layui-form-label">应用名称</label>
                <div class="layui-input-inline">
                  <input type="text" name="title"  autocomplete="off" class="layui-input">
                </div>
              </div>
			  
				<div class="layui-form-item">
                <label class="layui-form-label">广告图片</label>
                <div class="layui-input-block">
                  <input type="file" id="website-title" name="picname" placeholder="图片" class="col-xs-10 ">
                </div>
                
              </div>
             <div class="layui-form-item">
                <label class="layui-form-label">应用地址</label>
                <div class="layui-input-inline">
                  <input type="text" name="link" autocomplete="off" class="layui-input">
                </div>
				
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">应用介绍</label>
                <div class="layui-input-inline">
                  <input type="text" name="content" autocomplete="off" class="layui-input">
                </div>
				
              </div>
			<!--<div class="layui-form-item">-->
   <!--             <label class="layui-form-label">下载次数</label>-->
   <!--             <div class="layui-input-inline">-->
   <!--               <input type="text" name="count" lay-verify="require|number" autocomplete="off" class="layui-input">-->
   <!--             </div>-->
				
   <!--           </div>-->
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="card-point-set">添加</button>
                </div>
              </div>
            </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="/Public/common/lib/layui/layui.js"></script>
 <script type="text/javascript" src="/Public/common/lib/jquery/jquery-3.3.1.min.js"></script>
 <script type="text/javascript" src="/Public/common/lib/jquery/jquery.cookie.js"></script>
  <script>
layui.use(['form', 'layedit', 'laydate'], function(){
  var form = layui.form
  ,layer = layui.layer
  ,layedit = layui.layedit
  ,laydate = layui.laydate;

});
</script>
</body>
</html>