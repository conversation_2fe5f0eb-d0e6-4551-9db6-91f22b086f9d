<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>

<html>

<head>
  <meta charset="utf-8">
  <title><?php echo ($config); ?>总后台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/Public/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/Public/admin/css/admin.min.css" media="all">
</head>
<style>

.layui-form-item .layui-input-inline {
    float: left;
    width: 315px;
    margin-right: 10px;
}
</style>
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">视频添加</div>
          <div class="layui-card-body" pad15>
            <form action="/Youni/Video/insert" method="POST" enctype="multipart/form-data">
            <div class="layui-form">

              <div class="layui-form-item">
                <label class="layui-form-label">视频标题</label>
                <div class="layui-input-inline">
                  <input type="text" name="title" lay-verify="require|number" autocomplete="off" class="layui-input">
                </div>
              </div>
			
              <div class="layui-form-item">
                <label class="layui-form-label">视频地址</label>
                <div class="layui-input-block">
                  <input type="text" placeholder="请输入mp4链接或在下方上传视频" name="picname" lay-verify="require|number" autocomplete="off" class="layui-input" value="<?php echo ($mod["picname"]); ?>">
                </div>
              </div>
				<div class="layui-form-item">
                <label class="layui-form-label">上传视频</label>
                <div class="layui-input-block">
                  <input type="file" id="website-title"  placeholder="视频" class="col-xs-10 ">
                </div>
                
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">视频介绍</label>
                <div class="layui-input-inline">
                  <input type="text" placeholder="简单几个字即可" name="content" lay-verify="require|number" autocomplete="off" class="layui-input"value="<?php echo ($mod["content"]); ?>">
                </div>
				
              </div>
             <div class="layui-form-item">
                <label class="layui-form-label">播放次数</label>
                <div class="layui-input-inline">
                  <input type="text" placeholder="输入数字即可" name="sort" lay-verify="require|number" autocomplete="off" class="layui-input">
                </div>
				<div class="layui-form-mid layui-word-aux">数字越大越排前面</div>
              </div>
			
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="card-point-set">添加</button>
                </div>
              </div>
            </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
 <script src="/Public/common/lib/layui/layui.js"></script>
 <script type="text/javascript" src="/Public/common/lib/jquery/jquery-3.3.1.min.js"></script>
 <script type="text/javascript" src="/Public/common/lib/jquery/jquery.cookie.js"></script>
 <script>
        $(document).ready(function() {
            $("input[type='file']").change(function(e) {
                file_upload(this.files)
            });
            var obj = $('body');
            obj.on('dragenter', function(e) {
                e.stopPropagation();
                e.preventDefault()
            });
            obj.on('dragover', function(e) {
                e.stopPropagation();
                e.preventDefault()
            });
            obj.on('drop', function(e) {
                e.preventDefault();
                file_upload(e.originalEvent.dataTransfer.files)
            });
        });
        function file_upload(files){
            if (files.length == 0) return alert('请选择图片文件！');
            for(var j = 0,len = files.length; j < len; j++){
                console.log(files[j]);
                let imageData = new FormData();
                imageData.append("uploadpic", files[j]);
                $.ajax({
                    url: 'update',
                    type: 'POST',
                    data: imageData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    // 图片上传成功
                    success: function (result) {
                        if (result.code == 1){
                            $('input[name="picname"]').val(result.url);
                        }else{
                            alert('第'+j+'个视频上传失败');
                        }
                    },
                    // 图片上传失败
                    error: function () {
                        console.log('视频上传失败');
                    }
                });
            }
        }
 </script>
</body>
</html>