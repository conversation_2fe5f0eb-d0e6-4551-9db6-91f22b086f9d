<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码生成器 - <?php echo ($con["webname"]); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .input-group {
            margin-bottom: 30px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 10px;
            color: #555;
            font-weight: 500;
        }

        input[type="text"], textarea, select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        input[type="text"]:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .size-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .size-group > div {
            flex: 1;
        }

        .generate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 20px 0;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .generate-btn:active {
            transform: translateY(0);
        }

        .qr-result {
            margin-top: 30px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            display: none;
        }

        .qr-result.show {
            display: block;
        }

        #qrcode {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background-color 0.3s ease;
        }

        .download-btn:hover {
            background: #218838;
        }

        .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background-color 0.3s ease;
        }

        .clear-btn:hover {
            background: #c82333;
        }

        .error-message {
            color: #dc3545;
            margin-top: 10px;
            font-size: 14px;
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2em;
            }

            .size-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>二维码生成器</h1>
        
        <form id="qrForm">
            <div class="input-group">
                <label for="qrText">输入内容：</label>
                <textarea id="qrText" placeholder="请输入要生成二维码的文本、网址或其他内容..." required></textarea>
                <div class="error-message" id="textError">请输入有效内容</div>
            </div>

            <div class="input-group">
                <label for="qrSize">二维码大小：</label>
                <div class="size-group">
                    <div>
                        <select id="qrSize">
                            <option value="200">小 (200x200)</option>
                            <option value="300" selected>中 (300x300)</option>
                            <option value="400">大 (400x400)</option>
                            <option value="500">超大 (500x500)</option>
                        </select>
                    </div>
                    <div>
                        <label for="errorLevel">纠错级别：</label>
                        <select id="errorLevel">
                            <option value="L">低 (L)</option>
                            <option value="M" selected>中 (M)</option>
                            <option value="Q">高 (Q)</option>
                            <option value="H">最高 (H)</option>
                        </select>
                    </div>
                </div>
            </div>

            <button type="submit" class="generate-btn">生成二维码</button>
        </form>

        <div class="qr-result" id="qrResult">
            <h3>生成的二维码：</h3>
            <div id="qrcode"></div>
            <div>
                <button class="download-btn" id="downloadBtn">下载二维码</button>
                <button class="clear-btn" id="clearBtn">清除</button>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    <script>
        class QRCodeGenerator {
            constructor() {
                this.form = document.getElementById('qrForm');
                this.qrResult = document.getElementById('qrResult');
                this.qrCodeContainer = document.getElementById('qrcode');
                this.downloadBtn = document.getElementById('downloadBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.textError = document.getElementById('textError');
                
                this.initEventListeners();
            }

            initEventListeners() {
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));
                this.downloadBtn.addEventListener('click', () => this.downloadQRCode());
                this.clearBtn.addEventListener('click', () => this.clearResult());
                
                // 实时验证
                document.getElementById('qrText').addEventListener('input', () => {
                    this.hideError();
                });
            }

            handleSubmit(e) {
                e.preventDefault();
                
                const text = document.getElementById('qrText').value.trim();
                const size = parseInt(document.getElementById('qrSize').value);
                const errorLevel = document.getElementById('errorLevel').value;

                if (!this.validateInput(text)) {
                    return;
                }

                this.generateQRCode(text, size, errorLevel);
            }

            validateInput(text) {
                if (!text) {
                    this.showError('请输入有效内容');
                    return false;
                }
                
                if (text.length > 2000) {
                    this.showError('内容过长，请控制在2000字符以内');
                    return false;
                }
                
                return true;
            }

            generateQRCode(text, size, errorLevel) {
                try {
                    // 清除之前的二维码
                    this.qrCodeContainer.innerHTML = '';
                    
                    // 创建二维码
                    const qr = qrcode(0, errorLevel);
                    qr.addData(text);
                    qr.make();
                    
                    // 创建canvas元素
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    const moduleCount = qr.getModuleCount();
                    const cellSize = size / moduleCount;
                    
                    canvas.width = size;
                    canvas.height = size;
                    
                    // 设置背景色为白色
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, size, size);
                    
                    // 绘制二维码
                    ctx.fillStyle = '#000000';
                    for (let row = 0; row < moduleCount; row++) {
                        for (let col = 0; col < moduleCount; col++) {
                            if (qr.isDark(row, col)) {
                                ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                            }
                        }
                    }
                    
                    // 添加到容器
                    this.qrCodeContainer.appendChild(canvas);
                    
                    // 显示结果
                    this.qrResult.classList.add('show');
                    
                    // 滚动到结果区域
                    this.qrResult.scrollIntoView({ behavior: 'smooth' });
                    
                } catch (error) {
                    console.error('生成二维码时出错:', error);
                    this.showError('生成二维码失败，请重试');
                }
            }

            downloadQRCode() {
                const canvas = this.qrCodeContainer.querySelector('canvas');
                if (!canvas) {
                    alert('没有可下载的二维码');
                    return;
                }
                
                try {
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.download = `qrcode_${Date.now()}.png`;
                    link.href = canvas.toDataURL('image/png');
                    
                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                } catch (error) {
                    console.error('下载失败:', error);
                    alert('下载失败，请重试');
                }
            }

            clearResult() {
                this.qrResult.classList.remove('show');
                this.qrCodeContainer.innerHTML = '';
                document.getElementById('qrText').value = '';
                this.hideError();
            }

            showError(message) {
                this.textError.textContent = message;
                this.textError.style.display = 'block';
            }

            hideError() {
                this.textError.style.display = 'none';
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new QRCodeGenerator();
        });
    </script>
</body>
</html>