<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员购买测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>会员购买系统测试页面</h1>
    
    <div class="test-section">
        <h2>1. 测试创建订单API</h2>
        <button class="btn" onclick="testCreateOrder('85', 'wxpay')">创建月度会员订单(微信)</button>
        <button class="btn" onclick="testCreateOrder('86', 'alipay')">创建永久会员订单(支付宝)</button>
        <div id="createOrderResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试支付状态检查API</h2>
        <input type="text" id="orderIdInput" placeholder="输入订单号" style="padding: 8px; width: 200px;">
        <button class="btn" onclick="testCheckPayment()">检查支付状态</button>
        <div id="checkPaymentResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 测试会员注册API</h2>
        <input type="text" id="registerOrderId" placeholder="输入订单号" style="padding: 8px; width: 150px;">
        <select id="productType" style="padding: 8px;">
            <option value="1">38天会员</option>
            <option value="2">永久会员</option>
        </select>
        <button class="btn" onclick="testRegisterVip()">注册会员</button>
        <div id="registerResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 访问完整页面</h2>
        <a href="/Index/Utils/index" class="btn" style="text-decoration: none; display: inline-block;">打开会员购买页面</a>
    </div>

    <script>
        let lastOrderId = '';

        async function testCreateOrder(productId, payType) {
            const resultDiv = document.getElementById('createOrderResult');
            resultDiv.textContent = '正在创建订单...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/Index/Utils/createOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        customer_contact: '<EMAIL>',
                        product_id: productId,
                        pay_type: payType
                    })
                });

                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                    lastOrderId = data.data.order_info.order_id;
                    document.getElementById('orderIdInput').value = lastOrderId;
                    document.getElementById('registerOrderId').value = lastOrderId;
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testCheckPayment() {
            const orderId = document.getElementById('orderIdInput').value;
            const resultDiv = document.getElementById('checkPaymentResult');
            
            if (!orderId) {
                resultDiv.textContent = '请输入订单号';
                resultDiv.className = 'result error';
                return;
            }

            resultDiv.textContent = '正在检查支付状态...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/Index/Utils/checkPayment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        order_id: orderId
                    })
                });

                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testRegisterVip() {
            const orderId = document.getElementById('registerOrderId').value;
            const productType = document.getElementById('productType').value;
            const resultDiv = document.getElementById('registerResult');
            
            if (!orderId) {
                resultDiv.textContent = '请输入订单号';
                resultDiv.className = 'result error';
                return;
            }

            resultDiv.textContent = '正在注册会员...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/Index/Utils/registerVip', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        order_id: orderId,
                        product_type: productType
                    })
                });

                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.code === 200) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
