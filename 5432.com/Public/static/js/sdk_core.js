sdkBase = "";

var paramsInfo, ws;
var messageHandler=function(e) {
	if (e.data.op == 'pay') {
		e.data.params.game_id = game_id;
		e.data.params.channel_id = channel_id;
		paramsInfo = e.data.params;
		if (navigator.userAgent.match(/MicroMessenger/i)) {
			alert('请使用浏览器打开游戏,完成充值');
		} else {
			if (getCookie('user_auto_regist') == 1) {
				//处理试玩用户的逻辑
				layer.open({
					type: 1, 
					content: $('#account-login'), 
					skin: 'loginbox',
					closeBtn: 1, 
					shade: 0.3, 
					title: false });
			} else {
				chose_pay(paramsInfo.name, paramsInfo.price);
			}
		}
	}
	else if (e.data.op == 'alipay') {
		ajaxPost(sdkBase + 'pay/alipay', JSON.stringify(paramsInfo), function(data) {
			if (data.msg == 0) {
				$(document.getElementById('paylistframe').contentWindow.document.body).append(data.body);
			} else {
				layer.msg('支付宝支付暂不可用', {icon: 5, offset: 't', shift: 6, shade: 0.8});
			}
		});
	}
	else if (e.data.op == 'wxwap') {
		ajaxPost(sdkBase + 'pay/wxwap', JSON.stringify(paramsInfo), function(data) {
			if (data.msg == 0) {
				$(document.getElementById('paylistframe').contentWindow.document.body).append(data.body);
			} else {
				//layer.msg(data.body, {icon: 5, offset: 't', shift: 6, shade: 0.8});
				layer.msg('微信支付暂不可用', {icon: 5, offset: 't', shift: 6, shade: 0.8});
			}
		});
	}
	else if (e.data.op == 'scan') {
		ajaxPost(sdkBase + 'pay/scan', JSON.stringify(paramsInfo), function(data) {
			if (data.msg == 0) {
				$('#qrcode_div #qrcode').attr('src', data.body.body);
				qrcode_pay();

				if (!ws || ws.readyState == WebSocket.CLOSED) {
							console.log('1');
					ws = new WebSocket("ws://www.80855.net:10000/pay/listen");
					ws.onopen = function() {
							console.log('2');
						ws.send(data.body.checksum);
					};
					ws.onmessage = function(s) {
							console.log('4');
						if (s.data == 'ok') {
							ws.close();
							console.log('支付成功');
							layer.msg('支付成功', {icon: 1, offset: 't', time: 1000, shade: 0.8}, function(){
								layer.close(layer_qrcode);
							});
						}
					};
				} else if(ws.readyState == WebSocket.OPEN) {
							console.log('3');
					ws.send(data.body.checksum);
				}
			} else {
				layer.msg('微信支付暂不可用 '+data.body, {icon: 5, offset: 't', shift: 6, shade: 0.8});
			}
		});
	}
	else if (e.data.op == 'back') {
		$("#paylistframe").hide();
		layer.msg('支付完成', {icon: 1, offset: 't', time: 1000, shade: 0.8});
	}
}

if (typeof window.postMessage != 'undefined') {
	if(window.addEventListener) {
		window.addEventListener('message', messageHandler, false);
	} else if (window.attachEvent) {
		window.attachEvent('onmessage',messageHandler);
	}
}

function shareCallBack(type, status) {
	if ($("#fwgameframe").length > 0) {
		var data = {op: 'share', value: {type: type, status: status}};
		document.getElementById('fwgameframe').contentWindow.postMessage(data,'*');
	}
}

function ajaxPost(url, data, func) {
	var v;
	$.ajax({
		type: 'POST',
		url: url,
		cache: false,
		data: data,
		crossdomain: true,
		contentType: 'text/json; charset=utf-8',
		datatType: "json",
		beforeSend: function() {
			v = layer.load(0, {shade: 0.8});
		},
		success: function(data) {
			func(data);
		},
		error: function() {
			layer.msg('请求状态异常，请联系客服');
		},
		complete: function() {
			layer.close(v);
		}
	});
}
