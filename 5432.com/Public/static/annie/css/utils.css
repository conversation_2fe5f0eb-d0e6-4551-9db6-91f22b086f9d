.YF28-header[data-v-d0b9b47a] {
    margin-top: 3vw;
    padding: 3vw;
    margin-bottom: 1vw;
    border-radius: 10vw 10vw;
    background: linear-gradient(45deg,#2232ca,#e93165);
    position: relative
}

.YF28-header>.YF28-header-main[data-v-d0b9b47a] {
    color: #fff;
    font-weight: 700;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    justify-content: center
}

.td-1[data-v-d0b9b47a] {
    width: 17vw;
    margin-right: 3.5vw;
    position: absolute;
    left: 20vw;
    top: 3vw;
    animation: rotateAnimation-d0b9b47a 4s linear infinite
}

.td-2[data-v-d0b9b47a] {
    position: absolute;
    width: 5vw;
    left: 20%;
    top: 20%;
    animation: rotateAnimation2-d0b9b47a 26s linear infinite
}

@keyframes rotateAnimation-d0b9b47a {
    0% {
        top: 3vw;
        left: 20vw
    }

    50% {
        top: 6vw;
        left: 30vw
    }

    to {
        top: 3vw;
        left: 20vw
    }
}

@keyframes rotateAnimation2-d0b9b47a {
    0% {
        top: 20%;
        left: 15%;
        transform: rotate(0deg)
    }

    25% {
        top: 20%;
        left: 85%;
        transform: rotate(90deg)
    }

    50% {
        top: 50%;
        left: 85%;
        transform: rotate(180deg)
    }

    75% {
        top: 50%;
        left: 15%;
        transform: rotate(270deg)
    }

    to {
        top: 20%;
        left: 15%;
        transform: rotate(1turn)
    }
}

.YF28-header>.YF28-header-main>.text[data-v-d0b9b47a] {
    text-align: center
}

.YF28-header>.YF28-header-main>.text>p[data-v-d0b9b47a]:first-child {
    font-size: 5.8vw;
    font-weight: 700;
    margin: 5vw
}

.YF28-header>.YF28-header-main>.text>p[data-v-d0b9b47a]:nth-child(2) {
    font-size: 3.5vw
}

.YF28-nav[data-v-d0b9b47a] {
    display: flex;
    justify-content: center;
    padding: 3vw;
    padding-bottom: 0;
    margin-top: 3vw;
    margin-bottom: 4.2vw
}

.YF28-nav>a[data-v-d0b9b47a] {
    text-decoration: none;
    color: #fff;
    font-size: 4.2vw;
    padding: 2vw 5vw;
    background-color: #57cc61;
    border-radius: 5vw;
    font-weight: 700
}

.YF28-nav>a[data-v-d0b9b47a]:first-child {
    margin-right: 5vw;
    background-color: #eee;
    color: #3a3064
}

@media screen and (min-width: 1200px) {
    .YF28-header[data-v-d0b9b47a] {
        border-radius:.7vw;
        margin-bottom: 1vw;
        padding: 1.2vw
    }

    .YF28-header>img[data-v-d0b9b47a] {
        width: 6vw;
        margin-right: .5vw
    }
}

html[data-v-7cd14dad] {
    background: #f4f3f9;
    font-family: ui-monospace,Menlo,Monaco,Cascadia Mono,Segoe UI Mono,Roboto Mono,Oxygen Mono,"Ubuntu Monospace",Source Code Pro,Fira Mono,Droid Sans Mono,Courier New,monospace
}

body[data-v-7cd14dad] {
    padding: 0;
    margin: 0
}

.YF28[data-v-7cd14dad] {
    margin: 0 3vw
}

.td-1[data-v-7cd14dad] {
    width: 17vw;
    margin-right: 3.5vw;
    position: absolute;
    left: 20vw;
    top: 3vw;
    animation: rotateAnimation-7cd14dad 4s linear infinite
}

.td-2[data-v-7cd14dad] {
    position: absolute;
    width: 5vw;
    left: 20%;
    top: 20%;
    animation: rotateAnimation2-7cd14dad 26s linear infinite
}

@keyframes rotateAnimation-7cd14dad {
    0% {
        top: 3vw;
        left: 20vw
    }

    50% {
        top: 6vw;
        left: 30vw
    }

    to {
        top: 3vw;
        left: 20vw
    }
}

@keyframes rotateAnimation2-7cd14dad {
    0% {
        top: 20%;
        left: 15%;
        transform: rotate(0deg)
    }

    25% {
        top: 20%;
        left: 85%;
        transform: rotate(90deg)
    }

    50% {
        top: 50%;
        left: 85%;
        transform: rotate(180deg)
    }

    75% {
        top: 50%;
        left: 15%;
        transform: rotate(270deg)
    }

    to {
        top: 20%;
        left: 15%;
        transform: rotate(1turn)
    }
}

p[data-v-7cd14dad] {
    margin: 0
}

.list-box[data-v-7cd14dad] {
    position: relative
}

.new-bac[data-v-7cd14dad] {
    position: absolute;
    width: 38vw;
    left: -10vw;
    top: 15%;
    z-index: -1
}

.new-bac2[data-v-7cd14dad] {
    width: 12vw;
    position: absolute;
    right: -1vw;
    bottom: -15vw;
    animation: rotateAnimation3-7cd14dad 3s linear infinite
}

@keyframes rotateAnimation3-7cd14dad {
    0% {
        bottom: -15vw
    }

    50% {
        bottom: -12vw
    }

    to {
        bottom: -15vw
    }
}

.warp[data-v-7cd14dad] {
    margin-top: 3vw;
    height: 43vh;
    overflow: scroll
}

.YF28-list[data-v-7cd14dad] {
    display: flex;
    flex-wrap: wrap;
    padding: 3vw;
    border-radius: 3vw
}

.item[data-v-7cd14dad] {
    width: 26.5%;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #3a3064;
    text-decoration: none;
    margin-bottom: 3.5vw;
    padding: 3vw;
    font-size: 3.5vw
}

.item>img[data-v-7cd14dad] {
    width: 14vw;
    border-radius: 3.5vw;
    margin-bottom: 2vw;
    box-shadow: 0 1px 3px 0 #948fbc
}

.footer-sj[data-v-7cd14dad] {
    color: #3a3064;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 5vw;
    padding-bottom: 2vw;
    font-size: 3.5vw;
    border-top: .5px solid #938eae
}

.footer-sj>.footer-sj-main[data-v-7cd14dad] {
    width: 90%;
    margin-bottom: 3vw;
    display: flex;
    justify-content: center
}

.footer-sj>.footer-sj-main>a[data-v-7cd14dad] {
    margin: 0 1.5vw
}

.footer-sj>.footer-sj-main>a>img[data-v-7cd14dad] {
    width: 7.9vw
}
