.YF28-header[data-v-d0b9b47a] {
    margin-top: 3vw;
    padding: 3vw;
    margin-bottom: 1vw;
    border-radius: 10vw 10vw;
    background: linear-gradient(45deg,#2232ca,#e93165);
    position: relative
}

.YF28-header>.YF28-header-main[data-v-d0b9b47a] {
    color: #fff;
    font-weight: 700;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    justify-content: center
}

.td-1[data-v-d0b9b47a] {
    width: 17vw;
    margin-right: 3.5vw;
    position: absolute;
    left: 20vw;
    top: 3vw;
    animation: rotateAnimation-d0b9b47a 4s linear infinite
}

.td-2[data-v-d0b9b47a] {
    position: absolute;
    width: 5vw;
    left: 20%;
    top: 20%;
    animation: rotateAnimation2-d0b9b47a 26s linear infinite
}

@keyframes rotateAnimation-d0b9b47a {
    0% {
        top: 3vw;
        left: 20vw
    }

    50% {
        top: 6vw;
        left: 30vw
    }

    to {
        top: 3vw;
        left: 20vw
    }
}

@keyframes rotateAnimation2-d0b9b47a {
    0% {
        top: 20%;
        left: 15%;
        transform: rotate(0deg)
    }

    25% {
        top: 20%;
        left: 85%;
        transform: rotate(90deg)
    }

    50% {
        top: 50%;
        left: 85%;
        transform: rotate(180deg)
    }

    75% {
        top: 50%;
        left: 15%;
        transform: rotate(270deg)
    }

    to {
        top: 20%;
        left: 15%;
        transform: rotate(1turn)
    }
}

.YF28-header>.YF28-header-main>.text[data-v-d0b9b47a] {
    text-align: center
}

.YF28-header>.YF28-header-main>.text>p[data-v-d0b9b47a]:first-child {
    font-size: 5.8vw;
    font-weight: 700;
    margin: 5vw
}

.YF28-header>.YF28-header-main>.text>p[data-v-d0b9b47a]:nth-child(2) {
    font-size: 3.5vw
}

.YF28-nav[data-v-d0b9b47a] {
    display: flex;
    justify-content: center;
    padding: 3vw;
    padding-bottom: 0;
    margin-top: 3vw;
    margin-bottom: 4.2vw
}

.YF28-nav>a[data-v-d0b9b47a] {
    text-decoration: none;
    color: #fff;
    font-size: 4.2vw;
    padding: 2vw 5vw;
    background-color: #57cc61;
    border-radius: 5vw;
    font-weight: 700
}

.YF28-nav>a[data-v-d0b9b47a]:first-child {
    margin-right: 5vw;
    background-color: #eee;
    color: #3a3064
}

@media screen and (min-width: 1200px) {
    .YF28-header[data-v-d0b9b47a] {
        border-radius:.7vw;
        margin-bottom: 1vw;
        padding: 1.2vw
    }

    .YF28-header>img[data-v-d0b9b47a] {
        width: 6vw;
        margin-right: .5vw
    }
}

html[data-v-9bcfc8e6] {
    font-family: ui-monospace,Menlo,Monaco,Cascadia Mono,Segoe UI Mono,Roboto Mono,Oxygen Mono,"Ubuntu Monospace",Source Code Pro,Fira Mono,Droid Sans Mono,Courier New,monospace
}

body[data-v-9bcfc8e6] {
    padding: 0;
    margin: 0
}

.YF28[data-v-9bcfc8e6] {
    margin: 0 3vw
}

p[data-v-9bcfc8e6] {
    margin: 0
}

.YF28-list[data-v-9bcfc8e6] {
    margin: 5vw 0;
    margin-top: 8vw
}

.item[data-v-9bcfc8e6] {
    color: #68778c;
    text-decoration: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 2vw;
    padding: 3vw 5vw;
    margin-bottom: 1vw
}

.YF28-list>.item:first-child>.right-box[data-v-9bcfc8e6] {
    background-color: #2332c9
}

.YF28-list>.item:nth-child(2)>.right-box[data-v-9bcfc8e6] {
    background-color: #ee3163
}

.YF28-list>.item:nth-child(3)>.right-box[data-v-9bcfc8e6] {
    background-color: #f57143
}

.YF28-list>.item:nth-child(4)>.right-box[data-v-9bcfc8e6] {
    background-color: #57cc61
}

.YF28-list>.item:first-child>.right-box>img[data-v-9bcfc8e6],.YF28-list>.item:nth-child(2)>.right-box>img[data-v-9bcfc8e6] {
    width: 13vw
}

.YF28-list>.item:nth-child(3)>.right-box>img[data-v-9bcfc8e6] {
    width: 11vw
}

.YF28-list>.item:nth-child(4)>.right-box>img[data-v-9bcfc8e6] {
    width: 13.9vw
}

.right-box[data-v-9bcfc8e6] {
    width: 35vw;
    height: 20vw;
    position: relative;
    margin-right: 6vw;
    display: flex;
    align-items: center;
    justify-content: center
}

.right-box[data-v-9bcfc8e6]:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 8px;
    left: 8px;
    background: #ccc;
    z-index: -1
}

.item>.left[data-v-9bcfc8e6] {
    display: flex;
    flex-direction: column;
    margin-right: 3.5vw
}

.item>.left>.title[data-v-9bcfc8e6] {
    font-weight: 700;
    font-size: 4.2vw;
    margin-bottom: 1vw
}

.item>.left>.info[data-v-9bcfc8e6] {
    font-size: 3.5vw;
    margin-bottom: .5vw
}

.btn[data-v-9bcfc8e6] {
    background: transparent linear-gradient(240deg,#1d32cc,#ee3163) 0 0 no-repeat padding-box;
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 3.5vw;
    font-weight: 700;
    width: 23vw;
    border-radius: 2vw;
    padding: 1vw .5vw;
    margin-top: 2vw
}

.btn>span[data-v-9bcfc8e6] {
    margin-left: 1vw
}

.btn>img[data-v-9bcfc8e6] {
    width: 3.9vw
}

.btn>img[data-v-9bcfc8e6]:nth-child(3) {
    width: 3.5vw
}

.footer-sj[data-v-9bcfc8e6] {
    color: #3a3064;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 5vw;
    margin-top: 12vw;
    padding-bottom: 2vw;
    font-size: 3.5vw
}

.footer-sj>.footer-sj-main[data-v-9bcfc8e6] {
    width: 90%;
    margin-bottom: 3vw;
    display: flex;
    justify-content: center
}

.footer-sj>.footer-sj-main>a[data-v-9bcfc8e6] {
    margin: 0 1.5vw
}

.footer-sj>.footer-sj-main>a>img[data-v-9bcfc8e6] {
    width: 7.9vw
}

@media screen and (min-width: 1200px) {
    html[data-v-9bcfc8e6] {
        background-color:#35394f
    }

    .YF28[data-v-9bcfc8e6] {
        width: 35%;
        margin: 0 auto;
        background-color: #2d3250
    }

    .YF28-nav[data-v-9bcfc8e6] {
        padding: 1.2vw;
        margin-bottom: 1.6vw
    }

    .YF28-nav>a[data-v-9bcfc8e6] {
        font-size: 1.6vw;
        padding: .7vw 2vw;
        border-radius: .5vw
    }

    .YF28-nav>a[data-v-9bcfc8e6]:first-child {
        margin-right: 2vw
    }

    p[data-v-9bcfc8e6] {
        margin: 0
    }

    .item[data-v-9bcfc8e6] {
        border-radius: .5vw;
        padding: 1.2vw 2vw;
        margin-bottom: 1vw
    }

    .item>img[data-v-9bcfc8e6] {
        width: 6vw;
        border-radius: 1.6vw
    }

    .item>.left[data-v-9bcfc8e6] {
        margin-right: 1.4vw
    }

    .item>.left>.title[data-v-9bcfc8e6] {
        font-size: 1.6vw;
        margin-bottom: 1vw
    }

    .item>.left>.info[data-v-9bcfc8e6] {
        font-size: 1.4vw;
        margin-bottom: .5vw
    }

    .btn[data-v-9bcfc8e6] {
        font-size: 1.4vw;
        width: 10vw;
        border-radius: .5vw;
        padding: 1vw .5vw
    }

    .btn>span[data-v-9bcfc8e6] {
        margin-left: 1vw
    }

    .btn>img[data-v-9bcfc8e6] {
        width: 1.5vw
    }

    .btn>img[data-v-9bcfc8e6]:nth-child(3) {
        width: 1.4vw
    }

    .footer-sj[data-v-9bcfc8e6] {
        font-size: 1.2vw
    }

    .footer-sj>.footer-sj-main[data-v-9bcfc8e6] {
        width: 35%;
        margin-bottom: 1.2vw
    }

    .footer-sj>.footer-sj-main>a[data-v-9bcfc8e6] {
        margin: 0 1.5vw
    }

    .footer-sj>.footer-sj-main>a>img[data-v-9bcfc8e6] {
        width: 3.1vw
    }
}
