<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>API安全代理测试</h1>
    
    <h2>1. 创建订单测试</h2>
    <button onclick="testCreateOrder()">测试创建订单</button>
    <div id="createOrderResult"></div>
    
    <h2>2. 检查支付状态测试</h2>
    <input type="text" id="orderId" placeholder="输入订单号" value="test123">
    <button onclick="testCheckPayment()">测试检查支付</button>
    <div id="checkPaymentResult"></div>
    
    <h2>3. 注册VIP测试</h2>
    <button onclick="testRegisterVip()">测试注册VIP</button>
    <div id="registerVipResult"></div>

    <script>
        function testCreateOrder() {
            const formData = new FormData();
            formData.append('customer_contact', '<EMAIL>');
            formData.append('product_id', '85');
            formData.append('pay_type', 'wxpay');

            fetch('/index.php/Index/Utils/createOrder', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('createOrderResult').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('createOrderResult').innerHTML = 
                    '<span style="color: red;">错误: ' + error.message + '</span>';
            });
        }

        function testCheckPayment() {
            const orderId = document.getElementById('orderId').value;
            const formData = new FormData();
            formData.append('order_id', orderId);

            fetch('/index.php/Index/Utils/checkPayment', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('checkPaymentResult').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('checkPaymentResult').innerHTML = 
                    '<span style="color: red;">错误: ' + error.message + '</span>';
            });
        }

        function testRegisterVip() {
            const formData = new FormData();
            formData.append('order_id', 'test123');
            formData.append('product_type', '1');

            fetch('/index.php/Index/Utils/registerVip', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('registerVipResult').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('registerVipResult').innerHTML = 
                    '<span style="color: red;">错误: ' + error.message + '</span>';
            });
        }
    </script>
</body>
</html>
