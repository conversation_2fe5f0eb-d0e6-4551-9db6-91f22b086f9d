#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在tools目录下所有HTML文件的</body>前面添加统计代码脚本
"""

import os
import re
import glob
from pathlib import Path

# 要添加的JavaScript代码
TRACKING_SCRIPT = '''<script>
var _mtj = _mtj || [];
(function () {
    var mtj = document.createElement("script");
    mtj.src = "https://node94.aizhantj.com:21233/tjjs/?k=17jzo46p2e5";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(mtj, s);
})();
</script>'''

def process_html_file(file_path):
    """
    处理单个HTML文件，在</body>前添加统计代码
    
    Args:
        file_path (str): HTML文件路径
    
    Returns:
        bool: 是否成功处理
    """
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含统计代码
        if 'node94.aizhantj.com:21233/tjjs' in content:
            print(f"跳过 {file_path} - 已包含统计代码")
            return True
        
        # 查找</body>标签
        body_pattern = r'</body>'
        if not re.search(body_pattern, content, re.IGNORECASE):
            print(f"警告: {file_path} 中未找到</body>标签")
            return False
        
        # 在</body>前插入统计代码
        new_content = re.sub(
            body_pattern,
            f'\n{TRACKING_SCRIPT}\n</body>',
            content,
            flags=re.IGNORECASE
        )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 成功处理: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 处理失败 {file_path}: {str(e)}")
        return False

def main():
    """
    主函数：遍历tools目录下所有HTML文件并添加统计代码
    """
    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent
    tools_dir = script_dir / 'tools'
    
    # 检查tools目录是否存在
    if not tools_dir.exists():
        print(f"❌ tools目录不存在: {tools_dir}")
        return
    
    # 查找所有HTML文件
    html_files = list(tools_dir.glob('**/index.html'))
    
    if not html_files:
        print("❌ 在tools目录下未找到任何HTML文件")
        return
    
    print(f"📁 找到 {len(html_files)} 个HTML文件")
    print("=" * 50)
    
    # 统计处理结果
    success_count = 0
    total_count = len(html_files)
    
    # 处理每个HTML文件
    for html_file in html_files:
        if process_html_file(html_file):
            success_count += 1
    
    print("=" * 50)
    print(f"📊 处理完成: {success_count}/{total_count} 个文件成功")
    
    if success_count == total_count:
        print("🎉 所有文件处理成功！")
    else:
        print(f"⚠️  有 {total_count - success_count} 个文件处理失败")

if __name__ == "__main__":
    main()
