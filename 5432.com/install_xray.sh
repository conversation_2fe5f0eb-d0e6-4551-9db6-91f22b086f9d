#!/bin/bash

# Xray代理服务器安装脚本
# 支持多种协议：VMess, VLESS, Trojan, Shadowsocks

set -e

echo "开始安装Xray代理服务器..."

# 检查系统类型
if [[ -f /etc/redhat-release ]]; then
    SYSTEM="centos"
elif cat /etc/issue | grep -Eqi "debian"; then
    SYSTEM="debian"
elif cat /etc/issue | grep -Eqi "ubuntu"; then
    SYSTEM="ubuntu"
else
    echo "不支持的系统类型"
    exit 1
fi

# 更新系统包
echo "更新系统包..."
if [[ $SYSTEM == "centos" ]]; then
    yum update -y
    yum install -y curl wget unzip
elif [[ $SYSTEM == "debian" ]] || [[ $SYSTEM == "ubuntu" ]]; then
    apt update -y
    apt install -y curl wget unzip
fi

# 创建目录
mkdir -p /usr/local/xray
mkdir -p /var/log/xray
mkdir -p /etc/xray

# 下载Xray
echo "下载Xray..."
XRAY_VERSION=$(curl -s https://api.github.com/repos/XTLS/Xray-core/releases/latest | grep tag_name | cut -d '"' -f 4)
if [[ $(uname -m) == "x86_64" ]]; then
    ARCH="64"
elif [[ $(uname -m) == "aarch64" ]]; then
    ARCH="arm64-v8a"
else
    ARCH="32"
fi

wget -O /tmp/xray.zip "https://github.com/XTLS/Xray-core/releases/download/${XRAY_VERSION}/Xray-linux-${ARCH}.zip"

# 解压安装
echo "安装Xray..."
cd /tmp
unzip -o xray.zip
mv xray /usr/local/xray/
chmod +x /usr/local/xray/xray

# 创建systemd服务文件
cat > /etc/systemd/system/xray.service << EOF
[Unit]
Description=Xray Service
Documentation=https://github.com/xtls
After=network.target nss-lookup.target

[Service]
User=nobody
CapabilityBoundingSet=CAP_NET_ADMIN CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_ADMIN CAP_NET_BIND_SERVICE
NoNewPrivileges=true
ExecStart=/usr/local/xray/xray run -config /etc/xray/config.json
Restart=on-failure
RestartPreventExitStatus=23
LimitNPROC=10000
LimitNOFILE=1000000

[Install]
WantedBy=multi-user.target
EOF

# 生成UUID
UUID=$(cat /proc/sys/kernel/random/uuid)
echo "生成的UUID: $UUID"

# 创建基本配置文件
cat > /etc/xray/config.json << EOF
{
  "log": {
    "loglevel": "warning",
    "access": "/var/log/xray/access.log",
    "error": "/var/log/xray/error.log"
  },
  "inbounds": [
    {
      "port": 8080,
      "protocol": "vmess",
      "settings": {
        "clients": [
          {
            "id": "$UUID",
            "level": 1,
            "alterId": 0
          }
        ]
      },
      "streamSettings": {
        "network": "ws",
        "wsSettings": {
          "path": "/ray"
        }
      }
    },
    {
      "port": 8081,
      "protocol": "vless",
      "settings": {
        "clients": [
          {
            "id": "$UUID",
            "level": 0
          }
        ],
        "decryption": "none"
      },
      "streamSettings": {
        "network": "ws",
        "wsSettings": {
          "path": "/vless"
        }
      }
    },
    {
      "port": 8082,
      "protocol": "trojan",
      "settings": {
        "clients": [
          {
            "password": "your_password_here",
            "level": 0
          }
        ]
      },
      "streamSettings": {
        "network": "tcp"
      }
    },
    {
      "port": 8083,
      "protocol": "shadowsocks",
      "settings": {
        "method": "aes-256-gcm",
        "password": "your_ss_password",
        "network": "tcp,udp"
      }
    }
  ],
  "outbounds": [
    {
      "protocol": "freedom",
      "settings": {}
    },
    {
      "protocol": "blackhole",
      "settings": {},
      "tag": "blocked"
    }
  ],
  "routing": {
    "rules": [
      {
        "type": "field",
        "ip": ["geoip:private"],
        "outboundTag": "blocked"
      }
    ]
  }
}
EOF

# 设置权限
chown -R nobody:nobody /var/log/xray
chmod 644 /etc/xray/config.json

# 启用并启动服务
systemctl daemon-reload
systemctl enable xray
systemctl start xray

echo "Xray安装完成！"
echo "UUID: $UUID"
echo "VMess端口: 8080 (WebSocket路径: /ray)"
echo "VLESS端口: 8081 (WebSocket路径: /vless)"
echo "Trojan端口: 8082"
echo "Shadowsocks端口: 8083"
echo ""
echo "请记住这些信息，稍后配置Clash时需要用到。"
echo "可以使用以下命令查看服务状态："
echo "systemctl status xray"