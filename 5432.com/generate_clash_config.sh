#!/bin/bash

# Clash配置文件生成脚本

echo "=== Clash配置文件生成器 ==="
echo ""

# 获取服务器IP
echo "请输入您的服务器IP地址:"
read -p "服务器IP: " SERVER_IP

if [[ -z "$SERVER_IP" ]]; then
    echo "错误: 服务器IP不能为空"
    exit 1
fi

# 获取UUID
echo ""
echo "请输入UUID (从Xray安装脚本输出中获取):"
read -p "UUID: " UUID

if [[ -z "$UUID" ]]; then
    echo "错误: UUID不能为空"
    exit 1
fi

# 获取密码
echo ""
echo "请输入Trojan密码 (默认: your_password_here):"
read -p "Trojan密码: " TROJAN_PASSWORD
TROJAN_PASSWORD=${TROJAN_PASSWORD:-"your_password_here"}

echo ""
echo "请输入Shadowsocks密码 (默认: your_ss_password):"
read -p "SS密码: " SS_PASSWORD
SS_PASSWORD=${SS_PASSWORD:-"your_ss_password"}

# 生成配置文件
echo ""
echo "正在生成Clash配置文件..."

# 复制模板并替换变量
cp clash_config.yaml clash_config_generated.yaml

# 替换变量
sed -i "s/YOUR_SERVER_IP/$SERVER_IP/g" clash_config_generated.yaml
sed -i "s/YOUR_UUID/$UUID/g" clash_config_generated.yaml
sed -i "s/your_password_here/$TROJAN_PASSWORD/g" clash_config_generated.yaml
sed -i "s/your_ss_password/$SS_PASSWORD/g" clash_config_generated.yaml

echo "配置文件已生成: clash_config_generated.yaml"
echo ""
echo "=== 连接信息摘要 ==="
echo "服务器IP: $SERVER_IP"
echo "UUID: $UUID"
echo "VMess端口: 8080 (WebSocket路径: /ray)"
echo "VLESS端口: 8081 (WebSocket路径: /vless)"
echo "Trojan端口: 8082 (密码: $TROJAN_PASSWORD)"
echo "Shadowsocks端口: 8083 (密码: $SS_PASSWORD)"
echo ""
echo "请将 clash_config_generated.yaml 文件导入到您的Clash客户端中。"