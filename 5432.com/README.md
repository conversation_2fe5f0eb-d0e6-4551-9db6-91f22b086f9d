# VPN代理服务器 - Clash配置系统

这是一个完整的VPN代理服务器搭建方案，支持多种协议，可以生成Clash客户端配置文件。

## 🚀 功能特性

- **多协议支持**: VMess, VLESS, Trojan, Shadowsocks
- **WebSocket传输**: 支持WebSocket传输，提高连接稳定性
- **Clash配置**: 自动生成Clash客户端配置文件
- **Web管理界面**: 提供友好的Web管理界面
- **自动化脚本**: 一键安装、配置、管理
- **防火墙配置**: 自动配置防火墙规则
- **系统优化**: 自动优化系统参数提升性能

## 📋 系统要求

- **操作系统**: Ubuntu 18.04+, Debian 9+, CentOS 7+
- **内存**: 最少512MB RAM
- **存储**: 最少1GB可用空间
- **网络**: 公网IP地址
- **权限**: Root权限

## 🛠️ 快速安装

### 1. 下载脚本

```bash
# 克隆或下载所有脚本文件到服务器
# 确保以下文件存在：
# - install_xray.sh
# - manage_proxy.sh
# - setup_firewall.sh
# - generate_clash_config.sh
# - clash_config.yaml
# - web_panel/index.html
```

### 2. 设置权限

```bash
chmod +x *.sh
```

### 3. 运行管理脚本

```bash
sudo ./manage_proxy.sh
```

## 📖 详细使用说明

### 方法一：使用管理脚本（推荐）

运行管理脚本，按照菜单提示操作：

```bash
sudo ./manage_proxy.sh
```

菜单选项：
1. **安装Xray代理服务器** - 自动安装和配置Xray
2. **启动代理服务** - 启动所有代理服务
3. **停止代理服务** - 停止所有代理服务
4. **重启代理服务** - 重启所有代理服务
5. **查看服务状态** - 查看服务运行状态和端口监听情况
6. **查看服务日志** - 查看详细的服务日志
7. **生成Clash配置** - 交互式生成Clash配置文件
8. **配置防火墙** - 自动配置防火墙规则
9. **卸载服务** - 完全卸载代理服务

### 方法二：手动安装

#### 步骤1：安装Xray

```bash
sudo ./install_xray.sh
```

安装完成后，记录显示的UUID，后续配置需要用到。

#### 步骤2：配置防火墙

```bash
sudo ./setup_firewall.sh
```

#### 步骤3：生成Clash配置

```bash
./generate_clash_config.sh
```

## 🌐 Web管理界面

启动Web服务器来使用管理界面：

```bash
# 使用Python启动简单HTTP服务器
cd web_panel
python3 -m http.server 8000

# 或使用PHP
php -S 0.0.0.0:8000
```

然后在浏览器中访问：`http://your-server-ip:8000`

## 🔧 配置说明

### 代理协议和端口

| 协议 | 端口 | 传输方式 | 路径 |
|------|------|----------|------|
| VMess | 8080 | WebSocket | /ray |
| VLESS | 8081 | WebSocket | /vless |
| Trojan | 8082 | TCP | - |
| Shadowsocks | 8083 | TCP/UDP | - |

### 默认密码

- **Trojan密码**: `your_password_here`
- **Shadowsocks密码**: `your_ss_password`

⚠️ **安全提示**: 安装后请立即修改默认密码！

## 📱 Clash客户端配置

### 1. 获取配置文件

- 使用Web界面生成配置文件
- 或运行 `./generate_clash_config.sh` 脚本
- 配置文件将保存为 `clash_config_generated.yaml`

### 2. 导入Clash客户端

1. 打开Clash客户端
2. 导入配置文件或订阅链接
3. 选择代理节点
4. 开始使用

### 3. 推荐的Clash客户端

- **Windows**: Clash for Windows
- **macOS**: ClashX
- **Android**: Clash for Android
- **iOS**: Shadowrocket, Quantumult X

## 🔍 故障排除

### 服务无法启动

```bash
# 查看服务状态
sudo systemctl status xray

# 查看详细日志
sudo journalctl -u xray -f

# 检查配置文件
sudo /usr/local/xray/xray -test -config /etc/xray/config.json
```

### 端口无法访问

```bash
# 检查端口监听
sudo netstat -tlnp | grep -E "(8080|8081|8082|8083)"

# 检查防火墙状态
sudo ufw status  # Ubuntu/Debian
sudo firewall-cmd --list-all  # CentOS/RHEL
```

### 连接失败

1. 确认服务器IP地址正确
2. 确认端口已开放
3. 确认UUID和密码正确
4. 检查客户端配置

## 🛡️ 安全建议

1. **修改默认密码**: 安装后立即修改Trojan和Shadowsocks密码
2. **定期更新**: 定期更新Xray到最新版本
3. **监控日志**: 定期检查访问日志，发现异常及时处理
4. **限制访问**: 如果可能，限制特定IP访问
5. **使用HTTPS**: 如果有域名，建议配置TLS证书

## 📊 性能优化

系统已自动应用以下优化：

- TCP BBR拥塞控制算法
- 网络缓冲区优化
- 文件描述符限制调整
- TCP参数优化

## 🔄 更新和维护

### 更新Xray

```bash
# 下载最新版本
wget -O /tmp/xray.zip "https://github.com/XTLS/Xray-core/releases/latest/download/Xray-linux-64.zip"
cd /tmp && unzip -o xray.zip
sudo systemctl stop xray
sudo mv xray /usr/local/xray/
sudo chmod +x /usr/local/xray/xray
sudo systemctl start xray
```

### 备份配置

```bash
# 备份配置文件
sudo cp /etc/xray/config.json /etc/xray/config.json.backup
```

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志：`sudo journalctl -u xray -n 50`
3. 确认网络连接和防火墙设置
4. 验证配置文件语法

## 📄 许可证

本项目仅供学习和研究使用，请遵守当地法律法规。

## ⚠️ 免责声明

- 本工具仅供技术学习和研究使用
- 用户需遵守当地法律法规
- 作者不承担任何法律责任
- 请合理使用，不要用于非法用途

---

**祝您使用愉快！** 🎉