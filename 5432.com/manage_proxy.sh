#!/bin/bash

# VPN代理服务管理脚本

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示菜单
show_menu() {
    clear
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}    VPN代理服务管理脚本${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 安装Xray代理服务器"
    echo -e "${GREEN}2.${NC} 启动代理服务"
    echo -e "${GREEN}3.${NC} 停止代理服务"
    echo -e "${GREEN}4.${NC} 重启代理服务"
    echo -e "${GREEN}5.${NC} 查看服务状态"
    echo -e "${GREEN}6.${NC} 查看服务日志"
    echo -e "${GREEN}7.${NC} 生成Clash配置"
    echo -e "${GREEN}8.${NC} 配置防火墙"
    echo -e "${GREEN}9.${NC} 卸载服务"
    echo -e "${GREEN}0.${NC} 退出"
    echo ""
    echo -e "${YELLOW}请选择操作 [0-9]:${NC}"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}错误: 此脚本需要root权限运行${NC}"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 安装Xray
install_xray() {
    echo -e "${BLUE}开始安装Xray代理服务器...${NC}"
    if [[ -f "./install_xray.sh" ]]; then
        chmod +x ./install_xray.sh
        ./install_xray.sh
    else
        echo -e "${RED}错误: 找不到install_xray.sh文件${NC}"
        return 1
    fi
}

# 启动服务
start_service() {
    echo -e "${BLUE}启动Xray服务...${NC}"
    systemctl start xray
    if systemctl is-active --quiet xray; then
        echo -e "${GREEN}✓ Xray服务启动成功${NC}"
    else
        echo -e "${RED}✗ Xray服务启动失败${NC}"
    fi
}

# 停止服务
stop_service() {
    echo -e "${BLUE}停止Xray服务...${NC}"
    systemctl stop xray
    if ! systemctl is-active --quiet xray; then
        echo -e "${GREEN}✓ Xray服务已停止${NC}"
    else
        echo -e "${RED}✗ Xray服务停止失败${NC}"
    fi
}

# 重启服务
restart_service() {
    echo -e "${BLUE}重启Xray服务...${NC}"
    systemctl restart xray
    if systemctl is-active --quiet xray; then
        echo -e "${GREEN}✓ Xray服务重启成功${NC}"
    else
        echo -e "${RED}✗ Xray服务重启失败${NC}"
    fi
}

# 查看服务状态
check_status() {
    echo -e "${BLUE}=== 服务状态 ===${NC}"
    echo ""

    # Xray服务状态
    if systemctl is-active --quiet xray; then
        echo -e "Xray服务: ${GREEN}运行中${NC}"
    else
        echo -e "Xray服务: ${RED}已停止${NC}"
    fi

    # 端口监听状态
    echo ""
    echo -e "${BLUE}=== 端口监听状态 ===${NC}"
    for port in 8080 8081 8082 8083; do
        if netstat -tlnp | grep ":$port " > /dev/null 2>&1; then
            echo -e "端口 $port: ${GREEN}监听中${NC}"
        else
            echo -e "端口 $port: ${RED}未监听${NC}"
        fi
    done

    # 系统资源使用
    echo ""
    echo -e "${BLUE}=== 系统资源 ===${NC}"
    echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')%"
    echo "内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
    echo "磁盘使用: $(df -h / | awk 'NR==2{print $5}')"
}

# 查看日志
view_logs() {
    echo -e "${BLUE}=== Xray服务日志 ===${NC}"
    echo ""
    echo -e "${YELLOW}最近20条系统日志:${NC}"
    journalctl -u xray -n 20 --no-pager

    echo ""
    echo -e "${YELLOW}访问日志 (最后10行):${NC}"
    if [[ -f "/var/log/xray/access.log" ]]; then
        tail -n 10 /var/log/xray/access.log
    else
        echo "访问日志文件不存在"
    fi

    echo ""
    echo -e "${YELLOW}错误日志 (最后10行):${NC}"
    if [[ -f "/var/log/xray/error.log" ]]; then
        tail -n 10 /var/log/xray/error.log
    else
        echo "错误日志文件不存在"
    fi
}

# 生成Clash配置
generate_clash() {
    echo -e "${BLUE}生成Clash配置文件...${NC}"
    if [[ -f "./generate_clash_config.sh" ]]; then
        chmod +x ./generate_clash_config.sh
        ./generate_clash_config.sh
    else
        echo -e "${RED}错误: 找不到generate_clash_config.sh文件${NC}"
        return 1
    fi
}

# 配置防火墙
configure_firewall() {
    echo -e "${BLUE}配置防火墙规则...${NC}"

    # 检测防火墙类型
    if command -v ufw >/dev/null 2>&1; then
        echo "检测到UFW防火墙"
        ufw allow 8080/tcp
        ufw allow 8081/tcp
        ufw allow 8082/tcp
        ufw allow 8083/tcp
        echo -e "${GREEN}✓ UFW规则已添加${NC}"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        echo "检测到firewalld防火墙"
        firewall-cmd --permanent --add-port=8080/tcp
        firewall-cmd --permanent --add-port=8081/tcp
        firewall-cmd --permanent --add-port=8082/tcp
        firewall-cmd --permanent --add-port=8083/tcp
        firewall-cmd --reload
        echo -e "${GREEN}✓ firewalld规则已添加${NC}"
    elif command -v iptables >/dev/null 2>&1; then
        echo "使用iptables配置防火墙"
        iptables -I INPUT -p tcp --dport 8080 -j ACCEPT
        iptables -I INPUT -p tcp --dport 8081 -j ACCEPT
        iptables -I INPUT -p tcp --dport 8082 -j ACCEPT
        iptables -I INPUT -p tcp --dport 8083 -j ACCEPT
        # 保存规则
        if command -v iptables-save >/dev/null 2>&1; then
            iptables-save > /etc/iptables/rules.v4 2>/dev/null || iptables-save > /etc/iptables.rules 2>/dev/null
        fi
        echo -e "${GREEN}✓ iptables规则已添加${NC}"
    else
        echo -e "${YELLOW}未检测到防火墙，请手动开放端口: 8080, 8081, 8082, 8083${NC}"
    fi
}

# 卸载服务
uninstall_service() {
    echo -e "${RED}警告: 这将完全卸载Xray服务和相关文件${NC}"
    read -p "确定要继续吗? (y/N): " confirm

    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}卸载Xray服务...${NC}"

        # 停止并禁用服务
        systemctl stop xray 2>/dev/null
        systemctl disable xray 2>/dev/null

        # 删除服务文件
        rm -f /etc/systemd/system/xray.service
        systemctl daemon-reload

        # 删除程序文件
        rm -rf /usr/local/xray
        rm -rf /etc/xray
        rm -rf /var/log/xray

        echo -e "${GREEN}✓ Xray服务已完全卸载${NC}"
    else
        echo "取消卸载"
    fi
}

# 主循环
main() {
    check_root

    while true; do
        show_menu
        read -r choice

        case $choice in
            1)
                install_xray
                ;;
            2)
                start_service
                ;;
            3)
                stop_service
                ;;
            4)
                restart_service
                ;;
            5)
                check_status
                ;;
            6)
                view_logs
                ;;
            7)
                generate_clash
                ;;
            8)
                configure_firewall
                ;;
            9)
                uninstall_service
                ;;
            0)
                echo -e "${GREEN}退出程序${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选择，请重新输入${NC}"
                ;;
        esac

        echo ""
        read -p "按回车键继续..."
    done
}

# 运行主程序
main "$@"