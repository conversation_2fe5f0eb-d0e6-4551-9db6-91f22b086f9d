<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN代理管理面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #4facfe;
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-top: 3px solid #4facfe;
        }

        .status-card h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online {
            background-color: #28a745;
        }

        .status-offline {
            background-color: #dc3545;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        }

        .config-section {
            background: #fff;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .config-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 VPN代理管理面板</h1>
            <p>Clash代理服务器管理控制台</p>
        </div>

        <div class="content">
            <!-- 服务状态 -->
            <div class="card">
                <h3>📊 服务状态</h3>
                <div class="status-grid">
                    <div class="status-card">
                        <h4>Xray服务</h4>
                        <span class="status-indicator status-online"></span>
                        <span id="xray-status">运行中</span>
                        <br><br>
                        <button class="btn btn-success" onclick="startService('xray')">启动</button>
                        <button class="btn btn-danger" onclick="stopService('xray')">停止</button>
                        <button class="btn" onclick="restartService('xray')">重启</button>
                    </div>

                    <div class="status-card">
                        <h4>VMess (8080)</h4>
                        <span class="status-indicator status-online"></span>
                        <span>WebSocket</span>
                    </div>

                    <div class="status-card">
                        <h4>VLESS (8081)</h4>
                        <span class="status-indicator status-online"></span>
                        <span>WebSocket</span>
                    </div>

                    <div class="status-card">
                        <h4>Trojan (8082)</h4>
                        <span class="status-indicator status-online"></span>
                        <span>TCP</span>
                    </div>

                    <div class="status-card">
                        <h4>Shadowsocks (8083)</h4>
                        <span class="status-indicator status-online"></span>
                        <span>AEAD</span>
                    </div>
                </div>
            </div>

            <!-- Clash配置生成器 -->
            <div class="card">
                <h3>⚙️ Clash配置生成器</h3>
                <div class="config-section">
                    <div class="alert alert-info">
                        <strong>提示:</strong> 请填写您的服务器信息来生成Clash配置文件
                    </div>

                    <div class="form-group">
                        <label for="server-ip">服务器IP地址:</label>
                        <input type="text" id="server-ip" placeholder="例如: *************">
                    </div>

                    <div class="form-group">
                        <label for="uuid">UUID:</label>
                        <input type="text" id="uuid" placeholder="从安装脚本获取的UUID">
                    </div>

                    <div class="form-group">
                        <label for="trojan-password">Trojan密码:</label>
                        <input type="text" id="trojan-password" placeholder="your_password_here">
                    </div>

                    <div class="form-group">
                        <label for="ss-password">Shadowsocks密码:</label>
                        <input type="text" id="ss-password" placeholder="your_ss_password">
                    </div>

                    <button class="btn" onclick="generateConfig()">生成配置文件</button>
                    <button class="btn btn-success" onclick="downloadConfig()">下载配置</button>
                </div>
            </div>

            <!-- 配置文件预览 -->
            <div class="card" id="config-preview" style="display: none;">
                <h3>📄 配置文件预览</h3>
                <div class="alert alert-success">
                    配置文件已生成！您可以复制以下内容或点击下载按钮。
                </div>
                <div class="config-display" id="config-content"></div>
            </div>

            <!-- 使用说明 -->
            <div class="card">
                <h3>📖 使用说明</h3>
                <div style="line-height: 1.8;">
                    <h4>1. 安装代理服务器</h4>
                    <p>运行安装脚本: <code>sudo bash install_xray.sh</code></p>

                    <h4>2. 生成Clash配置</h4>
                    <p>在上方表单中填入服务器信息，点击"生成配置文件"</p>

                    <h4>3. 导入Clash客户端</h4>
                    <p>将生成的配置文件导入到Clash客户端中</p>

                    <h4>4. 支持的协议</h4>
                    <ul style="margin-left: 20px;">
                        <li><strong>VMess:</strong> 端口8080，WebSocket传输</li>
                        <li><strong>VLESS:</strong> 端口8081，WebSocket传输</li>
                        <li><strong>Trojan:</strong> 端口8082，TCP传输</li>
                        <li><strong>Shadowsocks:</strong> 端口8083，AEAD加密</li>
                    </ul>

                    <h4>5. 防火墙设置</h4>
                    <p>确保开放端口: 8080, 8081, 8082, 8083</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 生成配置文件
        function generateConfig() {
            const serverIp = document.getElementById('server-ip').value;
            const uuid = document.getElementById('uuid').value;
            const trojanPassword = document.getElementById('trojan-password').value || 'your_password_here';
            const ssPassword = document.getElementById('ss-password').value || 'your_ss_password';

            if (!serverIp || !uuid) {
                alert('请填写服务器IP和UUID');
                return;
            }

            const config = generateClashConfig(serverIp, uuid, trojanPassword, ssPassword);
            document.getElementById('config-content').textContent = config;
            document.getElementById('config-preview').style.display = 'block';

            // 保存到全局变量供下载使用
            window.generatedConfig = config;
        }

        // 下载配置文件
        function downloadConfig() {
            if (!window.generatedConfig) {
                alert('请先生成配置文件');
                return;
            }

            const blob = new Blob([window.generatedConfig], { type: 'text/yaml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'clash_config.yaml';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 生成Clash配置内容
        function generateClashConfig(serverIp, uuid, trojanPassword, ssPassword) {
            return `# Clash配置文件 - 自动生成
port: 7890
socks-port: 7891
allow-lan: true
mode: rule
log-level: info
external-controller: 127.0.0.1:9090

dns:
  enable: true
  ipv6: false
  listen: 0.0.0.0:53
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  nameserver:
    - *********
    - ***************
    - *******
  fallback:
    - tls://dns.rubyfish.cn:853
    - tls://*******:853
    - tls://dns.google:853

proxies:
  # VMess配置
  - name: "VMess-WS"
    type: vmess
    server: ${serverIp}
    port: 8080
    uuid: ${uuid}
    alterId: 0
    cipher: auto
    network: ws
    ws-opts:
      path: /ray
      headers:
        Host: ${serverIp}

  # VLESS配置
  - name: "VLESS-WS"
    type: vless
    server: ${serverIp}
    port: 8081
    uuid: ${uuid}
    network: ws
    ws-opts:
      path: /vless
      headers:
        Host: ${serverIp}

  # Trojan配置
  - name: "Trojan-TCP"
    type: trojan
    server: ${serverIp}
    port: 8082
    password: ${trojanPassword}

  # Shadowsocks配置
  - name: "SS-AEAD"
    type: ss
    server: ${serverIp}
    port: 8083
    cipher: aes-256-gcm
    password: ${ssPassword}

proxy-groups:
  - name: "🚀 节点选择"
    type: select
    proxies:
      - "♻️ 自动选择"
      - "🎯 全球直连"
      - "VMess-WS"
      - "VLESS-WS"
      - "Trojan-TCP"
      - "SS-AEAD"

  - name: "♻️ 自动选择"
    type: url-test
    proxies:
      - "VMess-WS"
      - "VLESS-WS"
      - "Trojan-TCP"
      - "SS-AEAD"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

  - name: "🎯 全球直连"
    type: select
    proxies:
      - "DIRECT"

  - name: "🛑 全球拦截"
    type: select
    proxies:
      - "REJECT"

  - name: "🐟 漏网之鱼"
    type: select
    proxies:
      - "🚀 节点选择"
      - "🎯 全球直连"

rules:
  # 本地网络直连
  - DOMAIN-SUFFIX,local,🎯 全球直连
  - IP-CIDR,*********/8,🎯 全球直连
  - IP-CIDR,**********/12,🎯 全球直连
  - IP-CIDR,***********/16,🎯 全球直连
  - IP-CIDR,10.0.0.0/8,🎯 全球直连
  - IP-CIDR,********/8,🎯 全球直连
  - IP-CIDR,**********/10,🎯 全球直连

  # 常见广告域名拦截
  - DOMAIN-SUFFIX,googlesyndication.com,🛑 全球拦截
  - DOMAIN-SUFFIX,googleadservices.com,🛑 全球拦截
  - DOMAIN-SUFFIX,doubleclick.net,🛑 全球拦截

  # 国外常用服务走代理
  - DOMAIN-SUFFIX,google.com,🚀 节点选择
  - DOMAIN-SUFFIX,youtube.com,🚀 节点选择
  - DOMAIN-SUFFIX,facebook.com,🚀 节点选择
  - DOMAIN-SUFFIX,twitter.com,🚀 节点选择
  - DOMAIN-SUFFIX,instagram.com,🚀 节点选择
  - DOMAIN-SUFFIX,telegram.org,🚀 节点选择
  - DOMAIN-SUFFIX,github.com,🚀 节点选择

  # 国内服务直连
  - DOMAIN-SUFFIX,baidu.com,🎯 全球直连
  - DOMAIN-SUFFIX,qq.com,🎯 全球直连
  - DOMAIN-SUFFIX,taobao.com,🎯 全球直连
  - DOMAIN-SUFFIX,alipay.com,🎯 全球直连
  - DOMAIN-SUFFIX,weibo.com,🎯 全球直连

  # 中国IP直连
  - GEOIP,CN,🎯 全球直连

  # 其他流量
  - MATCH,🐟 漏网之鱼`;
        }

        // 服务管理函数
        function startService(service) {
            alert('启动' + service + '服务 (需要在服务器上执行: systemctl start ' + service + ')');
        }

        function stopService(service) {
            alert('停止' + service + '服务 (需要在服务器上执行: systemctl stop ' + service + ')');
        }

        function restartService(service) {
            alert('重启' + service + '服务 (需要在服务器上执行: systemctl restart ' + service + ')');
        }
    </script>
</body>
</html>