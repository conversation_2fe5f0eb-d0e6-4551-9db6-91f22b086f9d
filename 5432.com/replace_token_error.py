#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量替换tools目录下所有文件中的tokenError div内容
"""

import os
import glob
import re
from pathlib import Path

def replace_token_error_in_file(file_path):
    """
    在指定文件中替换tokenError div内容
    
    Args:
        file_path (str): 文件路径
    
    Returns:
        bool: 是否进行了替换
    """
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 定义要查找的原始字符串
        old_string = '<div class="error" id="tokenError"></div>'
        
        # 定义要替换成的新字符串
        new_string = '''<div class="error" id="tokenError"></div> 
            <a href="/Index/utils/index" style="color: red; font-weight: bold; font-size: 0.8rem; text-decoration: none;">
                    没有Token?点我购买
                </a>'''
        
        # 检查是否包含要替换的内容
        if old_string in content:
            # 进行替换
            new_content = content.replace(old_string, new_string)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 已替换: {file_path}")
            return True
        else:
            print(f"⚪ 未找到目标内容: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 处理文件出错 {file_path}: {str(e)}")
        return False

def main():
    """
    主函数：遍历tools目录下的所有文件并进行替换
    """
    print("开始批量替换tokenError div内容...")
    print("=" * 50)
    
    # 获取tools目录的路径
    tools_dir = Path("tools")
    
    if not tools_dir.exists():
        print("❌ tools目录不存在！")
        return
    
    # 统计变量
    total_files = 0
    replaced_files = 0
    
    # 遍历tools目录下的所有子目录
    for subdir in tools_dir.iterdir():
        if subdir.is_dir():
            print(f"\n📁 处理目录: {subdir.name}")
            
            # 遍历子目录中的所有文件
            for file_path in subdir.rglob("*"):
                if file_path.is_file():
                    total_files += 1
                    
                    # 尝试替换文件内容
                    if replace_token_error_in_file(str(file_path)):
                        replaced_files += 1
    
    # 输出统计结果
    print("\n" + "=" * 50)
    print(f"处理完成！")
    print(f"总文件数: {total_files}")
    print(f"替换文件数: {replaced_files}")
    print(f"未替换文件数: {total_files - replaced_files}")

if __name__ == "__main__":
    main()
