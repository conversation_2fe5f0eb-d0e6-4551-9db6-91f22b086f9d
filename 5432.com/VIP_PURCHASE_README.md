# 会员购买系统使用说明

## 系统概述

这是一个完整的会员购买系统，支持微信支付和支付宝支付，具有以下特点：

- 🎨 **像素风格设计** - 蓝色科技感配色，支持多端自适应
- 💳 **多种支付方式** - 支持微信支付和支付宝
- 🔒 **安全防护** - 防重复注册、频率限制、安全检查
- 📱 **二维码支付** - 自动生成支付二维码
- 🎯 **用户友好** - 完整的错误处理和客服支持

## 访问地址

- **会员购买页面**: http://103.216.175.76:1235/Index/utils/index
- **API调试页面**: http://103.216.175.76:1235/Index/utils/testApi

## 产品套餐

### 月度会员 (¥28.05)
- 商品ID: 85
- 产品类型: 1
- 使用期限: 38天 (买一月送一周)
- 全功能无限制使用

### 永久会员 (¥38.05)
- 商品ID: 86
- 产品类型: 2
- 使用期限: 永久
- 终身使用，永不到期

## 购买流程

1. **选择套餐** - 点击选择月度会员或永久会员
2. **选择支付方式** - 微信支付或支付宝
3. **扫码支付** - 使用手机扫描二维码完成支付
4. **验证支付** - 点击"我已支付"按钮检查支付状态
5. **获取卡密** - 支付成功后自动注册并获得Token(卡密)

## API接口

### 1. 创建订单
- **接口**: `/Index/Utils/createOrder`
- **方法**: POST
- **参数**:
  - `customer_contact`: 客户联系方式
  - `product_id`: 商品ID (85或86)
  - `pay_type`: 支付方式 (wxpay或alipay)

### 2. 检查支付状态
- **接口**: `/Index/Utils/checkPayment`
- **方法**: POST
- **参数**:
  - `order_id`: 订单号

### 3. 注册会员
- **接口**: `/Index/Utils/registerVip`
- **方法**: POST
- **参数**:
  - `order_id`: 订单号
  - `product_type`: 产品类型 (1或2)

## 安全特性

### 防重复注册
- 每个订单只能注册一次
- 数据库记录防止重复处理

### 频率限制
- 基于IP的请求频率限制
- 5分钟内最多20次请求

### 安全检查
- 请求来源验证
- User-Agent检查
- 简单的机器人防护

## 数据库表结构

系统使用 `nav_order_status` 表来跟踪订单状态：

```sql
- id: 主键
- order_id: 订单号 (唯一)
- product_type: 产品类型
- register_status: 注册状态 (pending/success/failed)
- vip_token: 生成的会员Token
- reg_time: 注册时间
- expire_time: 到期时间
- client_ip: 客户端IP
- processed_count: 处理次数
- last_check_time: 最后检查时间
```

## 客服支持

如遇到任何问题，请优先联系客服：

- **Telegram客服**: https://t.me/Dataso
- **投诉地址**: https://cloudshop.qnm6.top/tousu.html

⚠️ **重要提醒**: 请避免直接投诉到支付平台，优先通过客服渠道解决问题。

## 技术特点

### 前端特性
- 响应式设计，支持手机和桌面端
- 像素风格UI，蓝色科技感配色
- 模态框交互，用户体验友好
- 二维码自动生成和显示
- 复制功能，方便用户保存信息

### 后端特性
- ThinkPHP框架
- 代理API设计，保护上游接口
- 完整的错误处理和日志记录
- 数据库事务处理
- HTTP请求封装 (cURL + file_get_contents备用)

### 安全措施
- 参数验证和过滤
- SQL注入防护
- XSS防护
- CSRF防护
- 频率限制

## 维护说明

### 日志文件
- API调用日志: `Application/Runtime/Logs/api_calls_YYYY-MM-DD.log`
- 系统日志: ThinkPHP默认日志目录

### 数据库维护
- 定期清理过期的订单记录
- 监控数据库性能
- 备份重要数据

### 监控建议
- 监控API响应时间
- 监控支付成功率
- 监控注册成功率
- 监控错误日志

## 故障排除

### 常见问题
1. **订单创建失败** - 检查上游API连接
2. **支付状态检查失败** - 验证订单号格式
3. **注册失败** - 检查会员注册API
4. **二维码不显示** - 检查JavaScript库加载

### 调试工具
访问 `/Index/Utils/testApi` 可以进行系统诊断和API测试。

---

**开发完成时间**: 2025-08-21  
**版本**: v1.0  
**技术栈**: ThinkPHP + MySQL + JavaScript + CSS3
