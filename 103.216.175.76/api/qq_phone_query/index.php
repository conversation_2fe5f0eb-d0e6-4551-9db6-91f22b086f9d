<?php
/**
 * QQ绑定Phone查询API - VIP功能
 * 根据QQ号查询绑定的手机号信息
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 记录开始时间
$startTime = microtime(true);

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $qq = $_GET['qq'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $qq = $input['qq'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($qq)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少qq参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证QQ号格式（5-11位数字）
    if (!preg_match('/^\d{5,11}$/', $qq)) {
        echo json_encode([
            'code' => 400,
            'message' => 'QQ号格式不正确，请输入5-11位数字'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'https://api.qnm6.top/api/qq/';
    $params = [
        'token' => $token,
        'qq' => $qq
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用cURL进行更可靠的HTTP请求
    $response = makeHttpRequest($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => 'QQ绑定Phone查询服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => 'QQ绑定Phone查询结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 计算执行时间
    $endTime = microtime(true);
    $executionTime = round(($endTime - $startTime), 4);
    
    // 如果上游API返回了execution_time，使用上游的时间，否则使用本地计算的时间
    if (!isset($result['execution_time'])) {
        $result['execution_time'] = $executionTime . ' 秒';
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '服务器内部错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 使用cURL发起HTTP请求
 */
function makeHttpRequest($url, $timeout = 30) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => $timeout,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_USERAGENT => 'iDatas-QQPhone/1.0',
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($response === false || !empty($error)) {
        error_log("HTTP Request Error: " . $error);
        return false;
    }
    
    if ($httpCode !== 200) {
        error_log("HTTP Request Failed: HTTP $httpCode");
        return false;
    }
    
    return $response;
}
?>
