<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 用户数据文件路径
$usersFile = __DIR__ . '/users.json';

// 确保用户数据文件存在
if (!file_exists($usersFile)) {
    file_put_contents($usersFile, json_encode([], JSON_UNESCAPED_UNICODE));
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode([
        "code" => 400,
        "message" => "无效的请求数据",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$username = trim($input['username'] ?? '');
$password = $input['password'] ?? '';
$token = $input['token'] ?? '';

// 验证输入
if (empty($username) || empty($password) || empty($token)) {
    echo json_encode([
        "code" => 400,
        "message" => "用户名、密码和Token不能为空",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证用户名格式（只允许字母、数字、下划线）
if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $username)) {
    echo json_encode([
        "code" => 400,
        "message" => "用户名格式不正确（3-20位字母、数字、下划线）",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证密码长度
if (strlen($password) < 6) {
    echo json_encode([
        "code" => 400,
        "message" => "密码长度至少6位",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证Token格式（18位小写字母和数字）
if (!preg_match('/^[a-z0-9]{18}$/', $token)) {
    echo json_encode([
        "code" => 400,
        "message" => "Token格式不正确",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 读取现有用户数据
    $users = json_decode(file_get_contents($usersFile), true);
    if (!is_array($users)) {
        $users = [];
    }

    // 检查用户名是否已存在
    foreach ($users as $user) {
        if ($user['username'] === $username) {
            echo json_encode([
                "code" => 409,
                "message" => "用户名已存在",
                "user" => null
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // 检查Token是否已存在
        if ($user['token'] === $token) {
            echo json_encode([
                "code" => 409,
                "message" => "Token已存在，请重新生成",
                "user" => null
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    // 创建新用户
    $newUser = [
        'username' => $username,
        'password' => password_hash($password, PASSWORD_DEFAULT),
        'token' => $token,
        'created_at' => date('Y-m-d H:i:s'),
        'last_login' => null,
        'login_count' => 0,
        'vipcode' => 0,
        'viptime' => '',
        'tokencode' => 200
    ];

    // 添加到用户数组
    $users[] = $newUser;

    // 保存到文件
    if (file_put_contents($usersFile, json_encode($users, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT))) {
        // 返回用户信息（不包含密码）
        $userResponse = [
            'username' => $newUser['username'],
            'token' => $newUser['token'],
            'created_at' => $newUser['created_at'],
            'vipcode' => $newUser['vipcode'],
            'viptime' => $newUser['viptime'],
            'tokencode' => $newUser['tokencode']
        ];

        echo json_encode([
            "code" => 200,
            "message" => "注册成功",
            "user" => $userResponse
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            "code" => 500,
            "message" => "保存用户数据失败",
            "user" => null
        ], JSON_UNESCAPED_UNICODE);
    }

} catch (Exception $e) {
    echo json_encode([
        "code" => 500,
        "message" => "服务器内部错误：" . $e->getMessage(),
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
