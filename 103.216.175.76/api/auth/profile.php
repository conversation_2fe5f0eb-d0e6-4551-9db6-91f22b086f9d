<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 用户数据文件路径
$usersFile = __DIR__ . '/users.json';

// 获取Token参数
$token = $_GET['token'] ?? '';

if (empty($token)) {
    echo json_encode([
        "code" => 400,
        "message" => "缺少Token参数",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查用户数据文件是否存在
if (!file_exists($usersFile)) {
    echo json_encode([
        "code" => 404,
        "message" => "用户数据文件不存在",
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 读取用户数据
    $users = json_decode(file_get_contents($usersFile), true);
    if (!is_array($users)) {
        echo json_encode([
            "code" => 500,
            "message" => "用户数据格式错误",
            "user" => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 查找用户
    $foundUser = null;
    
    foreach ($users as $user) {
        if ($user['token'] === $token) {
            $foundUser = $user;
            break;
        }
    }

    if (!$foundUser) {
        echo json_encode([
            "code" => 404,
            "message" => "用户不存在或Token无效",
            "user" => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 返回用户信息（不包含密码）
    $userResponse = [
        'username' => $foundUser['username'],
        'token' => $foundUser['token'],
        'created_at' => $foundUser['created_at'],
        'last_login' => $foundUser['last_login'] ?? null,
        'login_count' => $foundUser['login_count'] ?? 0,
        'vipcode' => $foundUser['vipcode'] ?? 0,
        'viptime' => $foundUser['viptime'] ?? '',
        'tokencode' => $foundUser['tokencode'] ?? 200
    ];

    echo json_encode([
        "code" => 200,
        "message" => "获取用户信息成功",
        "user" => $userResponse
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        "code" => 500,
        "message" => "服务器内部错误：" . $e->getMessage(),
        "user" => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
