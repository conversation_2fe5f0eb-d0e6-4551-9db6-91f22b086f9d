#!/bin/bash

# iDatas 性能优化部署脚本
# 用于快速部署和测试性能优化

echo "🚀 开始部署 iDatas 性能优化..."

# 检查必要文件
echo "📋 检查优化文件..."

required_files=(
    "index.html"
    "assets/css/icons.css"
    "assets/js/performance.js"
    "sw.js"
    ".htaccess"
    "performance-test.html"
)

missing_files=()

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ 缺少以下文件:"
    printf '%s\n' "${missing_files[@]}"
    exit 1
fi

echo "✅ 所有必要文件都存在"

# 备份原始文件
echo "💾 备份原始文件..."
backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"

if [ -f "index.html.original" ]; then
    cp "index.html.original" "$backup_dir/"
else
    cp "index.html" "$backup_dir/index.html.original"
fi

if [ -f ".htaccess.original" ]; then
    cp ".htaccess.original" "$backup_dir/"
else
    cp ".htaccess" "$backup_dir/.htaccess.original"
fi

echo "✅ 备份完成: $backup_dir"

# 检查Apache模块
echo "🔧 检查Apache配置..."

required_modules=(
    "mod_deflate"
    "mod_expires"
    "mod_headers"
    "mod_rewrite"
)

echo "需要的Apache模块:"
printf '%s\n' "${required_modules[@]}"
echo "请确保这些模块已启用"

# 设置文件权限
echo "🔐 设置文件权限..."
chmod 644 index.html
chmod 644 assets/css/*.css
chmod 644 assets/js/*.js
chmod 644 sw.js
chmod 644 .htaccess
chmod 644 performance-test.html

echo "✅ 文件权限设置完成"

# 清除可能的缓存
echo "🧹 清除缓存..."

# 如果有Redis
if command -v redis-cli &> /dev/null; then
    echo "清除Redis缓存..."
    redis-cli FLUSHALL
fi

# 如果有Memcached
if command -v memcached &> /dev/null; then
    echo "清除Memcached缓存..."
    echo 'flush_all' | nc localhost 11211
fi

echo "✅ 服务器缓存清除完成"

# 验证配置
echo "🔍 验证配置..."

# 检查.htaccess语法
if command -v apache2ctl &> /dev/null; then
    if apache2ctl configtest; then
        echo "✅ Apache配置语法正确"
    else
        echo "❌ Apache配置有错误，请检查.htaccess文件"
        exit 1
    fi
fi

# 生成测试报告
echo "📊 生成部署报告..."

cat > deployment-report.txt << EOF
iDatas 性能优化部署报告
========================

部署时间: $(date)
备份目录: $backup_dir

已部署的优化:
✅ 外部资源异步加载
✅ 关键CSS内联
✅ JavaScript延迟加载
✅ Service Worker缓存
✅ 本地图标系统
✅ Apache性能配置
✅ 资源压缩和缓存

测试步骤:
1. 访问主页面检查加载速度
2. 打开浏览器开发者工具查看Network面板
3. 访问 performance-test.html 进行自动化测试
4. 使用Lighthouse进行性能评分

预期改进:
- FontAwesome加载时间: 1.53s → <300ms
- 首屏渲染时间: 提升40-60%
- 重复访问速度: 提升80%
- Lighthouse性能分数: >90

如需回滚:
cp $backup_dir/index.html.original index.html
cp $backup_dir/.htaccess.original .htaccess

EOF

echo "✅ 部署报告已生成: deployment-report.txt"

# 性能测试
echo "🧪 运行基础性能测试..."

if command -v curl &> /dev/null; then
    echo "测试主页面响应时间..."
    response_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost/)
    echo "主页面响应时间: ${response_time}s"
    
    echo "测试资源压缩..."
    if curl -H "Accept-Encoding: gzip" -s -I http://localhost/assets/css/style.css | grep -q "Content-Encoding: gzip"; then
        echo "✅ CSS文件已启用Gzip压缩"
    else
        echo "⚠️  CSS文件未启用Gzip压缩"
    fi
fi

# 完成
echo ""
echo "🎉 性能优化部署完成!"
echo ""
echo "下一步:"
echo "1. 清除浏览器缓存"
echo "2. 访问网站测试加载速度"
echo "3. 访问 performance-test.html 进行详细测试"
echo "4. 使用Chrome DevTools Lighthouse进行评分"
echo ""
echo "如果遇到问题:"
echo "- 检查Apache错误日志"
echo "- 确认所有必要的Apache模块已启用"
echo "- 使用备份文件回滚: $backup_dir"
echo ""
echo "监控建议:"
echo "- 定期检查性能指标"
echo "- 监控服务器资源使用"
echo "- 关注用户反馈"

# 创建快速测试脚本
cat > quick-test.sh << 'EOF'
#!/bin/bash
echo "🚀 快速性能测试"
echo "=================="

if command -v curl &> /dev/null; then
    echo "1. 测试主页面..."
    time curl -s http://localhost/ > /dev/null
    
    echo "2. 测试CSS文件..."
    time curl -s http://localhost/assets/css/style.css > /dev/null
    
    echo "3. 测试JS文件..."
    time curl -s http://localhost/assets/js/main.js > /dev/null
    
    echo "4. 测试Service Worker..."
    if curl -s http://localhost/sw.js | grep -q "Service Worker"; then
        echo "✅ Service Worker文件正常"
    else
        echo "❌ Service Worker文件异常"
    fi
else
    echo "请安装curl来运行性能测试"
fi

echo ""
echo "完整测试请访问: http://localhost/performance-test.html"
EOF

chmod +x quick-test.sh

echo "✅ 快速测试脚本已创建: quick-test.sh"
echo ""
echo "运行 ./quick-test.sh 进行快速测试"
