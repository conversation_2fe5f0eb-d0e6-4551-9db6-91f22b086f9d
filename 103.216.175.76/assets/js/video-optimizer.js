/**
 * 视频优化器
 * 实现智能视频加载、质量选择和性能优化
 */

class VideoOptimizer {
    constructor() {
        this.loadedVideos = new Set();
        this.networkSpeed = this.detectNetworkSpeed();
        this.deviceCapability = this.detectDeviceCapability();
    }

    // 检测网络速度
    detectNetworkSpeed() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            const effectiveType = connection.effectiveType;
            
            switch (effectiveType) {
                case 'slow-2g':
                case '2g':
                    return 'slow';
                case '3g':
                    return 'medium';
                case '4g':
                default:
                    return 'fast';
            }
        }
        return 'medium'; // 默认中等速度
    }

    // 检测设备能力
    detectDeviceCapability() {
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isLowEnd = navigator.hardwareConcurrency <= 2;
        const hasLimitedMemory = navigator.deviceMemory && navigator.deviceMemory <= 2;
        
        if (isMobile || isLowEnd || hasLimitedMemory) {
            return 'low';
        }
        return 'high';
    }

    // 获取最佳视频质量
    getBestVideoQuality() {
        if (this.networkSpeed === 'slow' || this.deviceCapability === 'low') {
            return 'low';
        } else if (this.networkSpeed === 'medium') {
            return 'medium';
        }
        return 'high';
    }

    // 智能视频加载
    async loadVideo(videoElement, videoSources, options = {}) {
        const videoId = videoElement.id || 'video_' + Date.now();
        
        if (this.loadedVideos.has(videoId)) {
            return Promise.resolve();
        }

        const quality = options.quality || this.getBestVideoQuality();
        const videoUrl = this.selectVideoSource(videoSources, quality);
        
        return new Promise((resolve, reject) => {
            // 清空现有源
            videoElement.innerHTML = '';
            
            // 创建视频源
            const source = document.createElement('source');
            source.src = videoUrl;
            source.type = 'video/mp4';
            
            // 设置视频属性
            videoElement.preload = options.preload || 'metadata';
            videoElement.controls = options.controls !== false;
            
            // 添加加载事件
            const loadHandler = () => {
                this.loadedVideos.add(videoId);
                videoElement.removeEventListener('loadeddata', loadHandler);
                videoElement.removeEventListener('error', errorHandler);
                resolve();
            };
            
            const errorHandler = (error) => {
                videoElement.removeEventListener('loadeddata', loadHandler);
                videoElement.removeEventListener('error', errorHandler);
                reject(error);
            };
            
            videoElement.addEventListener('loadeddata', loadHandler);
            videoElement.addEventListener('error', errorHandler);
            
            // 添加源并开始加载
            videoElement.appendChild(source);
            videoElement.load();
        });
    }

    // 选择视频源
    selectVideoSource(sources, quality) {
        if (typeof sources === 'string') {
            return sources;
        }
        
        if (sources[quality]) {
            return sources[quality];
        }
        
        // 降级策略
        if (quality === 'high' && sources.medium) {
            return sources.medium;
        }
        if ((quality === 'high' || quality === 'medium') && sources.low) {
            return sources.low;
        }
        
        // 返回第一个可用源
        return Object.values(sources)[0];
    }

    // 预加载视频元数据
    preloadVideoMetadata(videoElement, videoUrl) {
        if (this.networkSpeed === 'slow') {
            return; // 慢速网络不预加载
        }
        
        const tempVideo = document.createElement('video');
        tempVideo.preload = 'metadata';
        tempVideo.src = videoUrl;
        tempVideo.style.display = 'none';
        document.body.appendChild(tempVideo);
        
        tempVideo.addEventListener('loadedmetadata', () => {
            // 将元数据传递给实际视频元素
            if (videoElement) {
                videoElement.duration = tempVideo.duration;
            }
            document.body.removeChild(tempVideo);
        });
    }

    // 创建视频占位符
    createVideoPlaceholder(container, options = {}) {
        const placeholder = document.createElement('div');
        placeholder.className = 'video-placeholder';
        placeholder.innerHTML = `
            <div class="placeholder-content">
                <div class="play-button">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                </div>
                <h3>${options.title || '视频内容'}</h3>
                <p>${options.description || '点击播放按钮加载视频'}</p>
                <div class="video-info">
                    <span class="video-duration">📹 ${options.duration || '视频'}</span>
                    <span class="video-size">🔄 按需加载</span>
                </div>
            </div>
        `;
        
        container.appendChild(placeholder);
        return placeholder;
    }

    // 创建加载状态
    createLoadingState(container) {
        container.innerHTML = `
            <div class="video-loading">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <h3>正在加载视频...</h3>
                <p>请稍候，视频正在下载中</p>
                <div class="loading-progress">
                    <div class="progress-bar" id="videoProgress_${Date.now()}"></div>
                </div>
            </div>
        `;
    }

    // 创建错误状态
    createErrorState(container, retryCallback) {
        container.innerHTML = `
            <div class="video-error">
                <div class="error-icon">⚠️</div>
                <h3>视频加载失败</h3>
                <p>请检查网络连接或稍后重试</p>
                <button class="retry-btn">重试</button>
            </div>
        `;
        
        const retryBtn = container.querySelector('.retry-btn');
        if (retryBtn && retryCallback) {
            retryBtn.addEventListener('click', retryCallback);
        }
    }

    // 监控视频性能
    monitorVideoPerformance(videoElement) {
        const startTime = performance.now();
        let firstFrameTime = 0;
        
        const onLoadStart = () => {
            console.log('视频开始加载');
        };
        
        const onLoadedData = () => {
            firstFrameTime = performance.now() - startTime;
            console.log(`视频首帧加载时间: ${firstFrameTime.toFixed(2)}ms`);
        };
        
        const onCanPlay = () => {
            const canPlayTime = performance.now() - startTime;
            console.log(`视频可播放时间: ${canPlayTime.toFixed(2)}ms`);
        };
        
        const onProgress = () => {
            if (videoElement.buffered.length > 0) {
                const buffered = videoElement.buffered.end(0);
                const duration = videoElement.duration || 1;
                const progress = (buffered / duration) * 100;
                console.log(`视频缓冲进度: ${progress.toFixed(1)}%`);
            }
        };
        
        videoElement.addEventListener('loadstart', onLoadStart);
        videoElement.addEventListener('loadeddata', onLoadedData);
        videoElement.addEventListener('canplay', onCanPlay);
        videoElement.addEventListener('progress', onProgress);
        
        // 清理函数
        return () => {
            videoElement.removeEventListener('loadstart', onLoadStart);
            videoElement.removeEventListener('loadeddata', onLoadedData);
            videoElement.removeEventListener('canplay', onCanPlay);
            videoElement.removeEventListener('progress', onProgress);
        };
    }

    // 获取网络和设备信息
    getOptimizationInfo() {
        return {
            networkSpeed: this.networkSpeed,
            deviceCapability: this.deviceCapability,
            recommendedQuality: this.getBestVideoQuality(),
            supportsHLS: this.supportsHLS(),
            supportsMSE: this.supportsMSE()
        };
    }

    // 检测HLS支持
    supportsHLS() {
        const video = document.createElement('video');
        return video.canPlayType('application/vnd.apple.mpegurl') !== '';
    }

    // 检测MSE支持
    supportsMSE() {
        return 'MediaSource' in window;
    }
}

// 创建全局实例
window.VideoOptimizer = new VideoOptimizer();

// 导出优化信息
console.log('视频优化器初始化完成:', window.VideoOptimizer.getOptimizationInfo());
