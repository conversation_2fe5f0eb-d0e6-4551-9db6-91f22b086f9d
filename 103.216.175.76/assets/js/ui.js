// UI管理系统
class UIManager {
    constructor() {
        this.activeModal = null;
        this.toastQueue = [];
        this.isLoading = false;
        this.init();
    }

    // 初始化UI管理器
    init() {
        this.bindEvents();
        this.initializeComponents();
    }

    // 绑定事件
    bindEvents() {
        // 模态框关闭事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                // 检查是否处于强制登录状态
                const isAuthRequired = document.body.classList.contains('auth-required');
                const isAuthModal = this.activeModal === 'loginModal' || this.activeModal === 'registerModal';

                if (isAuthRequired && isAuthModal) {
                    // 强制登录状态下不允许点击背景关闭
                    this.showToast('请先登录后再继续使用系统', 'warning');
                    return;
                }

                this.closeModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                // 检查是否处于强制登录状态
                const isAuthRequired = document.body.classList.contains('auth-required');
                const isAuthModal = this.activeModal === 'loginModal' || this.activeModal === 'registerModal';

                if (isAuthRequired && isAuthModal) {
                    // 强制登录状态下不允许ESC键关闭
                    this.showToast('请先登录后再继续使用系统', 'warning');
                    return;
                }

                this.closeModal();
            }
        });

        // 搜索功能
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.handleSearch(e.target.value);
            }, CONFIG.APP.SEARCH_DEBOUNCE));
        }

        // 移动端底栏管理
        this.initMobileBottomBar();

        // 过滤标签
        const filterTabs = document.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                this.handleFilterChange(tab.dataset.filter);
            });
        });
    }

    // 初始化组件
    initializeComponents() {
        // 初始化工具提示
        this.initTooltips();
        
        // 初始化懒加载
        this.initLazyLoading();
    }

    // 显示模态框
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        // 关闭当前模态框
        if (this.activeModal) {
            this.closeModal();
        }

        this.activeModal = modalId;
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // 聚焦第一个输入框
        setTimeout(() => {
            const firstInput = modal.querySelector('input, textarea, select');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }

    // 关闭模态框
    closeModal(modalId = null) {
        const targetModalId = modalId || this.activeModal;
        if (!targetModalId) return;

        // 检查是否处于强制登录状态
        const isAuthRequired = document.body.classList.contains('auth-required');
        const isAuthModal = targetModalId === 'loginModal' || targetModalId === 'registerModal';

        // 如果处于强制登录状态且试图关闭登录/注册模态框，则阻止关闭
        if (isAuthRequired && isAuthModal) {
            // 显示提示信息
            this.showToast('请先登录后再继续使用系统', 'warning');
            return;
        }

        const modal = document.getElementById(targetModalId);
        if (modal) {
            modal.classList.remove('show');

            // 清空表单
            const forms = modal.querySelectorAll('form');
            forms.forEach(form => form.reset());
        }

        this.activeModal = null;
        document.body.style.overflow = '';
    }

    // 显示加载状态
    showLoading(message = '加载中...') {
        if (this.isLoading) return;

        this.isLoading = true;
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            const loadingText = loadingOverlay.querySelector('p');
            if (loadingText) {
                loadingText.textContent = message;
            }
            loadingOverlay.classList.add('show');
        }
    }

    // 隐藏加载状态
    hideLoading() {
        this.isLoading = false;
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('show');
        }
    }

    // 显示消息提示
    showToast(message, type = 'info', duration = CONFIG.APP.TOAST_DURATION) {
        const toast = document.getElementById('toast');
        if (!toast) return;

        // 清除之前的类
        toast.className = 'toast';
        
        // 添加类型类
        toast.classList.add(type);

        // 设置消息和图标
        const messageEl = toast.querySelector('.toast-message');
        const iconEl = toast.querySelector('.toast-icon');
        
        if (messageEl) messageEl.textContent = message;
        
        if (iconEl) {
            let iconClass = 'fas fa-info-circle';
            switch (type) {
                case 'success':
                    iconClass = 'fas fa-check-circle';
                    break;
                case 'error':
                    iconClass = 'fas fa-exclamation-circle';
                    break;
                case 'warning':
                    iconClass = 'fas fa-exclamation-triangle';
                    break;
            }
            iconEl.className = `toast-icon ${iconClass}`;
        }

        // 显示提示
        toast.classList.add('show');

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, duration);
    }

    // 处理搜索
    handleSearch(query) {
        if (window.ToolsManager) {
            window.ToolsManager.filterTools(query);
        }
    }

    // 处理过滤器变化
    handleFilterChange(filter) {
        // 更新活动标签
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        // 过滤工具
        if (window.ToolsManager) {
            window.ToolsManager.filterByCategory(filter);
        }
    }

    // 初始化移动端底栏
    initMobileBottomBar() {
        const bottomBar = document.getElementById('mobileBottomBar');
        if (!bottomBar) return;

        // 根据屏幕尺寸显示/隐藏底栏
        this.updateBottomBarVisibility();

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.updateBottomBarVisibility();
        });

        // 监听滚动事件，实现底栏自动隐藏/显示
        let lastScrollTop = 0;
        let scrollTimeout;

        window.addEventListener('scroll', () => {
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (window.innerWidth <= 768) {
                if (currentScrollTop > lastScrollTop && currentScrollTop > 100) {
                    // 向下滚动，隐藏底栏
                    bottomBar.style.transform = 'translateY(100%)';
                } else {
                    // 向上滚动或在顶部，显示底栏
                    bottomBar.style.transform = 'translateY(0)';
                }

                // 停止滚动后显示底栏
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    bottomBar.style.transform = 'translateY(0)';
                }, 1000);
            }

            lastScrollTop = currentScrollTop;
        });
    }

    // 更新底栏可见性
    updateBottomBarVisibility() {
        const bottomBar = document.getElementById('mobileBottomBar');
        if (!bottomBar) return;

        if (window.innerWidth <= 768) {
            bottomBar.style.display = 'block';
        } else {
            bottomBar.style.display = 'none';
        }
    }

    // 设置底栏活动项
    setBottomBarActive(itemName) {
        const items = document.querySelectorAll('.bottom-bar-item');
        items.forEach(item => {
            item.classList.remove('active');
        });

        const activeItem = document.querySelector(`.bottom-bar-item[onclick*="${itemName}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }

    // 初始化工具提示
    initTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    // 显示工具提示
    showTooltip(element, text) {
        let tooltip = document.getElementById('tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'tooltip';
            tooltip.className = 'tooltip';
            document.body.appendChild(tooltip);
        }

        tooltip.textContent = text;
        tooltip.style.display = 'block';

        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    }

    // 隐藏工具提示
    hideTooltip() {
        const tooltip = document.getElementById('tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }

    // 初始化懒加载
    initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // 创建确认对话框
    showConfirm(message, onConfirm, onCancel = null) {
        const confirmModal = document.createElement('div');
        confirmModal.className = 'modal confirm-modal';
        confirmModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>确认操作</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                    <div class="confirm-buttons">
                        <button class="btn btn-secondary cancel-btn">取消</button>
                        <button class="btn btn-primary confirm-btn">确认</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(confirmModal);

        // 绑定事件
        const cancelBtn = confirmModal.querySelector('.cancel-btn');
        const confirmBtn = confirmModal.querySelector('.confirm-btn');

        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(confirmModal);
            if (onCancel) onCancel();
        });

        confirmBtn.addEventListener('click', () => {
            document.body.removeChild(confirmModal);
            onConfirm();
        });

        // 显示模态框
        setTimeout(() => {
            confirmModal.classList.add('show');
        }, 10);
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('已复制到剪贴板', 'success');
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showToast('已复制到剪贴板', 'success');
        }
    }

    // 下载文件
    downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 全局UI函数
function openModal(modalId) {
    if (window.UI) {
        window.UI.openModal(modalId);
    }
}

function closeModal(modalId) {
    if (window.UI) {
        window.UI.closeModal(modalId);
    }
}

function openProfile() {
    if (window.AuthManager && window.AuthManager.isLoggedIn) {
        if (window.ProfileManager) {
            window.ProfileManager.showProfile();
        }
        // 只在移动端设置底栏活动状态
        if (window.UI && window.innerWidth <= 768) {
            window.UI.setBottomBarActive('openProfile');
        }
    } else {
        if (window.UI) {
            window.UI.showToast('请先登录', 'warning');
        } else {
            alert('请先登录');
        }
    }
}

function openTutorial() {
    if (window.UI) {
        window.UI.openModal('tutorialModal');
        // 只在移动端设置底栏活动状态
        if (window.innerWidth <= 768) {
            window.UI.setBottomBarActive('openTutorial');
        }
    }
}

// 滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
    // 只在移动端设置底栏活动状态
    if (window.UI && window.innerWidth <= 768) {
        window.UI.setBottomBarActive('scrollToTop');
    }
}

// 初始化UI管理器
document.addEventListener('DOMContentLoaded', function() {
    window.UI = new UIManager();
});
