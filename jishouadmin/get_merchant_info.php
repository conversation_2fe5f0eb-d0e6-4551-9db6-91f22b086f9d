<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

$merchant_id = $_GET['merchant_id'] ?? '';

if (empty($merchant_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户ID不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT shop_name, shop_description, merchant_balance, payment_qr_code, created_at, updated_at FROM merchants WHERE merchant_id = ?");
    $stmt->execute([$merchant_id]);
    $merchant = $stmt->fetch();

    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户不存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'status' => 'success',
            'message' => '获取商户信息成功',
            'data' => $merchant
        ], JSON_UNESCAPED_UNICODE);
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取商户信息失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>