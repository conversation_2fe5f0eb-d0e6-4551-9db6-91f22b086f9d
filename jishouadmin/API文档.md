# 多商户商品管理系统 API 文档

## 系统概述

本系统是一个基于PHP的多商户商品管理系统，支持商户管理商品、卡密、订单和提现等功能。

### 数据库配置
- 数据库名：`yjsyjs`
- 用户名：`yjsyjs`
- 密码：`yjsyjs`
- 字符集：`utf8mb4`

## API 接口列表

### 1. 注册商户

**接口地址：** `register_merchant.php`

**请求方式：** GET

**功能描述：** 根据商户ID自动生成商户信息并注册

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_id | string | 是 | 商户ID |

**商户秘钥生成算法：**
- 使用固定的密钥和盐值
- 组合：商户ID + 密钥 + 盐值
- MD5加密生成32位哈希
- 格式：商户ID前缀_哈希值

**返回示例：**
```json
{
    "status": "success",
    "message": "商户注册成功",
    "data": {
        "merchant_id": "M003",
        "merchant_secret": "M00_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
        "shop_name": "商户M003的商店",
        "shop_description": "这是商户M003的商店，提供各种优质商品和服务。",
        "merchant_balance": "0.00",
        "payment_qr_code": "qrcodes/merchant_M003_qr.png",
        "created_at": "2024-01-01 12:00:00"
    }
}
```

### 2. 获取商户信息

**接口地址：** `get_merchant_info.php`

**请求方式：** GET

**功能描述：** 根据商户ID获取商户详细信息（不包含商户秘钥）

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_id | string | 是 | 商户ID |

**返回示例：**
```json
{
    "status": "success",
    "message": "获取商户信息成功",
    "data": {
        "shop_name": "测试商店1",
        "shop_description": "这是一个测试商店",
        "merchant_balance": "1000.00",
        "payment_qr_code": "qrcodes/shop1_qr.png",
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
    }
}
```

### 3. 验证商户秘钥

**接口地址：** `verify_merchant_secret.php`

**请求方式：** GET

**功能描述：** 验证商户秘钥是否正确

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_id | string | 是 | 商户ID |
| merchant_secret | string | 是 | 商户秘钥 |

**返回示例：**
```json
{
    "status": "success",
    "message": "商户秘钥验证成功",
    "data": {
        "merchant_id": "M003",
        "provided_secret": "M00_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
        "expected_secret": "M00_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
        "is_valid": true
    }
}
```

---

### 4. 商户发起提现

**接口地址：** `merchant_withdraw.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |
| withdrawal_amount | decimal | 是 | 提现金额 |

**返回示例：**
```json
{
    "status": "success",
    "message": "提现申请成功",
    "data": {
        "withdrawal_id": 1,
        "merchant_id": "M001",
        "withdrawal_amount": "100.00",
        "original_balance": "1000.00",
        "fee_amount": "0.00",
        "current_balance": "900.00"
    }
}
```

---

### 5. 商户提现列表

**接口地址：** `get_withdrawal_list.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |

**返回示例：**
```json
{
    "status": "success",
    "message": "获取提现列表成功",
    "data": [
        {
            "withdrawal_id": 1,
            "merchant_id": "M001",
            "withdrawal_time": "2024-01-01 10:00:00",
            "withdrawal_amount": "100.00",
            "withdrawal_status": "pending",
            "created_at": "2024-01-01 10:00:00"
        }
    ]
}
```

---

### 6. 商户商品列表

**接口地址：** `get_product_list.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |

**返回示例：**
```json
{
    "status": "success",
    "message": "获取商品列表成功",
    "data": [
        {
            "product_id": 1,
            "merchant_id": "M001",
            "product_name": "游戏充值卡",
            "product_price": "50.00",
            "product_description": "热门游戏充值卡",
            "stock_quantity": 100,
            "status": "active",
            "created_at": "2024-01-01 10:00:00",
            "updated_at": "2024-01-01 10:00:00"
        }
    ]
}
```

---

### 7. 商品卡密列表

**接口地址：** `get_card_key_list.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| product_id | int | 是 | 商品ID |

**返回示例：**
```json
{
    "status": "success",
    "message": "获取卡密列表成功",
    "data": [
        {
            "card_key_id": 1,
            "merchant_id": "M001",
            "product_id": 1,
            "product_name": "游戏充值卡",
            "card_key_content": "GAME001-XXXX-YYYY-ZZZZ",
            "card_key_status": "unsold",
            "order_id": null,
            "created_at": "2024-01-01 10:00:00",
            "updated_at": "2024-01-01 10:00:00"
        }
    ]
}
```

---

### 8. 商户订单列表

**接口地址：** `get_order_list.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |

**返回示例：**
```json
{
    "status": "success",
    "message": "获取订单列表成功",
    "data": [
        {
            "order_id": "ORDER001",
            "merchant_id": "M001",
            "product_name": "游戏充值卡",
            "product_price": "50.00",
            "purchase_time": "2024-01-01 10:00:00",
            "delivery_content": "GAME001-XXXX-YYYY-ZZZZ",
            "order_status": "paid",
            "customer_contact": "<EMAIL>",
            "created_at": "2024-01-01 10:00:00",
            "updated_at": "2024-01-01 10:00:00"
        }
    ]
}
```

---

### 9. 新增商品

**接口地址：** `add_product.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |
| product_name | string | 是 | 商品名称 |
| product_price | decimal | 是 | 商品价格 |
| product_description | string | 否 | 商品简介 |

**返回示例：**
```json
{
    "status": "success",
    "message": "商品添加成功",
    "data": {
        "product_id": 1,
        "merchant_id": "M001",
        "product_name": "游戏充值卡",
        "product_price": "50.00",
        "product_description": "热门游戏充值卡",
        "stock_quantity": 0,
        "status": "active"
    }
}
```

---

### 10. 增加商品卡密

**接口地址：** `add_card_keys.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |
| product_id | int | 是 | 商品ID |
| card_key_content | string | 是 | 卡密内容，多个卡密用-分割 |

**返回示例：**
```json
{
    "status": "success",
    "message": "成功添加 2 个卡密",
    "data": {
        "inserted_count": 2,
        "product_id": 1,
        "product_name": "游戏充值卡",
        "card_keys": [
            {
                "card_key_id": 1,
                "merchant_id": "M001",
                "product_id": 1,
                "product_name": "游戏充值卡",
                "card_key_content": "GAME001-XXXX-YYYY-ZZZZ",
                "card_key_status": "unsold",
                "created_at": "2024-01-01 10:00:00"
            }
        ]
    }
}
```

---

### 11. 编辑商品信息

**接口地址：** `edit_product.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |
| product_id | int | 是 | 商品ID |
| product_name | string | 否 | 商品名称（不传递则不修改） |
| product_price | decimal | 否 | 商品价格（不传递则不修改） |
| product_description | string | 否 | 商品简介（不传递则不修改） |

**返回示例：**
```json
{
    "status": "success",
    "message": "商品信息更新成功",
    "data": {
        "product_id": 1,
        "merchant_id": "M001",
        "product_name": "游戏充值卡",
        "product_price": "60.00",
        "product_description": "热门游戏充值卡",
        "stock_quantity": 100,
        "status": "active",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 11:00:00"
    }
}
```

---

### 12. 删除商品

**接口地址：** `delete_product.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |
| product_id | int | 是 | 商品ID |

**返回示例：**
```json
{
    "status": "success",
    "message": "商品删除成功",
    "data": {
        "product_id": 1,
        "product_name": "游戏充值卡",
        "delete_status": "success",
        "message": "商品删除成功"
    }
}
```

---

### 13. 获取订单内容

**接口地址：** `get_order_detail.php`

**请求方式：** GET

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| order_id | string | 是 | 订单ID |

**返回示例：**
```json
{
    "status": "success",
    "message": "获取订单详情成功",
    "data": {
        "order_id": "ORDER001",
        "merchant_id": "M001",
        "product_name": "游戏充值卡",
        "product_price": "50.00",
        "purchase_time": "2024-01-01 10:00:00",
        "delivery_content": "GAME001-XXXX-YYYY-ZZZZ",
        "order_status": "paid",
        "customer_contact": "<EMAIL>",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00"
    }
}
```

**注意：** 如果订单未支付，`delivery_content` 字段会显示为 `******`

---

### 14. 设置订单支付状态

**接口地址：** `set_order_payment_status.php`

**请求方式：** GET

**功能描述：** 设置订单的支付状态

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| order_id | string | 是 | 订单ID |
| payment_status | string | 是 | 支付状态(unpaid/paid/delivered/cancelled) |

**返回示例：**
```json
{
    "status": "success",
    "message": "订单状态更新成功",
    "data": {
        "order_id": "ORDER123456789",
        "old_status": "unpaid",
        "new_status": "paid",
        "order_info": {
            "order_id": "ORDER123456789",
            "merchant_id": "M001",
            "product_name": "测试商品",
            "product_price": "99.00",
            "delivery_content": "CARD001-XXXX-YYYY-ZZZZ",
            "order_status": "paid",
            "customer_contact": "<EMAIL>"
        }
    }
}
```

---

### 15. 商品信息查询

**接口地址：** `get_product_info.php`

**请求方式：** GET

**功能描述：** 根据商品ID获取商品详细信息

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| product_id | int | 是 | 商品ID |

**返回示例：**
```json
{
    "status": "success",
    "message": "获取商品信息成功",
    "data": {
        "product_id": 1,
        "merchant_id": "M001",
        "product_name": "测试商品",
        "product_price": "99.00",
        "product_description": "这是一个测试商品",
        "stock_quantity": 10,
        "status": "active",
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
    }
}
```

---

### 16. 发起订单

**接口地址：** `create_order.php`

**请求方式：** GET

**功能描述：** 创建订单并生成支付链接

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customer_contact | string | 是 | 客户联系方式 |
| product_id | int | 是 | 商品ID |

**返回示例：**
```json
{
    "status": "success",
    "message": "订单创建成功",
    "data": {
        "order_info": {
            "order_id": "ORDER123456789",
            "merchant_id": "M001",
            "product_name": "测试商品",
            "product_price": "99.00",
            "delivery_content": "*******",
            "order_status": "unpaid",
            "customer_contact": "<EMAIL>"
        },
        "payment_url": "http://aczhifutong.ndvfp.cn/submit.php?pid=10002&type=wxpay&out_trade_no=ORDER123456789&..."
    }
}
```

---

### 17. 检查支付状态

**接口地址：** `check_payment_status.php`

**请求方式：** GET

**功能描述：** 检查订单支付状态并同步数据库

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| order_id | string | 是 | 订单ID |

**返回示例：**
```json
{
    "status": "success",
    "message": "支付成功，订单已更新",
    "data": {
        "order_id": "ORDER123456789",
        "merchant_id": "M001",
        "product_name": "测试商品",
        "product_price": "99.00",
        "delivery_content": "CARD001-XXXX-YYYY-ZZZZ",
        "order_status": "paid",
        "customer_contact": "<EMAIL>"
    }
}
```

---

### 18. 编辑商户信息

**接口地址：** `edit_merchant_info.php`

**请求方式：** GET

**功能描述：** 根据商户秘钥编辑商户信息（商店名称和商店介绍）

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchant_secret | string | 是 | 商户秘钥 |
| shop_name | string | 否 | 商店名称（不传递则不修改） |
| shop_description | string | 否 | 商店介绍（不传递则不修改） |

**返回示例：**
```json
{
    "status": "success",
    "message": "商户信息更新成功",
    "data": {
        "merchant_id": "M001",
        "shop_name": "新的商店名称",
        "shop_description": "新的商店介绍",
        "merchant_balance": "1000.00",
        "payment_qr_code": "qrcodes/shop1_qr.png",
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 13:00:00",
        "updated_fields": ["shop_name", "shop_description"]
    }
}
```

**注意：** 
- 只更新传递的参数，未传递的参数保持不变
- 如果没有传递任何要更新的参数，会返回当前信息
- 返回的 `updated_fields` 数组显示本次更新了哪些字段

---

### 19. 更新提现状态

**接口地址：** `update_withdrawal_status.php`

**请求方式：** GET

**功能描述：** 管理员更新提现申请状态

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| withdrawal_id | int | 是 | 提现ID |
| admin_password | string | 是 | 管理员密码 |
| status | string | 是 | 状态值(pending/approved/rejected/completed) |

**返回示例：**
```json
{
    "status": "success",
    "message": "提现状态更新成功",
    "data": {
        "withdrawal_id": 1,
        "merchant_id": "M001",
        "shop_name": "测试商店",
        "withdrawal_amount": "100.00",
        "old_status": "pending",
        "new_status": "approved",
        "payment_qr_code": "admin/qrcodes/merchant_M001_qr_1234567890.png",
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 13:00:00"
    }
}
```

**注意：** 
- 管理员密码：`yjsyjs_admin_2024`
- 拒绝提现时会自动退还余额给商户
- 状态值必须为：pending(待处理)、approved(已批准)、rejected(已拒绝)、completed(已完成)

---

### 20. 获取所有提现记录（管理员）

**接口地址：** `get_all_withdrawals.php`

**请求方式：** GET

**功能描述：** 管理员获取所有商户的提现记录

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| admin_password | string | 是 | 管理员密码 |

**返回示例：**
```json
{
    "status": "success",
    "message": "获取提现列表成功",
    "data": [
        {
            "withdrawal_id": 1,
            "merchant_id": "M001",
            "withdrawal_amount": "100.00",
            "withdrawal_status": "pending",
            "created_at": "2024-01-01 12:00:00",
            "updated_at": "2024-01-01 12:00:00",
            "shop_name": "测试商店",
            "payment_qr_code": "admin/qrcodes/merchant_M001_qr_1234567890.png"
        }
    ]
}
```

**注意：** 
- 管理员密码：`yjsyjs_admin_2024`
- 返回所有商户的提现记录，按创建时间倒序排列
- 包含商户名称和收款码信息

---

## 管理员页面

### 提现管理后台

**访问地址：** `admin/withdrawal_admin.php?admin_password=yjsyjs_admin_2024`

**功能特点：**
- 显示所有商户的提现申请
- 收款码缩略图显示，点击查看大图
- 状态更新功能（待处理/已批准/已拒绝/已完成）
- 自动刷新（每30秒）
- 美观的卡片式布局

**使用说明：**
1. 访问管理员页面时需要提供管理员密码
2. 可以查看所有提现申请的详细信息
3. 点击收款码缩略图可查看大图
4. 点击"更新状态"按钮可修改提现状态
5. 拒绝提现时会自动退还余额给商户

---

## 数据库导入

### 导入SQL文件

**接口地址：** `import_sql.php`

**功能：** 覆盖导入 `install.sql` 文件到数据库

**返回示例：**
```json
{
    "status": "success",
    "message": "数据库导入完成",
    "data": {
        "total_statements": 15,
        "success_count": 15,
        "error_count": 0,
        "errors": []
    }
}
```

---

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| success | 操作成功 |
| error | 操作失败 |
| warning | 操作完成但有警告 |

## 错误处理

所有API都会返回统一的错误格式：

```json
{
    "status": "error",
    "message": "错误描述信息",
    "data": null
}
```

## 注意事项

1. 所有API都使用GET方式传递参数
2. 商户秘钥用于身份验证，请妥善保管
3. 商品删除采用软删除方式，不会真正删除数据
4. 订单支付后会自动分配卡密并更新库存
5. 未支付订单的发货内容会被隐藏
6. 数据库配置在 `config.php` 文件中 