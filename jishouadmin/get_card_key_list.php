<?php
/**
 * API 5: 商品卡密列表
 * 所需参数: 商品ID
 * 返回: 该商品的所有卡密
 */

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$product_id = $_GET['product_id'] ?? '';

// 验证参数
if (empty($product_id) || !is_numeric($product_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品ID不能为空且必须为数字',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 查询卡密列表
    $stmt = $pdo->prepare("
        SELECT 
            card_key_id,
            merchant_id,
            product_id,
            product_name,
            card_key_content,
            card_key_status,
            order_id,
            created_at,
            updated_at
        FROM card_keys 
        WHERE product_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$product_id]);
    $card_keys = $stmt->fetchAll();
    
    if (empty($card_keys)) {
        echo json_encode([
            'status' => 'success',
            'message' => '该商品暂无卡密',
            'data' => []
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'status' => 'success',
            'message' => '获取卡密列表成功',
            'data' => $card_keys
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取卡密列表失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 