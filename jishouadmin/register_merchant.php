<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

$merchant_id = $_GET['merchant_id'] ?? '';

if (empty($merchant_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户ID不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 检查商户ID是否已存在
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM merchants WHERE merchant_id = ?");
    $stmt->execute([$merchant_id]);
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户ID已存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 根据商户ID生成商户秘钥（使用可逆算法）
    $merchant_secret = generateMerchantSecret($merchant_id);
    
    // 生成示例商户信息
    $shop_name = "商户" . $merchant_id . "的商店";
    $shop_description = "这是商户" . $merchant_id . "的商店，提供各种优质商品和服务。";
    $merchant_balance = 0.00;
    $payment_qr_code = "admin/qrcodes/merchant_" . $merchant_id . "_qr.png";
    
    // 插入商户信息
    $stmt = $pdo->prepare("INSERT INTO merchants (merchant_id, merchant_secret, shop_name, shop_description, merchant_balance, payment_qr_code) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([
        $merchant_id,
        $merchant_secret,
        $shop_name,
        $shop_description,
        $merchant_balance,
        $payment_qr_code
    ]);
    
    // 获取注册的商户信息
    $stmt = $pdo->prepare("SELECT * FROM merchants WHERE merchant_id = ?");
    $stmt->execute([$merchant_id]);
    $merchant = $stmt->fetch();
    
    $result = [
        'merchant_id' => $merchant['merchant_id'],
        'merchant_secret' => $merchant['merchant_secret'],
        'shop_name' => $merchant['shop_name'],
        'shop_description' => $merchant['shop_description'],
        'merchant_balance' => $merchant['merchant_balance'],
        'payment_qr_code' => $merchant['payment_qr_code'],
        'created_at' => $merchant['created_at']
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '商户注册成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户注册失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

// 根据商户ID生成商户秘钥（可逆算法）
function generateMerchantSecret($merchant_id) {
    // 使用固定的密钥和算法
    $key = "yjsyjs_merchant_secret_key_2024";
    $salt = "yjsyjs_salt_2024";
    
    // 组合商户ID、密钥和盐值
    $combined = $merchant_id . $key . $salt;
    
    // 使用MD5生成32位字符串
    $hash = md5($combined);
    
    // 添加商户ID前缀，确保唯一性
    $prefix = substr($merchant_id, 0, 3);
    
    // 组合成最终的商户秘钥
    $merchant_secret = $prefix . "_" . $hash;
    
    return $merchant_secret;
}

// 验证商户秘钥（可逆验证）
function verifyMerchantSecret($merchant_id, $merchant_secret) {
    $expected_secret = generateMerchantSecret($merchant_id);
    return $merchant_secret === $expected_secret;
}
?> 