<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

$merchant_secret = $_GET['merchant_secret'] ?? '';

if (empty($merchant_secret)) {
    die('缺少商户秘钥参数');
}

// 获取商户信息
require_once '../config.php';
$pdo = getDBConnection();

if (!$pdo) {
    die('数据库连接失败');
}

try {
    $stmt = $pdo->prepare("SELECT merchant_id, shop_name, shop_description, merchant_balance, payment_qr_code FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        die('商户不存在或秘钥错误');
    }
} catch (Exception $e) {
    die('获取商户信息失败: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户管理后台 - <?php echo htmlspecialchars($merchant['shop_name']); ?></title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部信息 -->
        <div class="header">
            <h1><i class="fas fa-store"></i> 商户管理后台</h1>
            <p>欢迎回来，<?php echo htmlspecialchars($merchant['shop_name']); ?> (<?php echo htmlspecialchars($merchant['merchant_id']); ?>)</p>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <a href="#" class="nav-tab active" data-tab="shop-info">
                <i class="fas fa-edit"></i> 修改商店信息
            </a>
            <a href="#" class="nav-tab" data-tab="product-management">
                <i class="fas fa-box"></i> 商店商品管理
            </a>
            <a href="#" class="nav-tab" data-tab="withdraw-management">
                <i class="fas fa-money-bill-wave"></i> 余额提现管理
            </a>
            <a href="#" class="nav-tab" data-tab="order-management">
                <i class="fas fa-list-alt"></i> 本店订单管理
            </a>
        </div>

        <!-- 商店信息标签页 -->
        <div id="shop-info" class="tab-content">
            <div class="content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-edit"></i> 修改商店信息</h3>
                    </div>
                    
                    <form id="shopInfoForm">
                        <div class="form-group">
                            <label for="shop_name">商店名称</label>
                            <input type="text" id="shop_name" name="shop_name" class="form-control" 
                                   value="<?php echo htmlspecialchars($merchant['shop_name']); ?>" 
                                   placeholder="请输入商店名称">
                        </div>
                        
                        <div class="form-group">
                            <label for="shop_description">商店介绍</label>
                            <textarea id="shop_description" name="shop_description" class="form-control" 
                                      rows="4" placeholder="请输入商店介绍"><?php echo htmlspecialchars($merchant['shop_description']); ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>收款码</label>
                            <div class="qr-code-container">
                                <img id="qrCodePreview" src="<?php echo htmlspecialchars($merchant['payment_qr_code']); ?>" 
                                     alt="收款码" class="qr-code-image" style="display: block;">
                            </div>
                            <div class="file-upload">
                                <input type="file" id="qrCodeFile" name="qr_code" accept="image/*" 
                                       data-preview="#qrCodePreview">
                                <label for="qrCodeFile" class="file-upload-label">
                                    <i class="fas fa-upload"></i> 上传新的收款码
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 商品管理标签页 -->
        <div id="product-management" class="tab-content" style="display: none;">
            <div class="content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-box"></i> 商店商品管理</h3>
                        <button class="btn btn-success" onclick="showAddProductModal()">
                            <i class="fas fa-plus"></i> 添加商品
                        </button>
                    </div>
                    
                    <div id="productList">
                        <div class="loading">加载中...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提现管理标签页 -->
        <div id="withdraw-management" class="tab-content" style="display: none;">
            <div class="content">
                <!-- 余额显示 -->
                <div class="balance-display">
                    <div class="balance-label">当前余额</div>
                    <div class="balance-amount">¥<?php echo number_format($merchant['merchant_balance'], 2); ?></div>
                </div>
                
                <!-- 提现表单 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-money-bill-wave"></i> 发起提现</h3>
                    </div>
                    
                    <form id="withdrawForm">
                        <div class="form-group">
                            <label for="withdrawal_amount">提现金额</label>
                            <input type="number" id="withdrawal_amount" name="withdrawal_amount" 
                                   class="form-control" step="0.01" min="5" 
                                   placeholder="请输入提现金额（最低5元）">
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> 发起提现
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- 提现历史 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-history"></i> 提现历史</h3>
                    </div>
                    
                    <div id="withdrawHistory">
                        <div class="loading">加载中...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单管理标签页 -->
        <div id="order-management" class="tab-content" style="display: none;">
            <div class="content">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-list-alt"></i> 本店订单管理</h3>
                    </div>
                    
                    <div id="orderList">
                        <div class="loading">加载中...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加商品模态框 -->
    <div id="addProductModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('addProductModal')">&times;</span>
            <h3><i class="fas fa-plus"></i> 添加商品</h3>
            
            <form id="addProductForm">
                <div class="form-group">
                    <label for="product_name">商品名称</label>
                    <input type="text" id="product_name" name="product_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="product_price">商品价格</label>
                    <input type="number" id="product_price" name="product_price" class="form-control" 
                           step="0.01" min="0" required>
                </div>
                
                <div class="form-group">
                    <label for="product_description">商品简介</label>
                    <textarea id="product_description" name="product_description" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-success">添加商品</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addProductModal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加卡密模态框 -->
    <div id="addCardKeyModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('addCardKeyModal')">&times;</span>
            <h3><i class="fas fa-key"></i> 添加卡密</h3>
            
            <form id="addCardKeyForm">
                <input type="hidden" id="cardKeyProductId" name="product_id">
                
                <div class="form-group">
                    <label for="card_key_content">卡密内容</label>
                    <textarea id="card_key_content" name="card_key_content" class="form-control" 
                              rows="5" placeholder="请输入卡密内容，多个卡密用-分割" required></textarea>
                    <small>例如：CARD001-XXXX-YYYY-ZZZZ-CARD002-XXXX-YYYY-ZZZZ</small>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-success">添加卡密</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addCardKeyModal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑商品模态框 -->
    <div id="editProductModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('editProductModal')">&times;</span>
            <h3><i class="fas fa-edit"></i> 编辑商品</h3>
            
            <form id="editProductForm">
                <input type="hidden" id="editProductId" name="product_id">
                
                <div class="form-group">
                    <label for="edit_product_name">商品名称</label>
                    <input type="text" id="edit_product_name" name="product_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_product_price">商品价格</label>
                    <input type="number" id="edit_product_price" name="product_price" class="form-control" 
                           step="0.01" min="0" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_product_description">商品简介</label>
                    <textarea id="edit_product_description" name="product_description" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">保存修改</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editProductModal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // 页面特定的JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // 加载商品列表
            loadProductList();
            
            // 加载提现历史
            loadWithdrawHistory();
            
            // 加载订单列表
            loadOrderList();
            
            // 绑定表单提交事件
            bindFormEvents();
        });

        // 绑定表单事件
        function bindFormEvents() {
            // 商店信息表单
            document.getElementById('shopInfoForm').addEventListener('submit', function(e) {
                e.preventDefault();
                updateShopInfo();
            });
            
            // 提现表单
            document.getElementById('withdrawForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitWithdraw();
            });
            
            // 添加商品表单
            document.getElementById('addProductForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addProduct();
            });
            
            // 添加卡密表单
            document.getElementById('addCardKeyForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addCardKeys();
            });
            
            // 编辑商品表单
            document.getElementById('editProductForm').addEventListener('submit', function(e) {
                e.preventDefault();
                editProduct();
            });
        }

        // 更新商店信息
        function updateShopInfo() {
            const shopName = document.getElementById('shop_name').value.trim();
            const shopDescription = document.getElementById('shop_description').value.trim();
            const fileInput = document.getElementById('qrCodeFile');
            const hasFile = fileInput.files.length > 0;

            // 先处理收款码上传
            if (hasFile) {
                const formData = new FormData();
                formData.append('merchant_secret', window.merchantSecret);
                formData.append('qr_code', fileInput.files[0]);
                fetch('upload_qr_code.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showAlert('收款码上传成功', 'success');
                        if (data.data.qr_code_url) {
                            document.getElementById('qrCodePreview').src = data.data.qr_code_url;
                        }
                        // 上传成功后再更新名称和介绍
                        updateShopNameDesc(shopName, shopDescription);
                    } else {
                        showAlert(data.message, 'error');
                    }
                })
                .catch(error => {
                    showAlert('上传失败: ' + error.message, 'error');
                });
            } else {
                // 只更新名称和介绍
                updateShopNameDesc(shopName, shopDescription);
            }
        }

        // 只更新名称和介绍
        function updateShopNameDesc(shopName, shopDescription) {
            // 只传递有值的字段
            const params = { merchant_secret: window.merchantSecret };
            if (shopName) params.shop_name = shopName;
            if (shopDescription) params.shop_description = shopDescription;

            // 如果没有要更新的内容，直接提示
            if (!params.shop_name && !params.shop_description) {
                showAlert('没有需要更新的信息', 'warning');
                return;
            }

            apiRequest('../edit_merchant_info.php', params)
            .then(data => {
                if (data.status === 'success') {
                    showAlert('商店信息更新成功', 'success');
                } else {
                    showAlert(data.message, 'error');
                }
            })
            .catch(error => {
                showAlert('更新失败: ' + error.message, 'error');
            });
        }

        // 加载商品列表
        function loadProductList() {
            const container = document.getElementById('productList');
            
            apiRequest('../get_product_list.php', { merchant_secret: window.merchantSecret })
            .then(response => {
                if (response.status === 'success') {
                    displayProductList(response.data);
                } else {
                    container.innerHTML = '<div class="alert alert-error">' + response.message + '</div>';
                }
            })
            .catch(error => {
                container.innerHTML = '<div class="alert alert-error">加载失败: ' + error.message + '</div>';
            });
        }

        // 显示商品列表
        function displayProductList(products) {
            const container = document.getElementById('productList');
            
            if (products.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">暂无商品</div>';
                return;
            }
            
            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>商品ID</th>
                            <th>商品名称</th>
                            <th>价格</th>
                            <th>库存</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            products.forEach(product => {
                html += `
                    <tr>
                        <td>${product.product_id}</td>
                        <td>${product.product_name}</td>
                        <td>¥${product.product_price}</td>
                        <td>${product.stock_quantity}</td>
                        <td><span class="status-badge status-${product.status}">${product.status}</span></td>
                        <td>
                            <button class="btn btn-warning btn-sm" onclick="showAddCardKeyModal(${product.product_id})">
                                <i class="fas fa-key"></i> 添加卡密
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="showEditProductModal(${product.product_id})">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="confirmDeleteProduct(${product.product_id}, '${product.product_name}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 加载提现历史
        function loadWithdrawHistory() {
            const container = document.getElementById('withdrawHistory');
            
            apiRequest('../get_withdrawal_list.php', { merchant_secret: window.merchantSecret })
            .then(response => {
                if (response.status === 'success') {
                    displayWithdrawHistory(response.data);
                } else {
                    container.innerHTML = '<div class="alert alert-error">' + response.message + '</div>';
                }
            })
            .catch(error => {
                container.innerHTML = '<div class="alert alert-error">加载失败: ' + error.message + '</div>';
            });
        }

        // 显示提现历史
        function displayWithdrawHistory(withdrawals) {
            const container = document.getElementById('withdrawHistory');
            
            if (withdrawals.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">暂无提现记录</div>';
                return;
            }
            
            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>提现ID</th>
                            <th>提现金额</th>
                            <th>提现时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            withdrawals.forEach(withdrawal => {
                html += `
                    <tr>
                        <td>${withdrawal.withdrawal_id}</td>
                        <td>¥${withdrawal.withdrawal_amount}</td>
                        <td>${formatDate(withdrawal.withdrawal_time)}</td>
                        <td><span class="status-badge status-${withdrawal.withdrawal_status}">${withdrawal.withdrawal_status}</span></td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 加载订单列表
        function loadOrderList() {
            const container = document.getElementById('orderList');
            
            apiRequest('../get_order_list.php', { merchant_secret: window.merchantSecret })
            .then(response => {
                if (response.status === 'success') {
                    displayOrderList(response.data);
                } else {
                    container.innerHTML = '<div class="alert alert-error">' + response.message + '</div>';
                }
            })
            .catch(error => {
                container.innerHTML = '<div class="alert alert-error">加载失败: ' + error.message + '</div>';
            });
        }

        // 显示订单列表
        function displayOrderList(orders) {
            const container = document.getElementById('orderList');
            
            if (orders.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">暂无订单</div>';
                return;
            }
            
            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>商品名称</th>
                            <th>价格</th>
                            <th>购买时间</th>
                            <th>状态</th>
                            <th>客户联系方式</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            orders.forEach(order => {
                html += `
                    <tr>
                        <td>${order.order_id}</td>
                        <td>${order.product_name}</td>
                        <td>¥${order.product_price}</td>
                        <td>${formatDate(order.purchase_time)}</td>
                        <td><span class="status-badge status-${order.order_status}">${order.order_status}</span></td>
                        <td>${order.customer_contact}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 提交提现
        function submitWithdraw() {
            const amount = document.getElementById('withdrawal_amount').value;
            
            if (parseFloat(amount) < 5) {
                showAlert('提现金额不能低于5元', 'error');
                return;
            }
            
            apiRequest('../merchant_withdraw.php', {
                merchant_secret: window.merchantSecret,
                withdrawal_amount: amount
            })
            .then(response => {
                if (response.status === 'success') {
                    showAlert('提现申请成功', 'success');
                    document.getElementById('withdrawForm').reset();
                    // 重新加载余额和提现历史
                    location.reload();
                } else {
                    showAlert(response.message, 'error');
                }
            })
            .catch(error => {
                showAlert('提现失败: ' + error.message, 'error');
            });
        }

        // 显示模态框
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 显示添加商品模态框
        function showAddProductModal() {
            showModal('addProductModal');
        }

        // 显示添加卡密模态框
        function showAddCardKeyModal(productId) {
            document.getElementById('cardKeyProductId').value = productId;
            showModal('addCardKeyModal');
        }

        // 显示编辑商品模态框
        function showEditProductModal(productId) {
            // 获取商品信息并填充表单
            apiRequest('../get_product_info.php', { product_id: productId })
            .then(response => {
                if (response.status === 'success') {
                    const product = response.data;
                    document.getElementById('editProductId').value = product.product_id;
                    document.getElementById('edit_product_name').value = product.product_name;
                    document.getElementById('edit_product_price').value = product.product_price;
                    document.getElementById('edit_product_description').value = product.product_description || '';
                    showModal('editProductModal');
                } else {
                    showAlert(response.message, 'error');
                }
            })
            .catch(error => {
                showAlert('获取商品信息失败: ' + error.message, 'error');
            });
        }

        // 添加商品
        function addProduct() {
            const formData = new FormData(document.getElementById('addProductForm'));
            formData.append('merchant_secret', window.merchantSecret);
            
            apiRequest('../add_product.php', Object.fromEntries(formData))
            .then(response => {
                if (response.status === 'success') {
                    showAlert('商品添加成功', 'success');
                    closeModal('addProductModal');
                    document.getElementById('addProductForm').reset();
                    loadProductList();
                } else {
                    showAlert(response.message, 'error');
                }
            })
            .catch(error => {
                showAlert('添加失败: ' + error.message, 'error');
            });
        }

        // 添加卡密
        function addCardKeys() {
            const formData = new FormData(document.getElementById('addCardKeyForm'));
            formData.append('merchant_secret', window.merchantSecret);
            
            apiRequest('../add_card_keys.php', Object.fromEntries(formData))
            .then(response => {
                if (response.status === 'success') {
                    showAlert('卡密添加成功', 'success');
                    closeModal('addCardKeyModal');
                    document.getElementById('addCardKeyForm').reset();
                    loadProductList();
                } else {
                    showAlert(response.message, 'error');
                }
            })
            .catch(error => {
                showAlert('添加失败: ' + error.message, 'error');
            });
        }

        // 编辑商品
        function editProduct() {
            const formData = new FormData(document.getElementById('editProductForm'));
            formData.append('merchant_secret', window.merchantSecret);
            
            apiRequest('../edit_product.php', Object.fromEntries(formData))
            .then(response => {
                if (response.status === 'success') {
                    showAlert('商品信息更新成功', 'success');
                    closeModal('editProductModal');
                    loadProductList();
                } else {
                    showAlert(response.message, 'error');
                }
            })
            .catch(error => {
                showAlert('更新失败: ' + error.message, 'error');
            });
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        function confirmDeleteProduct(productId, productName) {
            if (confirm(`确定要删除商品【${productName}】（ID:${productId}）吗？此操作不可恢复！`)) {
                deleteProduct(productId);
            }
        }

        function deleteProduct(productId) {
            apiRequest('../delete_product.php', {
                merchant_secret: window.merchantSecret,
                product_id: productId
            })
            .then(response => {
                if (response.status === 'success') {
                    showAlert('商品删除成功', 'success');
                    loadProductList();
                } else {
                    showAlert(response.message, 'error');
                }
            })
            .catch(error => {
                showAlert('删除失败: ' + error.message, 'error');
            });
        }
    </script>
</body>
</html> 