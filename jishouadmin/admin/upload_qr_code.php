<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');
require_once '../config.php';

$merchant_secret = $_POST['merchant_secret'] ?? '';

if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查是否有文件上传
if (!isset($_FILES['qr_code']) || $_FILES['qr_code']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode([
        'status' => 'error',
        'message' => '文件上传失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$file = $_FILES['qr_code'];
$fileName = $file['name'];
$fileSize = $file['size'];
$fileTmp = $file['tmp_name'];
$fileType = $file['type'];

// 检查文件类型
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
if (!in_array($fileType, $allowedTypes)) {
    echo json_encode([
        'status' => 'error',
        'message' => '只允许上传图片文件 (JPG, PNG, GIF)',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查文件大小 (最大5MB)
if ($fileSize > 5 * 1024 * 1024) {
    echo json_encode([
        'status' => 'error',
        'message' => '文件大小不能超过5MB',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 根据商户秘钥查找商户
    $stmt = $pdo->prepare("SELECT merchant_id, payment_qr_code FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户不存在或秘钥错误',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 生成新的文件名
    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
    $newFileName = 'merchant_' . $merchant['merchant_id'] . '_qr_' . time() . '.' . $fileExtension;
    $uploadPath = 'admin/qrcodes/' . $newFileName;
    $fullPath = __DIR__ . '/../' . $uploadPath;
    
    // 确保目录存在
    $uploadDir = __DIR__ . '/../admin/qrcodes';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // 移动上传的文件
    if (!move_uploaded_file($fileTmp, $fullPath)) {
        echo json_encode([
            'status' => 'error',
            'message' => '文件保存失败',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 删除旧文件（如果存在且不是默认文件）
    $oldQrCode = $merchant['payment_qr_code'];
    if (!empty($oldQrCode) && $oldQrCode !== 'admin/qrcodes/default_qr.png') {
        $oldPath = __DIR__ . '/../' . $oldQrCode;
        if (file_exists($oldPath)) {
            unlink($oldPath);
        }
    }
    
    // 更新数据库
    $stmt = $pdo->prepare("UPDATE merchants SET payment_qr_code = ?, updated_at = NOW() WHERE merchant_secret = ?");
    $stmt->execute([$uploadPath, $merchant_secret]);
    
    // 获取更新后的商户信息
    $stmt = $pdo->prepare("SELECT merchant_id, shop_name, shop_description, merchant_balance, payment_qr_code, created_at, updated_at FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $updated_merchant = $stmt->fetch();
    
    $result = [
        'merchant_id' => $updated_merchant['merchant_id'],
        'shop_name' => $updated_merchant['shop_name'],
        'shop_description' => $updated_merchant['shop_description'],
        'merchant_balance' => $updated_merchant['merchant_balance'],
        'payment_qr_code' => $updated_merchant['payment_qr_code'],
        'created_at' => $updated_merchant['created_at'],
        'updated_at' => $updated_merchant['updated_at'],
        'qr_code_url' => $uploadPath
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '收款码上传成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '上传收款码失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 