<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多商户商品管理系统 API 文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
            color: white;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* 新增链接区域 */
        .links-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #FF9500;
        }

        .links-section h3 {
            color: #FF9500;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .link-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .link-item:last-child {
            margin-bottom: 0;
        }

        .link-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .link-url {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            color: #007AFF;
            margin-bottom: 8px;
            word-break: break-all;
        }

        .link-desc {
            font-size: 14px;
            color: #666;
        }

        /* 导航 */
        .nav {
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .nav h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3rem;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .nav-item {
            padding: 15px;
            border-radius: 8px;
            text-decoration: none;
            color: #666;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
            display: block;
        }

        .nav-item:hover {
            background: #007AFF;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
        }

        /* API 章节 */
        .api-section {
            background: white;
            margin-bottom: 30px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .api-header {
            background: #007AFF;
            color: white;
            padding: 20px 30px;
        }

        .api-header h2 {
            font-size: 1.4rem;
            margin-bottom: 8px;
        }

        .api-header .endpoint {
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 12px;
            border-radius: 6px;
            display: inline-block;
            font-size: 14px;
        }

        .method-badge {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }

        .api-content {
            padding: 30px;
        }

        .api-content h4 {
            color: #333;
            margin: 20px 0 15px 0;
            font-size: 1.1rem;
        }

        .api-content p {
            margin-bottom: 15px;
            color: #666;
        }

        /* 表格 */
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        tr:last-child td {
            border-bottom: none;
        }

        .required {
            color: #dc3545;
            font-weight: bold;
            background: rgba(220, 53, 69, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        .optional {
            color: #6c757d;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* 代码块 */
        .code-block {
            background: #2d3748;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #e2e8f0;
        }

        /* 提示框 */
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid #FF9500;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .note h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .note p {
            color: #856404;
            margin-bottom: 8px;
        }

        /* 响应参数表格 */
        .response-table {
            margin-top: 15px;
        }

        .response-table th {
            background: #e3f2fd;
            color: #1976d2;
        }

        /* 页脚 */
        .footer {
            text-align: center;
            padding: 40px 0;
            color: #666;
            border-top: 1px solid #e9ecef;
            margin-top: 50px;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav-grid {
                grid-template-columns: 1fr;
            }

            .api-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>多商户商品管理系统</h1>
            <p>企业级 API 接口文档</p>
        </div>

        <!-- 新增的链接部分 -->
        <div class="links-section">
            <h3>🔗 重要链接</h3>
            <div class="link-item">
                <div class="link-title">商户管理页面</div>
                <div class="link-url">https://cloudshop.qnm6.top/admin/index.php?merchant_secret=s商户秘钥</div>
                <div class="link-desc">需要替换 "s商户秘钥" 为实际的商户秘钥</div>
            </div>
            <div class="link-item">
                <div class="link-title">商户商城首页</div>
                <div class="link-url">https://cloudshop.qnm6.top/shop.php</div>
                <div class="link-desc">
                    <strong>方法1:</strong> GET ?sj=商户ID (访问某个商家店铺)<br>
                    <strong>方法2:</strong> GET ?sp=商品ID (访问某个商品详情)
                </div>
            </div>
        </div>

        <div class="nav">
            <h3>📋 API 接口导航</h3>
            <div class="nav-grid">
                <a href="#register-merchant" class="nav-item">注册商户</a>
                <a href="#get-merchant-info" class="nav-item">获取商户信息</a>
                <a href="#verify-merchant-secret" class="nav-item">验证商户秘钥</a>
                <a href="#merchant-withdraw" class="nav-item">商户发起提现</a>
                <a href="#get-withdrawal-list" class="nav-item">商户提现列表</a>
                <a href="#get-product-list" class="nav-item">商户商品列表</a>
                <a href="#get-card-key-list" class="nav-item">商品卡密列表</a>
                <a href="#get-order-list" class="nav-item">商户订单列表</a>
                <a href="#add-product" class="nav-item">新增商品</a>
                <a href="#add-card-keys" class="nav-item">增加商品卡密</a>
                <a href="#edit-product" class="nav-item">编辑商品</a>
                <a href="#delete-product" class="nav-item">删除商品</a>
                <a href="#get-order-detail" class="nav-item">获取订单内容</a>
                <a href="#get-product-info" class="nav-item">商品信息查询</a>
                <a href="#create-order" class="nav-item">发起订单</a>
                <a href="#check-payment-status" class="nav-item">检查支付状态</a>
                <a href="#edit-merchant-info" class="nav-item">编辑商户信息</a>
            </div>
        </div>

        <!-- 系统概述 -->
        <div class="api-section">
            <div class="api-header">
                <h2><i class="fas fa-info-circle"></i> 系统概述</h2>
            </div>
            <div class="api-content">
                <p><strong>多商户商品管理系统</strong>是一个功能完善的企业级电商平台，支持多商户入驻、商品管理、订单处理和支付集成。</p>

                <h4><i class="fas fa-star"></i> 核心特性</h4>
                <ul>
                    <li><strong>多商户支持：</strong>支持无限商户入驻，独立管理各自商品和订单</li>
                    <li><strong>安全认证：</strong>基于商户秘钥的安全认证机制</li>
                    <li><strong>自动化处理：</strong>支付成功后自动发货、库存管理</li>
                    <li><strong>实时监控：</strong>订单状态实时更新，支付状态自动检测</li>
                </ul>

                <h4><i class="fas fa-shield-alt"></i> 安全须知</h4>
                <div class="note">
                    <h4>重要提醒</h4>
                    <p>• 入驻本系统前，请先注册商户！务必牢记商户秘钥以及商户ID</p>
                    <p>• 切记勿向外透露本系统的商户秘钥（商户ID对外，商户秘钥对内）</p>
                    <p>• 建议定期更换商户秘钥，确保账户安全</p>
                </div>
            </div>
        </div>

        <!-- 注册商户 -->
        <div class="api-section" id="register-merchant">
            <div class="api-header">
                <h2><i class="fas fa-user-plus"></i> 注册商户</h2>
                <div class="endpoint"><span class="method-badge">GET</span> https://cloudshop.qnm6.top/register_merchant.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>根据商户ID自动生成商户信息并注册到系统中</p>
                
                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_id</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户ID，用于生成唯一的商户标识</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h4><i class="fas fa-lock"></i> 商户秘钥生成算法</h4>
                <div class="note">
                    <h4>算法说明</h4>
                    <p>1. 使用固定的密钥和盐值</p>
                    <p>2. 组合：商户ID + 密钥 + 盐值</p>
                    <p>3. MD5加密生成32位哈希</p>
                    <p>4. 格式：商户ID前缀_哈希值</p>
                </div>

                <h4><i class="fas fa-check-circle"></i> 成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "商户注册成功",
    "data": {
        "merchant_id": "M001",
        "merchant_secret": "M00_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
        "shop_name": "商户M001的商店",
        "shop_description": "这是商户M001的商店，提供各种优质商品和服务。",
        "merchant_balance": "0.00",
        "payment_qr_code": "admin/qrcodes/merchant_M001_qr.png",
        "created_at": "2024-01-01 12:00:00"
    }
}</pre>
                </div>
                
                <h4>📊 返回参数说明</h4>
                <div class="table-container response-table">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>status</td>
                                <td>string</td>
                                <td>响应状态：success(成功) / error(失败)</td>
                            </tr>
                            <tr>
                                <td>message</td>
                                <td>string</td>
                                <td>响应消息描述</td>
                            </tr>
                            <tr>
                                <td>data.merchant_id</td>
                                <td>string</td>
                                <td>商户ID，用于对外展示</td>
                            </tr>
                            <tr>
                                <td>data.merchant_secret</td>
                                <td>string</td>
                                <td>商户秘钥，用于API认证（请妥善保管）</td>
                            </tr>
                            <tr>
                                <td>data.shop_name</td>
                                <td>string</td>
                                <td>商店名称</td>
                            </tr>
                            <tr>
                                <td>data.shop_description</td>
                                <td>string</td>
                                <td>商店描述</td>
                            </tr>
                            <tr>
                                <td>data.merchant_balance</td>
                                <td>string</td>
                                <td>商户余额（初始为0.00）</td>
                            </tr>
                            <tr>
                                <td>data.payment_qr_code</td>
                                <td>string</td>
                                <td>支付二维码图片路径</td>
                            </tr>
                            <tr>
                                <td>data.created_at</td>
                                <td>string</td>
                                <td>创建时间</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>❌ 错误响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "error",
    "message": "商户ID已存在",
    "data": null
}</pre>
                </div>
            </div>
        </div>

        <!-- 获取商户信息 -->
        <div class="api-section" id="get-merchant-info">
            <div class="api-header">
                <h2><i class="fas fa-info-circle"></i> 获取商户信息</h2>
                <div class="endpoint"><span class="method-badge">GET</span> https://cloudshop.qnm6.top/get_merchant_info.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>根据商户ID获取商户详细信息（不包含商户秘钥）</p>

                <h4><i class="fas fa-cogs"></i> 请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_id</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户ID</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>✅ 成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "获取商户信息成功",
    "data": {
        "shop_name": "测试商店1",
        "shop_description": "这是一个测试商店",
        "merchant_balance": "1000.00",
        "payment_qr_code": "qrcodes/shop1_qr.png",
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
    }
}</pre>
                </div>

                <h4>📊 返回参数说明</h4>
                <div class="table-container response-table">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>status</td>
                                <td>string</td>
                                <td>响应状态：success(成功) / error(失败)</td>
                            </tr>
                            <tr>
                                <td>message</td>
                                <td>string</td>
                                <td>响应消息描述</td>
                            </tr>
                            <tr>
                                <td>data.shop_name</td>
                                <td>string</td>
                                <td>商店名称</td>
                            </tr>
                            <tr>
                                <td>data.shop_description</td>
                                <td>string</td>
                                <td>商店描述</td>
                            </tr>
                            <tr>
                                <td>data.merchant_balance</td>
                                <td>string</td>
                                <td>商户当前余额</td>
                            </tr>
                            <tr>
                                <td>data.payment_qr_code</td>
                                <td>string</td>
                                <td>支付二维码图片路径</td>
                            </tr>
                            <tr>
                                <td>data.created_at</td>
                                <td>string</td>
                                <td>商户创建时间</td>
                            </tr>
                            <tr>
                                <td>data.updated_at</td>
                                <td>string</td>
                                <td>最后更新时间</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 验证商户秘钥 -->
        <div class="api-section" id="verify-merchant-secret">
            <div class="api-header">
                <h2><i class="fas fa-key"></i> 验证商户秘钥</h2>
                <div class="endpoint"><span class="method-badge">GET</span> https://cloudshop.qnm6.top/verify_merchant_secret.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>验证商户秘钥是否正确</p>

                <h4><i class="fas fa-cogs"></i> 请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_id</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户ID</td>
                            </tr>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4><i class="fas fa-check-circle"></i> 成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "商户秘钥验证成功",
    "data": {
        "merchant_id": "M003",
        "provided_secret": "M00_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
        "expected_secret": "M00_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
        "is_valid": true
    }
}</pre>
                </div>
            </div>
        </div>

        <!-- 商户发起提现 -->
        <div class="api-section" id="merchant-withdraw">
            <div class="api-header">
                <h2><i class="fas fa-money-bill-wave"></i> 商户发起提现</h2>
                <div class="endpoint"><span class="method-badge">GET</span> https://cloudshop.qnm6.top/merchant_withdraw.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>商户发起提现申请，系统会扣除相应余额并创建提现记录</p>

                <h4><i class="fas fa-cogs"></i> 请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                            <tr>
                                <td>withdrawal_amount</td>
                                <td>decimal</td>
                                <td><span class="required">是</span></td>
                                <td>提现金额，必须大于0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4><i class="fas fa-check-circle"></i> 成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "提现申请成功",
    "data": {
        "withdrawal_id": 1,
        "merchant_id": "M001",
        "withdrawal_amount": "100.00",
        "original_balance": "1000.00",
        "fee_amount": "0.00",
        "current_balance": "900.00"
    }
}</pre>
                </div>

                <div class="note">
                    <h4>注意事项</h4>
                    <p>• 提现金额不能超过当前余额</p>
                    <p>• 提现申请创建后状态为"pending"，需要管理员审核</p>
                    <p>• 系统会立即扣除相应余额</p>
                </div>
            </div>
        </div>

        <!-- 商户提现列表 -->
        <div class="api-section" id="get-withdrawal-list">
            <div class="api-header">
                <h2><i class="fas fa-list-alt"></i> 商户提现列表</h2>
                <div class="endpoint"><span class="method-badge">GET</span> https://cloudshop.qnm6.top/get_withdrawal_list.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>获取指定商户的所有提现记录</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "获取提现列表成功",
    "data": [
        {
            "withdrawal_id": 1,
            "merchant_id": "M001",
            "withdrawal_amount": "100.00",
            "withdrawal_status": "pending",
            "created_at": "2024-01-01 12:00:00",
            "updated_at": "2024-01-01 12:00:00"
        }
    ]
}</pre>
                </div>
            </div>
        </div>

        <!-- 商户商品列表 -->
        <div class="api-section" id="get-product-list">
            <div class="api-header">
                <h2><i class="fas fa-box"></i> 商户商品列表</h2>
                <div class="endpoint"><span class="method-badge">GET</span> https://cloudshop.qnm6.top/get_product_list.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>获取指定商户的所有商品列表</p>

                <h4><i class="fas fa-cogs"></i> 请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "获取商品列表成功",
    "data": [
        {
            "product_id": 1,
            "merchant_id": "M001",
            "product_name": "测试商品",
            "product_price": "99.00",
            "product_description": "这是一个测试商品",
            "stock_quantity": 10,
            "status": "active",
            "created_at": "2024-01-01 12:00:00",
            "updated_at": "2024-01-01 12:00:00"
        }
    ]
}</pre>
                </div>
            </div>
        </div>

        <!-- 商品卡密列表 -->
        <div class="api-section" id="get-card-key-list">
            <div class="api-header">
                <h2>商品卡密列表</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/get_card_key_list.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>获取指定商品的所有卡密列表</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>product_id</td>
                                <td>int</td>
                                <td><span class="required">是</span></td>
                                <td>商品ID</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "获取卡密列表成功",
    "data": [
        {
            "card_key_id": 1,
            "product_name": "测试商品",
            "card_key_content": "CARD001-XXXX-YYYY-ZZZZ",
            "card_key_status": "unsold",
            "order_id": null,
            "created_at": "2024-01-01 12:00:00"
        }
    ]
}</pre>
                </div>
            </div>
        </div>

        <!-- 商户订单列表 -->
        <div class="api-section" id="get-order-list">
            <div class="api-header">
                <h2>商户订单列表</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/get_order_list.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>获取指定商户的所有订单列表</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "获取订单列表成功",
    "data": [
        {
            "order_id": "ORDER123456789",
            "merchant_id": "M001",
            "product_name": "测试商品",
            "product_price": "99.00",
            "delivery_content": "CARD001-XXXX-YYYY-ZZZZ",
            "order_status": "paid",
            "customer_contact": "<EMAIL>",
            "created_at": "2024-01-01 12:00:00",
            "updated_at": "2024-01-01 12:00:00"
        }
    ]
}</pre>
                </div>
            </div>
        </div>

        <!-- 新增商品 -->
        <div class="api-section" id="add-product">
            <div class="api-header">
                <h2><i class="fas fa-plus-circle"></i> 新增商品</h2>
                <div class="endpoint"><span class="method-badge">GET</span> https://cloudshop.qnm6.top/add_product.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>为指定商户添加新商品</p>

                <h4><i class="fas fa-cogs"></i> 请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                            <tr>
                                <td>product_name</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商品名称</td>
                            </tr>
                            <tr>
                                <td>product_price</td>
                                <td>decimal</td>
                                <td><span class="required">是</span></td>
                                <td>商品价格，必须大于0</td>
                            </tr>
                            <tr>
                                <td>product_description</td>
                                <td>string</td>
                                <td><span class="optional">否</span></td>
                                <td>商品描述</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "商品添加成功",
    "data": {
        "product_id": 1,
        "merchant_id": "M001",
        "product_name": "新商品",
        "product_price": "99.00",
        "product_description": "这是一个新商品",
        "stock_quantity": 0,
        "status": "active",
        "created_at": "2024-01-01 12:00:00"
    }
}</pre>
                </div>
            </div>
        </div>

        <!-- 增加商品卡密 -->
        <div class="api-section" id="add-card-keys">
            <div class="api-header">
                <h2>增加商品卡密</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/add_card_keys.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>为指定商品批量添加卡密</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                            <tr>
                                <td>product_id</td>
                                <td>int</td>
                                <td><span class="required">是</span></td>
                                <td>商品ID</td>
                            </tr>
                            <tr>
                                <td>card_key_content</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>卡密内容，多个卡密用"-"分隔</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "卡密添加成功",
    "data": {
        "product_id": 1,
        "product_name": "测试商品",
        "added_count": 2,
        "new_stock_quantity": 12
    }
}</pre>
                </div>

                <div class="note">
                    <h4>卡密格式说明</h4>
                    <p>多个卡密使用"-"分隔，例如：CARD001-CARD002-CARD003</p>
                </div>
            </div>
        </div>

        <!-- 编辑商品 -->
        <div class="api-section" id="edit-product">
            <div class="api-header">
                <h2>编辑商品</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/edit_product.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>编辑指定商品的信息</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                            <tr>
                                <td>product_id</td>
                                <td>int</td>
                                <td><span class="required">是</span></td>
                                <td>商品ID</td>
                            </tr>
                            <tr>
                                <td>product_name</td>
                                <td>string</td>
                                <td><span class="optional">否</span></td>
                                <td>新的商品名称</td>
                            </tr>
                            <tr>
                                <td>product_price</td>
                                <td>decimal</td>
                                <td><span class="optional">否</span></td>
                                <td>新的商品价格</td>
                            </tr>
                            <tr>
                                <td>product_description</td>
                                <td>string</td>
                                <td><span class="optional">否</span></td>
                                <td>新的商品描述</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "商品更新成功",
    "data": {
        "product_id": 1,
        "updated_fields": ["product_name", "product_price"],
        "product_name": "更新后的商品名称",
        "product_price": "199.00"
    }
}</pre>
                </div>
            </div>
        </div>

        <!-- 删除商品 -->
        <div class="api-section" id="delete-product">
            <div class="api-header">
                <h2>删除商品</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/delete_product.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>软删除指定商品（标记为已删除状态）</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                            <tr>
                                <td>product_id</td>
                                <td>int</td>
                                <td><span class="required">是</span></td>
                                <td>商品ID</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "商品删除成功",
    "data": {
        "product_id": 1,
        "product_name": "已删除的商品",
        "status": "deleted"
    }
}</pre>
                </div>

                <div class="note">
                    <h4>注意事项</h4>
                    <p>• 删除操作为软删除，商品状态会被标记为"deleted"</p>
                    <p>• 已删除的商品不会在商品列表中显示</p>
                    <p>• 数据不会被真正删除，可以通过数据库恢复</p>
                </div>
            </div>
        </div>

        <!-- 获取订单内容 -->
        <div class="api-section" id="get-order-detail">
            <div class="api-header">
                <h2>获取订单内容</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/get_order_detail.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>获取指定订单的详细信息</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>order_id</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>订单ID</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "获取订单详情成功",
    "data": {
        "order_id": "ORDER123456789",
        "merchant_id": "M001",
        "product_name": "测试商品",
        "product_price": "99.00",
        "delivery_content": "CARD001-XXXX-YYYY-ZZZZ",
        "order_status": "paid",
        "customer_contact": "<EMAIL>",
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
    }
}</pre>
                </div>
            </div>
        </div>

        <!-- 商品信息查询 -->
        <div class="api-section" id="get-product-info">
            <div class="api-header">
                <h2>商品信息查询</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/get_product_info.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>获取指定商品的详细信息</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>product_id</td>
                                <td>int</td>
                                <td><span class="required">是</span></td>
                                <td>商品ID</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "获取商品信息成功",
    "data": {
        "product_id": 1,
        "merchant_id": "M001",
        "product_name": "测试商品",
        "product_price": "99.00",
        "product_description": "这是一个测试商品",
        "stock_quantity": 10,
        "status": "active",
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
    }
}</pre>
                </div>
            </div>
        </div>

        <!-- 发起订单 -->
        <div class="api-section" id="create-order">
            <div class="api-header">
                <h2><i class="fas fa-shopping-cart"></i> 发起订单</h2>
                <div class="endpoint"><span class="method-badge">GET</span> https://cloudshop.qnm6.top/create_order.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>创建订单并生成支付链接</p>

                <h4><i class="fas fa-cogs"></i> 请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>customer_contact</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>客户联系方式</td>
                            </tr>
                            <tr>
                                <td>product_id</td>
                                <td>int</td>
                                <td><span class="required">是</span></td>
                                <td>商品ID</td>
                            </tr>
                            <tr>
                                <td>pay_type</td>
                                <td>string</td>
                                <td><span class="optional">否</span></td>
                                <td>支付方式：wxpay(微信支付) 或 alipay(支付宝)，默认wxpay</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>✅ 成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "订单创建成功",
    "data": {
        "order_info": {
            "order_id": "ORDER123456789",
            "merchant_id": "M001",
            "product_name": "测试商品",
            "product_price": "104.94",
            "delivery_content": "*******",
            "order_status": "unpaid",
            "customer_contact": "<EMAIL>"
        },
        "payment_url": "https://aftpay.k8s2024.com/submit.php?pid=2222&type=wxpay&...",
        "pay_type": "wxpay"
    }
}</pre>
                </div>

                <h4>📊 返回参数说明</h4>
                <div class="table-container response-table">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>status</td>
                                <td>string</td>
                                <td>响应状态：success(成功) / error(失败)</td>
                            </tr>
                            <tr>
                                <td>message</td>
                                <td>string</td>
                                <td>响应消息描述</td>
                            </tr>
                            <tr>
                                <td>data.order_info.order_id</td>
                                <td>string</td>
                                <td>订单ID，用于后续查询和支付状态检查</td>
                            </tr>
                            <tr>
                                <td>data.order_info.merchant_id</td>
                                <td>string</td>
                                <td>商户ID</td>
                            </tr>
                            <tr>
                                <td>data.order_info.product_name</td>
                                <td>string</td>
                                <td>商品名称</td>
                            </tr>
                            <tr>
                                <td>data.order_info.product_price</td>
                                <td>string</td>
                                <td>商品价格（含手续费）</td>
                            </tr>
                            <tr>
                                <td>data.order_info.delivery_content</td>
                                <td>string</td>
                                <td>发货内容（未支付时显示为*******）</td>
                            </tr>
                            <tr>
                                <td>data.order_info.order_status</td>
                                <td>string</td>
                                <td>订单状态：unpaid(未支付) / paid(已支付)</td>
                            </tr>
                            <tr>
                                <td>data.order_info.customer_contact</td>
                                <td>string</td>
                                <td>客户联系方式</td>
                            </tr>
                            <tr>
                                <td>data.payment_url</td>
                                <td>string</td>
                                <td>支付链接，用户需要访问此链接完成支付</td>
                            </tr>
                            <tr>
                                <td>data.pay_type</td>
                                <td>string</td>
                                <td>支付方式：wxpay(微信支付) / alipay(支付宝)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="note">
                    <h4>💡 价格计算说明</h4>
                    <p>• 最终价格 = 商品原价 × 1.06（6%手续费加成）</p>
                    <p>• 未支付订单的发货内容会被隐藏显示为"*******"</p>
                    <p>• 支付成功后会自动分配卡密并更新库存</p>
                </div>
            </div>
        </div>

        <!-- 检查支付状态 -->
        <div class="api-section" id="check-payment-status">
            <div class="api-header">
                <h2>检查支付状态</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/check_payment_status.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>检查订单支付状态，如果支付成功会自动更新订单状态并分配卡密</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>order_id</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>订单ID</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>支付成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "支付成功，订单已更新",
    "data": {
        "order_id": "ORDER123456789",
        "merchant_id": "M001",
        "product_name": "测试商品",
        "product_price": "104.94",
        "delivery_content": "CARD001-XXXX-YYYY-ZZZZ",
        "order_status": "paid",
        "customer_contact": "<EMAIL>",
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:05:00"
    }
}</pre>
                </div>

                <h4>未支付响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "订单未支付",
    "data": {
        "order_id": "ORDER123456789",
        "order_status": "unpaid",
        "delivery_content": "*******"
    }
}</pre>
                </div>

                <div class="note">
                    <h4>自动处理流程</h4>
                    <p>• 调用第三方支付接口验证支付状态</p>
                    <p>• 支付成功后自动分配可用卡密</p>
                    <p>• 更新商品库存数量</p>
                    <p>• 增加商户余额</p>
                </div>
            </div>
        </div>

        <!-- 编辑商户信息 -->
        <div class="api-section" id="edit-merchant-info">
            <div class="api-header">
                <h2>编辑商户信息</h2>
                <div class="endpoint">GET https://cloudshop.qnm6.top/edit_merchant_info.php</div>
            </div>
            <div class="api-content">
                <p><strong>功能描述：</strong>编辑商户的基本信息</p>

                <h4>请求参数</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>merchant_secret</td>
                                <td>string</td>
                                <td><span class="required">是</span></td>
                                <td>商户秘钥</td>
                            </tr>
                            <tr>
                                <td>shop_name</td>
                                <td>string</td>
                                <td><span class="optional">否</span></td>
                                <td>新的商店名称</td>
                            </tr>
                            <tr>
                                <td>shop_description</td>
                                <td>string</td>
                                <td><span class="optional">否</span></td>
                                <td>新的商店描述</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>成功响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "success",
    "message": "商户信息更新成功",
    "data": {
        "merchant_id": "M001",
        "updated_fields": ["shop_name", "shop_description"],
        "shop_name": "新的商店名称",
        "shop_description": "新的商店描述"
    }
}</pre>
                </div>
            </div>
        </div>

        <!-- 通用说明 -->
        <div class="api-section">
            <div class="api-header">
                <h2>通用说明</h2>
            </div>
            <div class="api-content">
                <h4>响应格式</h4>
                <p>所有API都返回统一的JSON格式：</p>
                <div class="code-block">
                    <pre>{
    "status": "success|error",
    "message": "操作结果描述",
    "data": "具体数据内容或null"
}</pre>
                </div>

                <h4>状态码说明</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>状态码</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="status-success">success</span></td>
                                <td>操作成功</td>
                            </tr>
                            <tr>
                                <td><span class="status-error">error</span></td>
                                <td>操作失败</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>错误响应示例</h4>
                <div class="code-block">
                    <pre>{
    "status": "error",
    "message": "具体错误描述信息",
    "data": null
}</pre>
                </div>

                <h4>常见错误类型</h4>
                <ul>
                    <li><strong>参数错误：</strong>缺少必填参数或参数格式不正确</li>
                    <li><strong>身份验证失败：</strong>商户秘钥无效或不存在</li>
                    <li><strong>权限不足：</strong>商户无权限操作指定资源</li>
                    <li><strong>资源不存在：</strong>请求的商品、订单等不存在</li>
                    <li><strong>业务逻辑错误：</strong>余额不足、库存不足等</li>
                    <li><strong>系统错误：</strong>数据库连接失败等系统级错误</li>
                </ul>

                <h4>安全注意事项</h4>
                <div class="note">
                    <h4>重要提醒</h4>
                    <p>• 商户秘钥是敏感信息，请妥善保管，不要在客户端暴露</p>
                    <p>• 建议在服务端调用API，避免在前端直接调用</p>
                    <p>• 定期检查API调用日志，监控异常访问</p>
                    <p>• 所有金额相关操作都有事务保护，确保数据一致性</p>
                </div>

                <h4>技术支持</h4>
                <p>如有技术问题，请联系系统管理员或查看系统日志文件。</p>
                <p>日志文件位置：<code>/log/</code> 目录下，按日期分类存储。</p>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 多商户商品管理系统 API 文档</p>
            <p>版本：v1.0 | 最后更新：2024年8月18日</p>
        </div>
    </div>

    <script>
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 100; // 考虑固定导航栏高度
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // 高亮当前导航项
        function highlightCurrentSection() {
            const sections = document.querySelectorAll('.api-section');
            const navLinks = document.querySelectorAll('.nav-item');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (window.pageYOffset >= sectionTop - 150) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        }

        window.addEventListener('scroll', highlightCurrentSection);
        window.addEventListener('load', highlightCurrentSection);
    </script>
</body>
</html>
