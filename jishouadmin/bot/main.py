#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import requests
import hashlib
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
import json
import re

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# 配置
BOT_TOKEN = "8171182904:AAEuvB3WauARBlLAeuuBBP1DjGj_s3ebCzY"
API_BASE_URL = "https://cloudshop.qnm6.top/"
BOT_USERNAME = "MikaJishouBot"  # 机器人用户名

# 商户秘钥生成算法（与PHP版本保持一致）
def generate_merchant_secret(merchant_id):
    """根据商户ID生成商户秘钥"""
    key = "yjsyjs_merchant_secret_key_2024"
    salt = "yjsyjs_salt_2024"
    
    # 组合商户ID、密钥和盐值
    combined = merchant_id + key + salt
    
    # 使用MD5生成32位字符串
    hash_value = hashlib.md5(combined.encode('utf-8')).hexdigest()
    
    # 添加商户ID前缀，确保唯一性
    prefix = merchant_id[:3]
    
    # 组合成最终的商户秘钥
    merchant_secret = f"{prefix}_{hash_value}"
    
    return merchant_secret

# API调用函数
def call_api(endpoint, params=None):
    """调用API接口"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"API调用失败: {e}")
        return None

def get_merchant_info(merchant_id):
    """获取商户信息"""
    return call_api("get_merchant_info.php", {"merchant_id": merchant_id})

def register_merchant(merchant_id):
    """注册商户"""
    return call_api("register_merchant.php", {"merchant_id": merchant_id})

def get_product_list(merchant_secret):
    """获取商品列表"""
    return call_api("get_product_list.php", {"merchant_secret": merchant_secret})

def get_product_info(product_id):
    """获取商品信息"""
    return call_api("get_product_info.php", {"product_id": product_id})

def create_order(customer_contact, product_id, pay_type="wxpay"):
    """创建订单，支持支付方式选择"""
    return call_api("create_order.php", {
        "customer_contact": customer_contact,
        "product_id": product_id,
        "pay_type": pay_type
    })

def check_payment_status(order_id):
    """检查支付状态"""
    return call_api("check_payment_status.php", {"order_id": order_id})

def set_order_payment_status(order_id, payment_status):
    """设置订单支付状态"""
    return call_api("set_order_payment_status.php", {
        "order_id": order_id,
        "payment_status": payment_status
    })

# Telegram通知函数
async def send_order_notification(context: ContextTypes.DEFAULT_TYPE, merchant_id, order_data):
    """发送订单通知给商户"""
    try:
        order_id = order_data.get('order_id', 'N/A')
        product_name = order_data.get('product_name', '未知商品')
        product_price = order_data.get('product_price', '0.00')
        customer_contact = order_data.get('customer_contact', 'N/A')
        delivery_content = order_data.get('delivery_content', '')
        created_at = order_data.get('created_at', 'N/A')
        
        message = f"""
🆕 <b>新订单通知</b>

📋 <b>订单号：</b>{order_id}
📦 <b>商品名称：</b>{product_name}
💰 <b>订单金额：</b>¥{product_price}
👤 <b>客户ID：</b>{customer_contact}
⏰ <b>下单时间：</b>{created_at}

🎁 <b>发货内容：</b>
<code>{delivery_content}</code>

✅ <b>订单已自动发货完成！</b>
"""
        
        # 发送通知给商户
        await context.bot.send_message(
            chat_id=merchant_id,
            text=message,
            parse_mode='HTML'
        )
        
        logger.info(f"订单通知已发送给商户 {merchant_id}")
        
    except Exception as e:
        logger.error(f"发送订单通知失败: {e}")

# 消息格式化函数
def format_merchant_welcome(merchant_info, has_products):
    """格式化商户欢迎消息"""
    shop_name = merchant_info.get('shop_name', '未知商店')
    shop_description = merchant_info.get('shop_description', '暂无介绍')
    
    message = f"""
🎉 <b>欢迎光临！</b>

🏪 <b>商店名称：</b>{shop_name}

📝 <b>商店介绍：</b>
{shop_description}

"""
    
    if not has_products:
        message += "⚠️ <b>该商户还没有商品</b>"
    
    return message

def format_product_info(product_info, merchant_id):
    """格式化商品信息"""
    product_id = product_info.get('product_id', 'N/A')
    product_name = product_info.get('product_name', '未知商品')
    product_description = product_info.get('product_description', '暂无描述')
    stock_quantity = product_info.get('stock_quantity', 0)
    
    message = f"""
📦 <b>商品详情</b>

🏪 <b>商家ID：</b>{merchant_id}
🆔 <b>商品编号：</b>{product_id}
📝 <b>商品名称：</b>{product_name}
📄 <b>商品简介：</b>{product_description}
📊 <b>商品库存：</b>{stock_quantity} 件
"""
    
    return message

def format_order_info(order_info, payment_url):
    """格式化订单信息"""
    order_id = order_info.get('order_id', 'N/A')
    merchant_id = order_info.get('merchant_id', 'N/A')
    product_name = order_info.get('product_name', '未知商品')
    product_price = order_info.get('product_price', '0.00')
    created_at = order_info.get('created_at', 'N/A')
    customer_contact = order_info.get('customer_contact', 'N/A')
    
    message = f"""
🛒 <b>订单创建成功！</b>

📋 <b>订单号：</b>{order_id}
🏪 <b>商户ID：</b>{merchant_id}
📦 <b>商品名称：</b>{product_name}
💰 <b>需支付金额：</b>¥{product_price}
⏰ <b>订单创建时间：</b>{created_at}
👤 <b>购买人ID：</b>{customer_contact}
"""
    
    return message, payment_url

def format_payment_result(order_data):
    """格式化支付结果"""
    order_id = order_data.get('order_id', 'N/A')
    merchant_id = order_data.get('merchant_id', 'N/A')
    product_name = order_data.get('product_name', '未知商品')
    product_price = order_data.get('product_price', '0.00')
    created_at = order_data.get('created_at', 'N/A')
    updated_at = order_data.get('updated_at', 'N/A')
    customer_contact = order_data.get('customer_contact', 'N/A')
    delivery_content = order_data.get('delivery_content', '')
    order_status = order_data.get('order_status', 'unknown')
    
    if order_status == 'paid':
        message = f"""
✅ <b>支付成功！</b>

📋 <b>订单号：</b>{order_id}
🏪 <b>商户ID：</b>{merchant_id}
📦 <b>商品名称：</b>{product_name}
💰 <b>商品金额：</b>¥{product_price}
⏰ <b>订单发起时间：</b>{created_at}
💳 <b>支付时间：</b>{updated_at}
👤 <b>购买人ID：</b>{customer_contact}

🎁 <b>发货内容：</b>
<code>{delivery_content}</code>
"""
    else:
        message = f"""
❌ <b>订单未支付！</b>

📋 <b>订单号：</b>{order_id}
🏪 <b>商户ID：</b>{merchant_id}
📦 <b>商品名称：</b>{product_name}
💰 <b>商品金额：</b>¥{product_price}
⏰ <b>订单发起时间：</b>{created_at}
👤 <b>购买人ID：</b>{customer_contact}
"""
    
    return message

# 按钮创建函数
def create_product_list_buttons(products, merchant_id):
    """创建商品列表按钮"""
    keyboard = []
    
    if not products:
        keyboard.append([InlineKeyboardButton("该商户还没有商品", callback_data="no_products")])
    else:
        for product in products:
            product_id = product.get('product_id')
            product_name = product.get('product_name', '未知商品')
            button_text = f"{product_name}"
            callback_data = f"product_{merchant_id}_{product_id}"
            keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])
    
    return InlineKeyboardMarkup(keyboard)

def create_product_detail_buttons(merchant_id, product_id):
    """创建商品详情按钮，支持支付方式选择和分享商品"""
    keyboard = [
        [
            InlineKeyboardButton("🔙 返回", callback_data=f"back_to_products_{merchant_id}")
        ],
        [
            InlineKeyboardButton("💳 微信支付", callback_data=f"buy_{merchant_id}_{product_id}_wxpay"),
            InlineKeyboardButton("💳 支付宝", callback_data=f"buy_{merchant_id}_{product_id}_alipay")
        ],
        [
            InlineKeyboardButton("📤 分享商品", callback_data=f"share_product_{merchant_id}_{product_id}")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def create_order_buttons(order_id, payment_url):
    """创建订单按钮"""
    keyboard = [
        [
            InlineKeyboardButton("💳 立即支付", url=payment_url),
            InlineKeyboardButton("✅ 我已支付", callback_data=f"check_payment_{order_id}")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def create_shop_buttons(user_id):
    """创建店铺按钮"""
    admin_url = f"https://shop.qnm6.top/admin/index.php?merchant_secret={generate_merchant_secret(user_id)}"
    share_url = f"https://t.me/{BOT_USERNAME}?start={user_id}"
    
    keyboard = [
        [InlineKeyboardButton("🛒 我的店铺", url=admin_url)],
        [InlineKeyboardButton("📤 分享店铺", callback_data=f"share_shop_{user_id}")]
    ]
    return InlineKeyboardMarkup(keyboard)

# 分享文案列表
SHARE_MESSAGES = [
    "💎 这是我的在线商城!7x24h自动发货、售后稳定,交易完全匿名,欢迎下单！"
]

# 命令处理器
async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理/start命令"""
    message_text = update.message.text
    user_id = update.effective_user.id
    
    # 检查是否带有 shoping_商品ID
    match_product = re.match(r'/start\s+shoping_(\d+)', message_text)
    if match_product:
        product_id = match_product.group(1)
        # 获取商品信息
        product_response = get_product_info(product_id)
        if not product_response or product_response.get('status') != 'success':
            await update.message.reply_text("❌ 获取商品信息失败")
            return
        product_info = product_response.get('data', {})
        merchant_id = product_info.get('merchant_id', '')
        message = format_product_info(product_info, merchant_id)
        keyboard = create_product_detail_buttons(merchant_id, product_id)
        await update.message.reply_text(
            message,
            reply_markup=keyboard,
            parse_mode='HTML'
        )
        return
    
    # 解析商户ID
    match = re.match(r'/start\s+(\w+)', message_text)
    if not match:
        # 如果没有商户ID，则与/shop命令相同
        await shop_command(update, context)
        return
    
    merchant_id = match.group(1)
    
    # 获取商户信息
    merchant_info_response = get_merchant_info(merchant_id)
    if not merchant_info_response or merchant_info_response.get('status') != 'success':
        await update.message.reply_text("❌ 商户不存在或获取商户信息失败")
        return
    
    merchant_info = merchant_info_response.get('data', {})
    
    # 生成商户秘钥
    merchant_secret = generate_merchant_secret(merchant_id)
    
    # 获取商品列表
    products_response = get_product_list(merchant_secret)
    products = []
    has_products = False
    
    if products_response and products_response.get('status') == 'success':
        products = products_response.get('data', [])
        has_products = len(products) > 0
    
    # 发送欢迎消息
    welcome_message = format_merchant_welcome(merchant_info, has_products)
    keyboard = create_product_list_buttons(products, merchant_id)
    
    await update.message.reply_text(
        welcome_message,
        reply_markup=keyboard,
        parse_mode='HTML'
    )

async def shop_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理/shop命令"""
    user_id = str(update.effective_user.id)
    
    # 注册商户（无视已存在）
    register_merchant(user_id)
    
    # 欢迎消息
    msg = (
        "🎉 <b>欢迎使用 Mika 寄售机器人！</b>\n\n"
        "这是一个匿名、安全的寄售机器人，\n"
        "一站式托管发卡平台，零门槛入驻。\n\n"
        f"您的专属店铺后台已为您生成👇"
    )
    
    # 内联按钮
    keyboard = create_shop_buttons(user_id)
    
    await update.message.reply_text(
        msg,
        reply_markup=keyboard,
        parse_mode='HTML'
    )

# 回调查询处理器
async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理按钮回调"""
    query = update.callback_query
    await query.answer()
    
    data = query.data
    user_id = update.effective_user.id
    
    if data == "no_products":
        await query.edit_message_text("⚠️ 该商户还没有商品")
        return
    
    elif data.startswith("share_shop_"):
        # 分享店铺功能
        merchant_id = data.split("_")[2]
        share_url = f"https://t.me/{BOT_USERNAME}?start={merchant_id}"
        
        # 发送5个不同的推广文案
        for i, message in enumerate(SHARE_MESSAGES, 1):
            share_message = f"{message}\n\n🔗 店铺链接：{share_url}"
            await query.message.reply_text(share_message)
        
        # 发送完成提示
        await query.message.reply_text("📤 推广文案已推送！今天的努力就是明天的美好！")
        return
    
    elif data.startswith("product_"):
        # 显示商品详情
        parts = data.split("_")
        merchant_id = parts[1]
        product_id = parts[2]
        
        # 获取商品信息
        product_response = get_product_info(product_id)
        if not product_response or product_response.get('status') != 'success':
            await query.edit_message_text("❌ 获取商品信息失败")
            return
        
        product_info = product_response.get('data', {})
        message = format_product_info(product_info, merchant_id)
        keyboard = create_product_detail_buttons(merchant_id, product_id)
        
        await query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode='HTML'
        )
    
    elif data.startswith("back_to_products_"):
        # 返回商品列表
        merchant_id = data.split("_")[3]
        
        # 重新获取商户信息和商品列表
        merchant_info_response = get_merchant_info(merchant_id)
        if not merchant_info_response or merchant_info_response.get('status') != 'success':
            await query.edit_message_text("❌ 获取商户信息失败")
            return
        
        merchant_info = merchant_info_response.get('data', {})
        merchant_secret = generate_merchant_secret(merchant_id)
        
        products_response = get_product_list(merchant_secret)
        products = []
        has_products = False
        
        if products_response and products_response.get('status') == 'success':
            products = products_response.get('data', [])
            has_products = len(products) > 0
        
        welcome_message = format_merchant_welcome(merchant_info, has_products)
        keyboard = create_product_list_buttons(products, merchant_id)
        
        await query.edit_message_text(
            welcome_message,
            reply_markup=keyboard,
            parse_mode='HTML'
        )
    
    elif data.startswith("buy_"):
        # 创建订单，支持支付方式
        parts = data.split("_")
        merchant_id = parts[1]
        product_id = parts[2]
        pay_type = parts[3] if len(parts) > 3 else "wxpay"
        order_response = create_order(str(user_id), product_id, pay_type)
        if not order_response or order_response.get('status') != 'success':
            await query.edit_message_text("❌ 创建订单失败")
            return
        order_data = order_response.get('data', {})
        order_info = order_data.get('order_info', {})
        payment_url = order_data.get('payment_url', '')
        message, payment_url = format_order_info(order_info, payment_url)
        keyboard = create_order_buttons(order_info.get('order_id'), payment_url)
        await query.edit_message_text(
            message,
            reply_markup=keyboard,
            parse_mode='HTML'
        )
    
    elif data.startswith("check_payment_"):
        # 检查支付状态
        order_id = data.split("_")[2]
        
        payment_response = check_payment_status(order_id)
        if not payment_response or payment_response.get('status') != 'success':
            await query.answer("❌ 检查支付状态失败", show_alert=True)
            return
        
        order_data = payment_response.get('data', {})
        order_status = order_data.get('order_status', 'unknown')
        
        if order_status == 'paid':
            # 支付成功，显示完整信息
            message = format_payment_result(order_data)
            await query.edit_message_text(
                message,
                parse_mode='HTML'
            )
            
            # 发送订单通知给商户
            merchant_id = order_data.get('merchant_id')
            if merchant_id:
                await send_order_notification(context, merchant_id, order_data)
        else:
            # 订单未支付，发送新消息
            message = f"""
❌ <b>订单未支付！</b>

📋 <b>订单号：</b>{order_data.get('order_id', 'N/A')}
🏪 <b>商户ID：</b>{order_data.get('merchant_id', 'N/A')}
📦 <b>商品名称：</b>{order_data.get('product_name', '未知商品')}
💰 <b>商品金额：</b>¥{order_data.get('product_price', '0.00')}
⏰ <b>订单发起时间：</b>{order_data.get('created_at', 'N/A')}
👤 <b>购买人ID：</b>{order_data.get('customer_contact', 'N/A')}

💡 <b>请完成支付后再点击"我已支付"按钮</b>
"""
            await query.message.reply_text(
                message,
                parse_mode='HTML'
            )
    
    elif data.startswith("share_product_"):
        # 分享商品功能
        parts = data.split("_")
        merchant_id = parts[2]
        product_id = parts[3]
        share_url = f"https://t.me/{BOT_USERNAME}?start=shoping_{product_id}"
        share_message = f"💎 推荐好物，自动发货，欢迎下单！\n\n🔗 商品直达链接：{share_url}"
        await query.message.reply_text(share_message)
        await query.message.reply_text("📤 商品推广文案已推送！")
        return

async def error_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """错误处理器"""
    logger.error(f"更新 {update} 导致错误 {context.error}")

def main():
    """主函数"""
    # 创建应用
    application = Application.builder().token(BOT_TOKEN).build()
    
    # 添加处理器
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("shop", shop_command))
    application.add_handler(CallbackQueryHandler(button_callback))
    
    # 添加错误处理器
    application.add_error_handler(error_handler)
    
    # 启动机器人
    print("🤖 Telegram机器人启动中...")
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main() 