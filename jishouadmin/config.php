<?php
// 数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'yjsyjs',
    'password' => 'yjsyjs',
    'database' => 'yjsyjs',
    'charset' => 'utf8mb4'
];

// 获取数据库连接
function getDBConnection() {
    global $db_config;
    try {
        $pdo = new PDO(
            "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}",
            $db_config['username'],
            $db_config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$db_config['charset']}"
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        // 这里不echo，交由API文件处理
        return false;
    }
}
?>