<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>数据库连接测试</h1>";

// 数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'yjsyjs',
    'password' => 'yjsyjs',
    'database' => 'yjsyjs',
    'charset' => 'utf8mb4'
];

echo "数据库配置:<br>";
echo "主机: " . $db_config['host'] . "<br>";
echo "用户名: " . $db_config['username'] . "<br>";
echo "数据库: " . $db_config['database'] . "<br><br>";

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$db_config['charset']}"
        ]
    );
    
    echo "✅ 数据库连接成功<br>";
    
    // 测试查询商户表
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM merchants");
    $result = $stmt->fetch();
    echo "商户表记录数: " . $result['count'] . "<br>";
    
    // 测试查询商品表
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $result = $stmt->fetch();
    echo "商品表记录数: " . $result['count'] . "<br>";
    
    // 测试查询订单表
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $result = $stmt->fetch();
    echo "订单表记录数: " . $result['count'] . "<br>";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "<br>";
}
?> 