# Mika API 系统

一个简单的用户管理和卡密系统API，使用PHP和JSON文件存储。

## 功能特性

- 用户注册和信息管理
- 会员天数和积分管理
- 卡密生成和使用系统
- 完整的API文档
- 跨域支持
- 时间验证机制

## 文件结构

```
mika/
├── config.php      # 配置文件和公共函数
├── data.json       # 数据存储文件
├── reg.php         # 用户注册API
├── info.php        # 查看用户信息API
├── vip.php         # 会员天数管理API
├── points.php      # 积分管理API
├── list.php        # 用户列表API
├── gen.php         # 生成卡密API
├── use.php         # 使用卡密API
├── card.php        # 查看卡密API
├── docs.html       # API文档
├── test.html       # 测试工具
└── README.md       # 说明文档
```

## API列表

1. **注册用户** - `reg.php`
2. **查看用户信息** - `info.php`
3. **充值/扣除会员天数** - `vip.php`
4. **充值/扣除积分** - `points.php`
5. **查看所有用户列表** - `list.php`
6. **生成卡密** - `gen.php`
7. **使用卡密** - `use.php`
8. **查看卡密** - `card.php`

## 验证机制

所有API都需要传递`link`参数进行验证：
- 格式：当前时间（YmdHi）的MD5值
- 例如：2025081815:30 → 202508181530 → MD5加密

## 使用方法

1. 部署到Web服务器
2. 确保PHP有文件读写权限
3. 访问`docs.html`查看完整API文档
4. 使用`test.html`进行功能测试

## 数据格式

### 用户数据
```json
{
  "username": "用户名",
  "vipDays": 会员剩余天数,
  "points": 剩余积分,
  "regTime": "注册时间"
}
```

### 卡密数据
```json
{
  "code": "卡密内容",
  "genTime": "生成时间",
  "useTime": "使用时间",
  "useUser": "使用用户",
  "value": 卡密数值,
  "type": "卡密类型",
  "status": "卡密状态"
}
```

## 注意事项

- 所有API使用GET请求
- 响应格式为JSON
- 支持跨域访问
- 会员时间精确到分钟
- 积分精确到个位
- 数据存储在JSON文件中

## 安全建议

1. 在生产环境中使用更强的验证机制
2. 考虑使用数据库替代JSON文件
3. 添加访问频率限制
4. 使用HTTPS协议
5. 定期备份数据文件
