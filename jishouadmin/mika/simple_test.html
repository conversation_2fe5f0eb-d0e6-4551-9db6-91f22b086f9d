<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika API 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input, select {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Mika API 简单测试工具</h1>
    
    <div class="test-section">
        <h3>获取验证码</h3>
        <button onclick="getLink()">获取当前验证码</button>
        <button onclick="debugInfo()">调试信息</button>
        <div class="result" id="linkResult"></div>
    </div>

    <div class="test-section">
        <h3>1. 注册用户</h3>
        <input type="text" id="regUsername" placeholder="用户名" value="testuser">
        <button onclick="registerUser()">注册</button>
        <div class="result" id="regResult"></div>
    </div>

    <div class="test-section">
        <h3>2. 查看用户信息</h3>
        <input type="text" id="infoUsername" placeholder="用户名" value="testuser">
        <button onclick="getUserInfo()">查询</button>
        <div class="result" id="infoResult"></div>
    </div>

    <div class="test-section">
        <h3>3. 充值会员天数</h3>
        <input type="text" id="vipUsername" placeholder="用户名" value="testuser">
        <input type="number" id="vipDays" placeholder="天数" value="30">
        <button onclick="updateVip()">充值</button>
        <div class="result" id="vipResult"></div>
    </div>

    <div class="test-section">
        <h3>4. 充值积分</h3>
        <input type="text" id="pointsUsername" placeholder="用户名" value="testuser">
        <input type="number" id="pointsValue" placeholder="积分" value="100">
        <button onclick="updatePoints()">充值</button>
        <div class="result" id="pointsResult"></div>
    </div>

    <div class="test-section">
        <h3>5. 查看所有用户</h3>
        <button onclick="getAllUsers()">查询</button>
        <div class="result" id="listResult"></div>
    </div>

    <div class="test-section">
        <h3>6. 生成卡密</h3>
        <select id="cardType">
            <option value="vip">会员卡密</option>
            <option value="points">积分卡密</option>
        </select>
        <input type="number" id="cardCount" placeholder="数量" value="3">
        <input type="number" id="cardValue" placeholder="数值" value="30">
        <button onclick="generateCard()">生成</button>
        <div class="result" id="genResult"></div>
    </div>

    <div class="test-section">
        <h3>7. 使用卡密</h3>
        <input type="text" id="useUsername" placeholder="用户名" value="testuser">
        <input type="text" id="useCard" placeholder="卡密">
        <button onclick="useCard()">使用</button>
        <div class="result" id="useResult"></div>
    </div>

    <div class="test-section">
        <h3>8. 查看卡密</h3>
        <input type="text" id="checkCard" placeholder="卡密">
        <button onclick="checkCard()">查询</button>
        <div class="result" id="cardResult"></div>
    </div>

    <script>
        let currentLink = '';

        async function getLink() {
            try {
                const response = await fetch('getlink.php');
                const data = await response.json();
                currentLink = data.link;
                document.getElementById('linkResult').innerHTML = `
时间: ${data.time}
验证码: ${data.link}
时间戳: ${data.timestamp}`;
                document.getElementById('linkResult').className = 'result success';
            } catch (error) {
                document.getElementById('linkResult').textContent = 'Error: ' + error.message;
                document.getElementById('linkResult').className = 'result error';
            }
        }

        async function debugInfo() {
            try {
                if (!currentLink) {
                    await getLink();
                }
                const response = await fetch(`debug.php?link=${currentLink}`);
                const data = await response.json();
                document.getElementById('linkResult').textContent = JSON.stringify(data, null, 2);
                document.getElementById('linkResult').className = 'result';
            } catch (error) {
                document.getElementById('linkResult').textContent = 'Debug Error: ' + error.message;
                document.getElementById('linkResult').className = 'result error';
            }
        }

        async function apiCall(url, resultId) {
            try {
                if (!currentLink) {
                    await getLink();
                }
                
                const fullUrl = `${url}&link=${currentLink}`;
                console.log('Calling:', fullUrl);
                
                const response = await fetch(fullUrl);
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    document.getElementById(resultId).textContent = JSON.stringify(data, null, 2);
                    document.getElementById(resultId).className = data.code === 200 ? 'result success' : 'result error';
                } catch (e) {
                    document.getElementById(resultId).textContent = text;
                    document.getElementById(resultId).className = 'result error';
                }
            } catch (error) {
                document.getElementById(resultId).textContent = 'Error: ' + error.message;
                document.getElementById(resultId).className = 'result error';
            }
        }

        function registerUser() {
            const username = document.getElementById('regUsername').value;
            apiCall(`reg.php?username=${username}`, 'regResult');
        }

        function getUserInfo() {
            const username = document.getElementById('infoUsername').value;
            apiCall(`info.php?username=${username}`, 'infoResult');
        }

        function updateVip() {
            const username = document.getElementById('vipUsername').value;
            const days = document.getElementById('vipDays').value;
            apiCall(`vip.php?username=${username}&days=${days}`, 'vipResult');
        }

        function updatePoints() {
            const username = document.getElementById('pointsUsername').value;
            const points = document.getElementById('pointsValue').value;
            apiCall(`points.php?username=${username}&points=${points}`, 'pointsResult');
        }

        function getAllUsers() {
            apiCall(`list.php?`, 'listResult');
        }

        function generateCard() {
            const type = document.getElementById('cardType').value;
            const count = document.getElementById('cardCount').value;
            const value = document.getElementById('cardValue').value;
            apiCall(`gen.php?type=${type}&count=${count}&value=${value}`, 'genResult');
        }

        function useCard() {
            const username = document.getElementById('useUsername').value;
            const card = document.getElementById('useCard').value;
            apiCall(`use.php?username=${username}&card=${card}`, 'useResult');
        }

        function checkCard() {
            const card = document.getElementById('checkCard').value;
            apiCall(`card.php?card=${card}`, 'cardResult');
        }

        // 自动获取验证码
        getLink();
        // 每分钟自动更新验证码
        setInterval(getLink, 60000);
    </script>
</body>
</html>
