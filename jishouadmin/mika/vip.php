<?php
require_once 'config.php';

// 验证link参数
if (!isset($_GET['link']) || !verifyLink($_GET['link'])) {
    response(400, '验证失败');
}

// 获取参数
if (!isset($_GET['username']) || empty($_GET['username'])) {
    response(400, '用户名不能为空');
}

if (!isset($_GET['days']) || !is_numeric($_GET['days'])) {
    response(400, '天数参数错误');
}

$username = trim($_GET['username']);
$days = intval($_GET['days']);
$data = readData();

// 检查用户是否存在
if (!isset($data['users'][$username])) {
    response(404, '用户不存在');
}

// 更新会员天数
$data['users'][$username]['vipDays'] += $days;

// 确保不为负数
if ($data['users'][$username]['vipDays'] < 0) {
    $data['users'][$username]['vipDays'] = 0;
}

// 保存数据
if (saveData($data)) {
    $user = $data['users'][$username];
    response(200, '操作成功', [
        'username' => $user['username'],
        'vipDays' => $user['vipDays'],
        'points' => $user['points'],
        'regTime' => $user['regTime']
    ]);
} else {
    response(500, '操作失败');
}
?>
