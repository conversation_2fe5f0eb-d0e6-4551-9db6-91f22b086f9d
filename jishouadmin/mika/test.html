<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input, select {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Mika API 测试工具</h1>
    
    <div class="test-section">
        <h3>当前验证码</h3>
        <p>Link参数: <span id="currentLink"></span></p>
        <button onclick="updateLink()">刷新验证码</button>
    </div>

    <div class="test-section">
        <h3>1. 注册用户</h3>
        <input type="text" id="regUsername" placeholder="用户名" value="testuser">
        <button onclick="registerUser()">注册</button>
        <div class="result" id="regResult"></div>
    </div>

    <div class="test-section">
        <h3>2. 查看用户信息</h3>
        <input type="text" id="infoUsername" placeholder="用户名" value="testuser">
        <button onclick="getUserInfo()">查询</button>
        <div class="result" id="infoResult"></div>
    </div>

    <div class="test-section">
        <h3>3. 充值/扣除会员天数</h3>
        <input type="text" id="vipUsername" placeholder="用户名" value="testuser">
        <input type="number" id="vipDays" placeholder="天数" value="30">
        <button onclick="updateVip()">操作</button>
        <div class="result" id="vipResult"></div>
    </div>

    <div class="test-section">
        <h3>4. 充值/扣除积分</h3>
        <input type="text" id="pointsUsername" placeholder="用户名" value="testuser">
        <input type="number" id="pointsValue" placeholder="积分" value="100">
        <button onclick="updatePoints()">操作</button>
        <div class="result" id="pointsResult"></div>
    </div>

    <div class="test-section">
        <h3>5. 查看所有用户</h3>
        <button onclick="getAllUsers()">查询</button>
        <div class="result" id="listResult"></div>
    </div>

    <div class="test-section">
        <h3>6. 生成卡密</h3>
        <select id="cardType">
            <option value="vip">会员卡密</option>
            <option value="points">积分卡密</option>
        </select>
        <input type="number" id="cardCount" placeholder="数量" value="3">
        <input type="number" id="cardValue" placeholder="数值" value="30">
        <button onclick="generateCard()">生成</button>
        <div class="result" id="genResult"></div>
    </div>

    <div class="test-section">
        <h3>7. 使用卡密</h3>
        <input type="text" id="useUsername" placeholder="用户名" value="testuser">
        <input type="text" id="useCard" placeholder="卡密">
        <button onclick="useCard()">使用</button>
        <div class="result" id="useResult"></div>
    </div>

    <div class="test-section">
        <h3>8. 查看卡密</h3>
        <input type="text" id="checkCard" placeholder="卡密">
        <button onclick="checkCard()">查询</button>
        <div class="result" id="cardResult"></div>
    </div>

    <script>
        let currentLink = '';

        // MD5 implementation
        function md5(string) {
            function md5cycle(x, k) {
                var a = x[0], b = x[1], c = x[2], d = x[3];
                a = ff(a, b, c, d, k[0], 7, -680876936);
                d = ff(d, a, b, c, k[1], 12, -389564586);
                c = ff(c, d, a, b, k[2], 17, 606105819);
                b = ff(b, c, d, a, k[3], 22, -1044525330);
                a = ff(a, b, c, d, k[4], 7, -176418897);
                d = ff(d, a, b, c, k[5], 12, 1200080426);
                c = ff(c, d, a, b, k[6], 17, -1473231341);
                b = ff(b, c, d, a, k[7], 22, -45705983);
                a = ff(a, b, c, d, k[8], 7, 1770035416);
                d = ff(d, a, b, c, k[9], 12, -1958414417);
                c = ff(c, d, a, b, k[10], 17, -42063);
                b = ff(b, c, d, a, k[11], 22, -1990404162);
                a = ff(a, b, c, d, k[12], 7, 1804603682);
                d = ff(d, a, b, c, k[13], 12, -40341101);
                c = ff(c, d, a, b, k[14], 17, -1502002290);
                b = ff(b, c, d, a, k[15], 22, 1236535329);
                a = gg(a, b, c, d, k[1], 5, -165796510);
                d = gg(d, a, b, c, k[6], 9, -1069501632);
                c = gg(c, d, a, b, k[11], 14, 643717713);
                b = gg(b, c, d, a, k[0], 20, -373897302);
                a = gg(a, b, c, d, k[5], 5, -701558691);
                d = gg(d, a, b, c, k[10], 9, 38016083);
                c = gg(c, d, a, b, k[15], 14, -660478335);
                b = gg(b, c, d, a, k[4], 20, -405537848);
                a = gg(a, b, c, d, k[9], 5, 568446438);
                d = gg(d, a, b, c, k[14], 9, -1019803690);
                c = gg(c, d, a, b, k[3], 14, -187363961);
                b = gg(b, c, d, a, k[8], 20, 1163531501);
                a = gg(a, b, c, d, k[13], 5, -1444681467);
                d = gg(d, a, b, c, k[2], 9, -51403784);
                c = gg(c, d, a, b, k[7], 14, 1735328473);
                b = gg(b, c, d, a, k[12], 20, -1926607734);
                a = hh(a, b, c, d, k[5], 4, -378558);
                d = hh(d, a, b, c, k[8], 11, -2022574463);
                c = hh(c, d, a, b, k[11], 16, 1839030562);
                b = hh(b, c, d, a, k[14], 23, -35309556);
                a = hh(a, b, c, d, k[1], 4, -1530992060);
                d = hh(d, a, b, c, k[4], 11, 1272893353);
                c = hh(c, d, a, b, k[7], 16, -155497632);
                b = hh(b, c, d, a, k[10], 23, -1094730640);
                a = hh(a, b, c, d, k[13], 4, 681279174);
                d = hh(d, a, b, c, k[0], 11, -358537222);
                c = hh(c, d, a, b, k[3], 16, -722521979);
                b = hh(b, c, d, a, k[6], 23, 76029189);
                a = hh(a, b, c, d, k[9], 4, -640364487);
                d = hh(d, a, b, c, k[12], 11, -421815835);
                c = hh(c, d, a, b, k[15], 16, 530742520);
                b = hh(b, c, d, a, k[2], 23, -995338651);
                a = ii(a, b, c, d, k[0], 6, -198630844);
                d = ii(d, a, b, c, k[7], 10, 1126891415);
                c = ii(c, d, a, b, k[14], 15, -1416354905);
                b = ii(b, c, d, a, k[5], 21, -57434055);
                a = ii(a, b, c, d, k[12], 6, 1700485571);
                d = ii(d, a, b, c, k[3], 10, -1894986606);
                c = ii(c, d, a, b, k[10], 15, -1051523);
                b = ii(b, c, d, a, k[1], 21, -2054922799);
                a = ii(a, b, c, d, k[8], 6, 1873313359);
                d = ii(d, a, b, c, k[15], 10, -30611744);
                c = ii(c, d, a, b, k[6], 15, -1560198380);
                b = ii(b, c, d, a, k[13], 21, 1309151649);
                a = ii(a, b, c, d, k[4], 6, -145523070);
                d = ii(d, a, b, c, k[11], 10, -1120210379);
                c = ii(c, d, a, b, k[2], 15, 718787259);
                b = ii(b, c, d, a, k[9], 21, -343485551);
                x[0] = add32(a, x[0]);
                x[1] = add32(b, x[1]);
                x[2] = add32(c, x[2]);
                x[3] = add32(d, x[3]);
            }

            function cmn(q, a, b, x, s, t) {
                a = add32(add32(a, q), add32(x, t));
                return add32((a << s) | (a >>> (32 - s)), b);
            }

            function ff(a, b, c, d, x, s, t) {
                return cmn((b & c) | ((~b) & d), a, b, x, s, t);
            }

            function gg(a, b, c, d, x, s, t) {
                return cmn((b & d) | (c & (~d)), a, b, x, s, t);
            }

            function hh(a, b, c, d, x, s, t) {
                return cmn(b ^ c ^ d, a, b, x, s, t);
            }

            function ii(a, b, c, d, x, s, t) {
                return cmn(c ^ (b | (~d)), a, b, x, s, t);
            }

            function md51(s) {
                var n = s.length,
                    state = [1732584193, -271733879, -1732584194, 271733878], i;
                for (i = 64; i <= s.length; i += 64) {
                    md5cycle(state, md5blk(s.substring(i - 64, i)));
                }
                s = s.substring(i - 64);
                var tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
                for (i = 0; i < s.length; i++)
                    tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);
                tail[i >> 2] |= 0x80 << ((i % 4) << 3);
                if (i > 55) {
                    md5cycle(state, tail);
                    for (i = 0; i < 16; i++) tail[i] = 0;
                }
                tail[14] = n * 8;
                md5cycle(state, tail);
                return state;
            }

            function md5blk(s) {
                var md5blks = [], i;
                for (i = 0; i < 64; i += 4) {
                    md5blks[i >> 2] = s.charCodeAt(i)
                        + (s.charCodeAt(i + 1) << 8)
                        + (s.charCodeAt(i + 2) << 16)
                        + (s.charCodeAt(i + 3) << 24);
                }
                return md5blks;
            }

            var hex_chr = '0123456789abcdef'.split('');

            function rhex(n) {
                var s = '', j = 0;
                for (; j < 4; j++)
                    s += hex_chr[(n >> (j * 8 + 4)) & 0x0F]
                        + hex_chr[(n >> (j * 8)) & 0x0F];
                return s;
            }

            function hex(x) {
                for (var i = 0; i < x.length; i++)
                    x[i] = rhex(x[i]);
                return x.join('');
            }

            function add32(a, b) {
                return (a + b) & 0xFFFFFFFF;
            }

            return hex(md51(string));
        }

        async function updateLink() {
            try {
                // 从服务器获取正确的验证码
                const response = await fetch('getlink.php');
                const data = await response.json();
                currentLink = data.link;
                document.getElementById('currentLink').textContent = currentLink + ' (服务器生成)';
            } catch (error) {
                // 如果服务器不可用，使用本地生成
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hour = String(now.getHours()).padStart(2, '0');
                const minute = String(now.getMinutes()).padStart(2, '0');

                const timeStr = year + month + day + hour + minute;
                currentLink = md5(timeStr);
                document.getElementById('currentLink').textContent = currentLink + ' (本地生成)';
            }
        }

        async function apiCall(url, resultId) {
            try {
                const response = await fetch(url);
                const data = await response.text();
                document.getElementById(resultId).textContent = data;
            } catch (error) {
                document.getElementById(resultId).textContent = 'Error: ' + error.message;
            }
        }

        function registerUser() {
            const username = document.getElementById('regUsername').value;
            apiCall(`reg.php?username=${username}&link=${currentLink}`, 'regResult');
        }

        function getUserInfo() {
            const username = document.getElementById('infoUsername').value;
            apiCall(`info.php?username=${username}&link=${currentLink}`, 'infoResult');
        }

        function updateVip() {
            const username = document.getElementById('vipUsername').value;
            const days = document.getElementById('vipDays').value;
            apiCall(`vip.php?username=${username}&days=${days}&link=${currentLink}`, 'vipResult');
        }

        function updatePoints() {
            const username = document.getElementById('pointsUsername').value;
            const points = document.getElementById('pointsValue').value;
            apiCall(`points.php?username=${username}&points=${points}&link=${currentLink}`, 'pointsResult');
        }

        function getAllUsers() {
            apiCall(`list.php?link=${currentLink}`, 'listResult');
        }

        function generateCard() {
            const type = document.getElementById('cardType').value;
            const count = document.getElementById('cardCount').value;
            const value = document.getElementById('cardValue').value;
            apiCall(`gen.php?type=${type}&count=${count}&value=${value}&link=${currentLink}`, 'genResult');
        }

        function useCard() {
            const username = document.getElementById('useUsername').value;
            const card = document.getElementById('useCard').value;
            apiCall(`use.php?username=${username}&card=${card}&link=${currentLink}`, 'useResult');
        }

        function checkCard() {
            const card = document.getElementById('checkCard').value;
            apiCall(`card.php?card=${card}&link=${currentLink}`, 'cardResult');
        }

        // 初始化
        updateLink();
        setInterval(updateLink, 60000); // 每分钟更新一次
    </script>
</body>
</html>
