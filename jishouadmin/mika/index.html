<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika API 系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }
        .nav-links a {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            border-radius: 0 5px 5px 0;
        }
        .api-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .api-item {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .api-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .api-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Mika API 系统</h1>
        
        <div class="nav-links">
            <a href="docs.html">📖 API文档</a>
            <a href="test.html">🧪 完整测试</a>
            <a href="simple_test.html">🚀 简单测试</a>
            <a href="getlink.php" target="_blank">🔑 获取验证码</a>
        </div>

        <h2>✨ 系统特性</h2>
        <ul class="feature-list">
            <li><strong>用户管理：</strong>注册用户、查看信息、管理会员和积分</li>
            <li><strong>卡密系统：</strong>生成、使用、查询卡密状态</li>
            <li><strong>安全验证：</strong>基于时间的MD5验证机制</li>
            <li><strong>跨域支持：</strong>支持跨域访问，便于前端调用</li>
            <li><strong>JSON存储：</strong>轻量级文件存储，无需数据库</li>
        </ul>

        <h2>🔧 API接口列表</h2>
        <div class="api-list">
            <div class="api-item">
                <h4>1. 用户注册</h4>
                <p>文件: reg.php<br>功能: 注册新用户</p>
            </div>
            <div class="api-item">
                <h4>2. 用户信息</h4>
                <p>文件: info.php<br>功能: 查看用户详细信息</p>
            </div>
            <div class="api-item">
                <h4>3. 会员管理</h4>
                <p>文件: vip.php<br>功能: 充值/扣除会员天数</p>
            </div>
            <div class="api-item">
                <h4>4. 积分管理</h4>
                <p>文件: points.php<br>功能: 充值/扣除用户积分</p>
            </div>
            <div class="api-item">
                <h4>5. 用户列表</h4>
                <p>文件: list.php<br>功能: 获取所有用户列表</p>
            </div>
            <div class="api-item">
                <h4>6. 生成卡密</h4>
                <p>文件: gen.php<br>功能: 批量生成卡密</p>
            </div>
            <div class="api-item">
                <h4>7. 使用卡密</h4>
                <p>文件: use.php<br>功能: 用户使用卡密</p>
            </div>
            <div class="api-item">
                <h4>8. 查看卡密</h4>
                <p>文件: card.php<br>功能: 查询卡密状态</p>
            </div>
        </div>

        <h2>📋 使用说明</h2>
        <ol>
            <li>所有API均使用GET请求方式</li>
            <li>响应格式统一为JSON</li>
            <li>需要传递link参数进行验证</li>
            <li>会员时间精确到分钟，积分精确到个位</li>
            <li>支持跨域访问</li>
        </ol>

        <h2>🔐 验证机制</h2>
        <p>所有API调用都需要传递<code>link</code>参数，该参数为当前时间（格式：YmdHi）的MD5值。</p>
        <p>例如：2025年8月18日15:30 → 202508181530 → MD5加密后的值</p>
        
        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>© 2025 Mika API System - 简单高效的用户管理API</p>
        </div>
    </div>
</body>
</html>
