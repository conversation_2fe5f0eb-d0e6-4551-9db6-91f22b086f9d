<?php
// 命令行API测试脚本
echo "=== Mika API 测试 ===\n\n";

// 模拟GET参数
function simulateAPI($file, $params) {
    // 保存原始的$_GET
    $originalGet = $_GET;
    
    // 设置新的$_GET参数
    $_GET = $params;
    
    // 开始输出缓冲
    ob_start();
    
    try {
        // 包含API文件
        include $file;
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage();
    }
    
    // 获取输出
    $output = ob_get_clean();
    
    // 恢复原始的$_GET
    $_GET = $originalGet;
    
    return $output;
}

// 获取当前验证码
$currentTime = date('YmdHi');
$link = md5($currentTime);

echo "当前时间: $currentTime\n";
echo "验证码: $link\n\n";

// 测试1: 注册用户
echo "1. 测试注册用户\n";
$result = simulateAPI('reg.php', [
    'username' => 'testuser',
    'link' => $link
]);
echo "结果: $result\n\n";

// 测试2: 查看用户信息
echo "2. 测试查看用户信息\n";
$result = simulateAPI('info.php', [
    'username' => 'testuser',
    'link' => $link
]);
echo "结果: $result\n\n";

// 测试3: 充值会员
echo "3. 测试充值会员30天\n";
$result = simulateAPI('vip.php', [
    'username' => 'testuser',
    'days' => 30,
    'link' => $link
]);
echo "结果: $result\n\n";

// 测试4: 充值积分
echo "4. 测试充值积分100\n";
$result = simulateAPI('points.php', [
    'username' => 'testuser',
    'points' => 100,
    'link' => $link
]);
echo "结果: $result\n\n";

// 测试5: 查看用户列表
echo "5. 测试查看用户列表\n";
$result = simulateAPI('list.php', [
    'link' => $link
]);
echo "结果: $result\n\n";

// 测试6: 生成卡密
echo "6. 测试生成3张会员卡密\n";
$result = simulateAPI('gen.php', [
    'type' => 'vip',
    'count' => 3,
    'value' => 15,
    'link' => $link
]);
echo "结果: $result\n\n";

// 解析生成的卡密
$cardData = json_decode($result, true);
if ($cardData && isset($cardData['vipkami'])) {
    $cards = explode('-', $cardData['vipkami']);
    $firstCard = $cards[0];
    
    // 测试7: 使用卡密
    echo "7. 测试使用卡密: $firstCard\n";
    $result = simulateAPI('use.php', [
        'username' => 'testuser',
        'card' => $firstCard,
        'link' => $link
    ]);
    echo "结果: $result\n\n";
    
    // 测试8: 查看卡密
    echo "8. 测试查看卡密: $firstCard\n";
    $result = simulateAPI('card.php', [
        'card' => $firstCard,
        'link' => $link
    ]);
    echo "结果: $result\n\n";
}

// 查看最终数据文件
echo "=== 最终数据文件内容 ===\n";
if (file_exists('data.json')) {
    $data = file_get_contents('data.json');
    echo $data . "\n";
} else {
    echo "数据文件不存在\n";
}

echo "\n=== 测试完成 ===\n";
?>
