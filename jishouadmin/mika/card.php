<?php
require_once 'config.php';

// 验证link参数
if (!isset($_GET['link']) || !verifyLink($_GET['link'])) {
    response(400, '验证失败');
}

// 获取卡密
if (!isset($_GET['card']) || empty($_GET['card'])) {
    response(400, '卡密不能为空');
}

$cardCode = trim($_GET['card']);
$data = readData();

// 检查卡密是否存在
if (!isset($data['cards'][$cardCode])) {
    response(404, '卡密不存在');
}

$card = $data['cards'][$cardCode];

response(200, '查询成功', [
    'cardInfo' => $card
]);
?>
