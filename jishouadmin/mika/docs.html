<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika API 文档</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .api-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
        }
        .api-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .code-block {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .param-table th, .param-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .param-table th {
            background-color: #f2f2f2;
        }
        .method-get {
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Mika API 文档</h1>
    <p>所有API均使用GET请求，响应格式为JSON。所有API都需要传递link参数用于验证。</p>
    
    <h2>验证说明</h2>
    <p><strong>link参数：</strong>当前时间（格式：YmdHi，精确到分钟）的MD5值</p>
    <div class="code-block">
        例如：2025081815:30 → 202508181530 → MD5加密后的值
    </div>

    <div class="api-section">
        <h2 class="api-title">1. 注册用户 <span class="method-get">GET</span></h2>
        <p><strong>文件：</strong>reg.php</p>
        
        <h3>请求参数</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
            <tr><td>username</td><td>string</td><td>是</td><td>用户名</td></tr>
            <tr><td>link</td><td>string</td><td>是</td><td>验证参数</td></tr>
        </table>
        
        <h3>请求示例</h3>
        <div class="code-block">
            GET /mika/reg.php?username=test&link=abc123def456
        </div>
        
        <h3>响应示例</h3>
        <div class="code-block">
{
    "code": 200,
    "msg": "注册成功",
    "username": "test",
    "vipDays": 0,
    "points": 0,
    "regTime": "2025-08-18 15:30"
}
        </div>
        
        <h3>响应参数说明</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>说明</th></tr>
            <tr><td>code</td><td>int</td><td>状态码</td></tr>
            <tr><td>msg</td><td>string</td><td>状态信息</td></tr>
            <tr><td>username</td><td>string</td><td>用户名</td></tr>
            <tr><td>vipDays</td><td>int</td><td>会员剩余天数</td></tr>
            <tr><td>points</td><td>int</td><td>剩余积分</td></tr>
            <tr><td>regTime</td><td>string</td><td>注册时间</td></tr>
        </table>
    </div>

    <div class="api-section">
        <h2 class="api-title">2. 查看用户信息 <span class="method-get">GET</span></h2>
        <p><strong>文件：</strong>info.php</p>
        
        <h3>请求参数</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
            <tr><td>username</td><td>string</td><td>是</td><td>用户名</td></tr>
            <tr><td>link</td><td>string</td><td>是</td><td>验证参数</td></tr>
        </table>
        
        <h3>请求示例</h3>
        <div class="code-block">
            GET /mika/info.php?username=test&link=abc123def456
        </div>
        
        <h3>响应示例</h3>
        <div class="code-block">
{
    "code": 200,
    "msg": "查询成功",
    "username": "test",
    "vipDays": 30,
    "points": 100,
    "regTime": "2025-08-18 15:30"
}
        </div>
    </div>

    <div class="api-section">
        <h2 class="api-title">3. 充值/扣除会员天数 <span class="method-get">GET</span></h2>
        <p><strong>文件：</strong>vip.php</p>
        
        <h3>请求参数</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
            <tr><td>username</td><td>string</td><td>是</td><td>用户名</td></tr>
            <tr><td>days</td><td>int</td><td>是</td><td>天数（正数充值，负数扣除）</td></tr>
            <tr><td>link</td><td>string</td><td>是</td><td>验证参数</td></tr>
        </table>
        
        <h3>请求示例</h3>
        <div class="code-block">
            GET /mika/vip.php?username=test&days=30&link=abc123def456
        </div>
        
        <h3>响应示例</h3>
        <div class="code-block">
{
    "code": 200,
    "msg": "操作成功",
    "username": "test",
    "vipDays": 60,
    "points": 100,
    "regTime": "2025-08-18 15:30"
}
        </div>
    </div>

    <div class="api-section">
        <h2 class="api-title">4. 充值/扣除积分 <span class="method-get">GET</span></h2>
        <p><strong>文件：</strong>points.php</p>
        
        <h3>请求参数</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
            <tr><td>username</td><td>string</td><td>是</td><td>用户名</td></tr>
            <tr><td>points</td><td>int</td><td>是</td><td>积分（正数充值，负数扣除）</td></tr>
            <tr><td>link</td><td>string</td><td>是</td><td>验证参数</td></tr>
        </table>
        
        <h3>请求示例</h3>
        <div class="code-block">
            GET /mika/points.php?username=test&points=50&link=abc123def456
        </div>

    <div class="api-section">
        <h2 class="api-title">6. 生成卡密 <span class="method-get">GET</span></h2>
        <p><strong>文件：</strong>gen.php</p>

        <h3>请求参数</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
            <tr><td>type</td><td>string</td><td>是</td><td>卡密类型（vip或points）</td></tr>
            <tr><td>count</td><td>int</td><td>是</td><td>生成数量</td></tr>
            <tr><td>value</td><td>int</td><td>是</td><td>卡密数值</td></tr>
            <tr><td>link</td><td>string</td><td>是</td><td>验证参数</td></tr>
        </table>

        <h3>请求示例</h3>
        <div class="code-block">
            GET /mika/gen.php?type=vip&count=3&value=30&link=abc123def456
        </div>

        <h3>响应示例</h3>
        <div class="code-block">
{
    "code": 200,
    "msg": "生成成功",
    "vipkami": "ABCD1234-EFGH5678-IJKL9012"
}
        </div>

        <h3>响应参数说明</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>说明</th></tr>
            <tr><td>vipkami</td><td>string</td><td>会员卡密（多张用-分割）</td></tr>
            <tr><td>jifenkami</td><td>string</td><td>积分卡密（多张用-分割）</td></tr>
        </table>
    </div>

    <div class="api-section">
        <h2 class="api-title">7. 使用卡密 <span class="method-get">GET</span></h2>
        <p><strong>文件：</strong>use.php</p>

        <h3>请求参数</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
            <tr><td>username</td><td>string</td><td>是</td><td>用户名</td></tr>
            <tr><td>card</td><td>string</td><td>是</td><td>卡密内容</td></tr>
            <tr><td>link</td><td>string</td><td>是</td><td>验证参数</td></tr>
        </table>

        <h3>请求示例</h3>
        <div class="code-block">
            GET /mika/use.php?username=test&card=ABCD1234&link=abc123def456
        </div>

        <h3>响应示例</h3>
        <div class="code-block">
{
    "code": 200,
    "msg": "使用成功",
    "username": "test",
    "oldVipDays": 30,
    "newVipDays": 60,
    "oldPoints": 100,
    "newPoints": 100
}
        </div>

        <h3>响应参数说明</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>说明</th></tr>
            <tr><td>username</td><td>string</td><td>用户名</td></tr>
            <tr><td>oldVipDays</td><td>int</td><td>原本会员天数</td></tr>
            <tr><td>newVipDays</td><td>int</td><td>现在会员天数</td></tr>
            <tr><td>oldPoints</td><td>int</td><td>原本剩余积分</td></tr>
            <tr><td>newPoints</td><td>int</td><td>现在剩余积分</td></tr>
        </table>
    </div>

    <div class="api-section">
        <h2 class="api-title">8. 查看卡密 <span class="method-get">GET</span></h2>
        <p><strong>文件：</strong>card.php</p>

        <h3>请求参数</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
            <tr><td>card</td><td>string</td><td>是</td><td>卡密内容</td></tr>
            <tr><td>link</td><td>string</td><td>是</td><td>验证参数</td></tr>
        </table>

        <h3>请求示例</h3>
        <div class="code-block">
            GET /mika/card.php?card=ABCD1234&link=abc123def456
        </div>

        <h3>响应示例</h3>
        <div class="code-block">
{
    "code": 200,
    "msg": "查询成功",
    "cardInfo": {
        "code": "ABCD1234",
        "genTime": "2025-08-18 15:30",
        "useTime": "2025-08-18 16:00",
        "useUser": "test",
        "value": 30,
        "type": "vip",
        "status": "used"
    }
}
        </div>

        <h3>响应参数说明</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>说明</th></tr>
            <tr><td>code</td><td>string</td><td>卡密内容</td></tr>
            <tr><td>genTime</td><td>string</td><td>生成时间</td></tr>
            <tr><td>useTime</td><td>string</td><td>使用时间</td></tr>
            <tr><td>useUser</td><td>string</td><td>使用用户</td></tr>
            <tr><td>value</td><td>int</td><td>卡密数值</td></tr>
            <tr><td>type</td><td>string</td><td>卡密类型</td></tr>
            <tr><td>status</td><td>string</td><td>卡密状态（unused/used）</td></tr>
        </table>
    </div>

    <h2>状态码说明</h2>
    <table class="param-table">
        <tr><th>状态码</th><th>说明</th></tr>
        <tr><td>200</td><td>成功</td></tr>
        <tr><td>400</td><td>请求参数错误</td></tr>
        <tr><td>404</td><td>资源不存在</td></tr>
        <tr><td>500</td><td>服务器内部错误</td></tr>
    </table>

</body>
</html>
        
        <h3>响应示例</h3>
        <div class="code-block">
{
    "code": 200,
    "msg": "操作成功",
    "username": "test",
    "vipDays": 60,
    "points": 150,
    "regTime": "2025-08-18 15:30"
}
        </div>
    </div>

    <div class="api-section">
        <h2 class="api-title">5. 查看所有用户列表 <span class="method-get">GET</span></h2>
        <p><strong>文件：</strong>list.php</p>
        
        <h3>请求参数</h3>
        <table class="param-table">
            <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
            <tr><td>link</td><td>string</td><td>是</td><td>验证参数</td></tr>
        </table>
        
        <h3>请求示例</h3>
        <div class="code-block">
            GET /mika/list.php?link=abc123def456
        </div>
        
        <h3>响应示例</h3>
        <div class="code-block">
{
    "code": 200,
    "msg": "查询成功",
    "users": [
        {
            "username": "test1",
            "vipDays": 30,
            "points": 100,
            "regTime": "2025-08-18 15:30"
        },
        {
            "username": "test2",
            "vipDays": 0,
            "points": 50,
            "regTime": "2025-08-18 16:00"
        }
    ],
    "total": 2
}
        </div>
    </div>
