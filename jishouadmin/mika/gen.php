<?php
require_once 'config.php';

// 验证link参数
if (!isset($_GET['link']) || !verifyLink($_GET['link'])) {
    response(400, '验证失败');
}

// 获取参数
if (!isset($_GET['type']) || !in_array($_GET['type'], ['vip', 'points'])) {
    response(400, '卡密类型错误，只能是vip或points');
}

if (!isset($_GET['count']) || !is_numeric($_GET['count']) || $_GET['count'] <= 0) {
    response(400, '生成数量错误');
}

if (!isset($_GET['value']) || !is_numeric($_GET['value']) || $_GET['value'] <= 0) {
    response(400, '卡密数值错误');
}

$type = $_GET['type'];
$count = intval($_GET['count']);
$value = intval($_GET['value']);
$data = readData();

$cards = [];
$genTime = date('Y-m-d H:i');

// 生成卡密
for ($i = 0; $i < $count; $i++) {
    $cardCode = generateCard($type, $value);
    
    // 确保卡密唯一
    while (isset($data['cards'][$cardCode])) {
        $cardCode = generateCard($type, $value);
    }
    
    $data['cards'][$cardCode] = [
        'code' => $cardCode,
        'genTime' => $genTime,
        'useTime' => null,
        'useUser' => null,
        'value' => $value,
        'type' => $type,
        'status' => 'unused'
    ];
    
    $cards[] = $cardCode;
}

// 保存数据
if (saveData($data)) {
    $responseKey = $type === 'vip' ? 'vipkami' : 'jifenkami';
    response(200, '生成成功', [
        $responseKey => implode('-', $cards)
    ]);
} else {
    response(500, '生成失败');
}
?>
