<?php
// 快速测试脚本
echo "<h1>Mika API 快速测试</h1>";

// 获取当前验证码
$currentTime = date('YmdHi');
$link = md5($currentTime);

echo "<h2>当前验证信息</h2>";
echo "时间: $currentTime<br>";
echo "验证码: $link<br><br>";

// 测试函数
function testAPI($name, $url) {
    echo "<h3>测试: $name</h3>";
    echo "URL: $url<br>";
    
    $result = file_get_contents($url);
    echo "结果: <pre>$result</pre><br>";
}

// 设置基础URL
$baseUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/";

// 测试各个API
echo "<h2>API测试结果</h2>";

// 1. 注册用户
testAPI("注册用户", $baseUrl . "reg.php?username=testuser&link=$link");

// 2. 查看用户信息
testAPI("查看用户信息", $baseUrl . "info.php?username=testuser&link=$link");

// 3. 充值会员
testAPI("充值会员30天", $baseUrl . "vip.php?username=testuser&days=30&link=$link");

// 4. 充值积分
testAPI("充值积分100", $baseUrl . "points.php?username=testuser&points=100&link=$link");

// 5. 查看用户列表
testAPI("查看用户列表", $baseUrl . "list.php?link=$link");

// 6. 生成卡密
testAPI("生成3张会员卡密", $baseUrl . "gen.php?type=vip&count=3&value=15&link=$link");

// 7. 查看数据文件
echo "<h3>数据文件内容</h3>";
if (file_exists('data.json')) {
    $data = file_get_contents('data.json');
    echo "<pre>$data</pre>";
} else {
    echo "数据文件不存在";
}
?>
