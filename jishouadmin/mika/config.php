<?php
// 配置文件
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 数据文件路径
define('DATA_FILE', __DIR__ . '/data.json');

// 验证link参数
function verifyLink($link) {
    $currentTime = date('YmdHi'); // 当前时间，精确到分钟
    $expectedHash = md5($currentTime);
    return $link === $expectedHash;
}

// 读取数据
function readData() {
    if (!file_exists(DATA_FILE)) {
        return ['users' => [], 'cards' => []];
    }
    $data = file_get_contents(DATA_FILE);
    return json_decode($data, true) ?: ['users' => [], 'cards' => []];
}

// 保存数据
function saveData($data) {
    return file_put_contents(DATA_FILE, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

// 响应函数
function response($code, $msg, $data = null) {
    $result = [
        'code' => $code,
        'msg' => $msg
    ];
    if ($data !== null) {
        $result = array_merge($result, $data);
    }
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    exit;
}

// 生成随机卡密
function generateCard($type, $value) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $card = '';
    for ($i = 0; $i < 8; $i++) {
        $card .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $card;
}
?>
