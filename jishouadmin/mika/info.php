<?php
require_once 'config.php';

// 验证link参数
if (!isset($_GET['link']) || !verifyLink($_GET['link'])) {
    response(400, '验证失败');
}

// 获取用户名
if (!isset($_GET['username']) || empty($_GET['username'])) {
    response(400, '用户名不能为空');
}

$username = trim($_GET['username']);
$data = readData();

// 检查用户是否存在
if (!isset($data['users'][$username])) {
    response(404, '用户不存在');
}

$user = $data['users'][$username];

response(200, '查询成功', [
    'username' => $user['username'],
    'vipDays' => $user['vipDays'],
    'points' => $user['points'],
    'regTime' => $user['regTime']
]);
?>
