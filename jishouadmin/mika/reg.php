<?php
require_once 'config.php';

// 验证link参数
if (!isset($_GET['link']) || !verifyLink($_GET['link'])) {
    response(400, '验证失败');
}

// 获取用户名
if (!isset($_GET['username']) || empty($_GET['username'])) {
    response(400, '用户名不能为空');
}

$username = trim($_GET['username']);
$data = readData();

// 检查用户是否已存在
if (isset($data['users'][$username])) {
    response(400, '用户已存在');
}

// 创建新用户
$regTime = date('Y-m-d H:i');
$data['users'][$username] = [
    'username' => $username,
    'vipDays' => 0,
    'points' => 0,
    'regTime' => $regTime
];

// 保存数据
if (saveData($data)) {
    response(200, '注册成功', [
        'username' => $username,
        'vipDays' => 0,
        'points' => 0,
        'regTime' => $regTime
    ]);
} else {
    response(500, '注册失败');
}
?>
