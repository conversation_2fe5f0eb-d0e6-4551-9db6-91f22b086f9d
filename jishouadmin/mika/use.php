<?php
require_once 'config.php';

// 验证link参数
if (!isset($_GET['link']) || !verifyLink($_GET['link'])) {
    response(400, '验证失败');
}

// 获取参数
if (!isset($_GET['username']) || empty($_GET['username'])) {
    response(400, '用户名不能为空');
}

if (!isset($_GET['card']) || empty($_GET['card'])) {
    response(400, '卡密不能为空');
}

$username = trim($_GET['username']);
$cardCode = trim($_GET['card']);
$data = readData();

// 检查用户是否存在
if (!isset($data['users'][$username])) {
    response(404, '用户不存在');
}

// 检查卡密是否存在
if (!isset($data['cards'][$cardCode])) {
    response(404, '卡密不存在');
}

$card = $data['cards'][$cardCode];

// 检查卡密是否已使用
if ($card['status'] === 'used') {
    response(400, '卡密已被使用');
}

// 记录原始数据
$oldVipDays = $data['users'][$username]['vipDays'];
$oldPoints = $data['users'][$username]['points'];

// 使用卡密
if ($card['type'] === 'vip') {
    $data['users'][$username]['vipDays'] += $card['value'];
} else {
    $data['users'][$username]['points'] += $card['value'];
}

// 更新卡密状态
$data['cards'][$cardCode]['status'] = 'used';
$data['cards'][$cardCode]['useTime'] = date('Y-m-d H:i');
$data['cards'][$cardCode]['useUser'] = $username;

// 保存数据
if (saveData($data)) {
    response(200, '使用成功', [
        'username' => $username,
        'oldVipDays' => $oldVipDays,
        'newVipDays' => $data['users'][$username]['vipDays'],
        'oldPoints' => $oldPoints,
        'newPoints' => $data['users'][$username]['points']
    ]);
} else {
    response(500, '使用失败');
}
?>
