<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 调试信息
$debug = [
    'current_time' => date('Y-m-d H:i:s'),
    'time_format' => date('YmdHi'),
    'expected_link' => md5(date('YmdHi')),
    'received_link' => $_GET['link'] ?? 'not provided',
    'get_params' => $_GET,
    'server_info' => [
        'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'unknown'
    ],
    'file_permissions' => [
        'data_file_exists' => file_exists(__DIR__ . '/data.json'),
        'data_file_readable' => is_readable(__DIR__ . '/data.json'),
        'data_file_writable' => is_writable(__DIR__ . '/data.json'),
        'directory_writable' => is_writable(__DIR__)
    ]
];

// 验证link
$currentTime = date('YmdHi');
$expectedHash = md5($currentTime);
$receivedLink = $_GET['link'] ?? '';

$debug['verification'] = [
    'current_time' => $currentTime,
    'expected_hash' => $expectedHash,
    'received_link' => $receivedLink,
    'is_valid' => ($receivedLink === $expectedHash)
];

// 测试数据文件读写
try {
    $testData = ['test' => 'data', 'timestamp' => time()];
    $writeResult = file_put_contents(__DIR__ . '/test_write.json', json_encode($testData));
    $readResult = file_get_contents(__DIR__ . '/test_write.json');
    $debug['file_test'] = [
        'write_result' => $writeResult,
        'read_result' => $readResult,
        'success' => ($writeResult !== false && $readResult !== false)
    ];
    // 清理测试文件
    @unlink(__DIR__ . '/test_write.json');
} catch (Exception $e) {
    $debug['file_test'] = [
        'error' => $e->getMessage()
    ];
}

echo json_encode($debug, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
