<?php
// 测试nginx配置和PHP环境
echo "<h1>PHP环境测试</h1>";

echo "<h2>服务器信息</h2>";
echo "<p><strong>PHP版本:</strong> " . phpversion() . "</p>";
echo "<p><strong>服务器软件:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>文档根目录:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";

echo "<h2>请求信息</h2>";
echo "<p><strong>请求URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "</p>";
echo "<p><strong>脚本名称:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'N/A') . "</p>";
echo "<p><strong>查询字符串:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'N/A') . "</p>";
echo "<p><strong>请求方法:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'N/A') . "</p>";

echo "<h2>GET参数</h2>";
if (!empty($_GET)) {
    foreach ($_GET as $key => $value) {
        echo "<p><strong>$key:</strong> " . htmlspecialchars($value) . "</p>";
    }
} else {
    echo "<p>没有GET参数</p>";
}

echo "<h2>所有$_SERVER变量</h2>";
echo "<pre>";
foreach ($_SERVER as $key => $value) {
    echo htmlspecialchars("$key = $value") . "\n";
}
echo "</pre>";

echo "<h2>测试链接</h2>";
echo '<p><a href="test_nginx.php?test=1&dd=ORDER17542839568146">测试带参数的链接</a></p>';
echo '<p><a href="shop.php?dd=ORDER17542839568146">测试shop.php</a></p>';
?>
