<?php
/**
 * 支付完成后的页面跳转处理文件
 * 接收支付平台的return_url跳转，验证参数后跳转到订单查看页面
 */

// 记录日志函数
function writeReturnLog($message, $level = 'INFO') {
    $log_dir = __DIR__ . '/log';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0777, true);
    }
    $log_file = $log_dir . '/return_' . date('Y-m-d') . '.log';
    $log_time = date('Y-m-d H:i:s');
    $log_content = "[$log_time] [$level] $message\n";
    file_put_contents($log_file, $log_content, FILE_APPEND);
}

// 记录访问
writeReturnLog("收到支付返回请求");
writeReturnLog("GET参数: " . json_encode($_GET, JSON_UNESCAPED_UNICODE));
writeReturnLog("POST参数: " . json_encode($_POST, JSON_UNESCAPED_UNICODE));

// 获取参数（优先使用GET参数）
$params = !empty($_GET) ? $_GET : $_POST;

// 检查是否有订单号
$out_trade_no = $params['out_trade_no'] ?? '';

if (empty($out_trade_no)) {
    writeReturnLog("缺少订单号参数", 'ERROR');
    // 跳转到首页或错误页面
    header('Location: https://cloudshop.qnm6.top/');
    exit;
}

writeReturnLog("订单号: $out_trade_no，准备跳转到订单查看页面");

// 跳转到订单查看页面
$redirect_url = "https://cloudshop.qnm6.top/shop.php?dd=" . urlencode($out_trade_no);
writeReturnLog("跳转URL: $redirect_url");

header("Location: $redirect_url");
exit;
?>
