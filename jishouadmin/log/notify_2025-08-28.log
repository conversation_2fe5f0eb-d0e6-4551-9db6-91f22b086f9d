[2025-08-28 03:36:17] [INFO] 收到支付通知
[2025-08-28 03:36:17] [INFO] GET参数: {"pid":"2222","trade_no":"2025082803364276521","out_trade_no":"ORDER17563233527881","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"45f7f03ad0edd5b731980b874e0a6be5","sign_type":"MD5"}
[2025-08-28 03:36:17] [INFO] POST参数: []
[2025-08-28 03:36:17] [INFO] 原始输入: 
[2025-08-28 03:36:17] [INFO] 解析参数 - 订单号: ORDER17563233527881, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-28 03:36:17] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17563233527881&pid=2222&trade_no=2025082803364276521&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-28 03:36:17] [INFO] 签名验证 - 期望签名: 45f7f03ad0edd5b731980b874e0a6be5, 实际签名: 45f7f03ad0edd5b731980b874e0a6be5
[2025-08-28 03:36:17] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17563233527881&payment_status=paid
[2025-08-28 03:36:17] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563233527881","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563233527881","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-28 03:35:52","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-28 03:35:52","updated_at":"2025-08-28 03:36:17"}}}
[2025-08-28 03:36:17] [SUCCESS] 订单 ORDER17563233527881 支付状态更新成功
[2025-08-28 04:38:03] [INFO] 收到支付通知
[2025-08-28 04:38:03] [INFO] GET参数: {"pid":"2222","trade_no":"2025082804382253514","out_trade_no":"ORDER17563270459479","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"dc662a5361ce24a857345d01ad3a8570","sign_type":"MD5"}
[2025-08-28 04:38:03] [INFO] POST参数: []
[2025-08-28 04:38:03] [INFO] 原始输入: 
[2025-08-28 04:38:03] [INFO] 解析参数 - 订单号: ORDER17563270459479, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-28 04:38:03] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17563270459479&pid=2222&trade_no=2025082804382253514&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-28 04:38:03] [INFO] 签名验证 - 期望签名: dc662a5361ce24a857345d01ad3a8570, 实际签名: dc662a5361ce24a857345d01ad3a8570
[2025-08-28 04:38:03] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17563270459479&payment_status=paid
[2025-08-28 04:38:03] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563270459479","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563270459479","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-28 04:37:25","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-28 04:37:25","updated_at":"2025-08-28 04:38:03"}}}
[2025-08-28 04:38:03] [SUCCESS] 订单 ORDER17563270459479 支付状态更新成功
[2025-08-28 09:40:39] [INFO] 收到支付通知
[2025-08-28 09:40:39] [INFO] GET参数: {"pid":"2222","trade_no":"2025082809410562194","out_trade_no":"ORDER17563452085116","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"fe0152fc3904b5a38d8db48ca5bc865e","sign_type":"MD5"}
[2025-08-28 09:40:39] [INFO] POST参数: []
[2025-08-28 09:40:39] [INFO] 原始输入: 
[2025-08-28 09:40:39] [INFO] 解析参数 - 订单号: ORDER17563452085116, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-28 09:40:39] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17563452085116&pid=2222&trade_no=2025082809410562194&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-28 09:40:39] [INFO] 签名验证 - 期望签名: fe0152fc3904b5a38d8db48ca5bc865e, 实际签名: fe0152fc3904b5a38d8db48ca5bc865e
[2025-08-28 09:40:39] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17563452085116&payment_status=paid
[2025-08-28 09:40:39] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563452085116","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563452085116","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-28 09:40:08","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-28 09:40:08","updated_at":"2025-08-28 09:40:39"}}}
[2025-08-28 09:40:39] [SUCCESS] 订单 ORDER17563452085116 支付状态更新成功
[2025-08-28 12:57:40] [INFO] 收到支付通知
[2025-08-28 12:57:40] [INFO] GET参数: {"pid":"2222","trade_no":"2025082812580866902","out_trade_no":"ORDER17563570238488","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"ce8358d7392c5c418ad1ef86b53f89f2","sign_type":"MD5"}
[2025-08-28 12:57:40] [INFO] POST参数: []
[2025-08-28 12:57:40] [INFO] 原始输入: 
[2025-08-28 12:57:40] [INFO] 解析参数 - 订单号: ORDER17563570238488, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-28 12:57:40] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17563570238488&pid=2222&trade_no=2025082812580866902&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-28 12:57:40] [INFO] 签名验证 - 期望签名: ce8358d7392c5c418ad1ef86b53f89f2, 实际签名: ce8358d7392c5c418ad1ef86b53f89f2
[2025-08-28 12:57:40] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17563570238488&payment_status=paid
[2025-08-28 12:57:40] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563570238488","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563570238488","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-28 12:57:03","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-28 12:57:03","updated_at":"2025-08-28 12:57:40"}}}
[2025-08-28 12:57:40] [SUCCESS] 订单 ORDER17563570238488 支付状态更新成功
[2025-08-28 18:28:23] [INFO] 收到支付通知
[2025-08-28 18:28:23] [INFO] GET参数: {"pid":"2222","trade_no":"2025082818284592810","out_trade_no":"ORDER17563768755388","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"a29ec99e61f6c615a3c3b7b22cbd61b9","sign_type":"MD5"}
[2025-08-28 18:28:23] [INFO] POST参数: []
[2025-08-28 18:28:23] [INFO] 原始输入: 
[2025-08-28 18:28:23] [INFO] 解析参数 - 订单号: ORDER17563768755388, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-28 18:28:23] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17563768755388&pid=2222&trade_no=2025082818284592810&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-28 18:28:23] [INFO] 签名验证 - 期望签名: a29ec99e61f6c615a3c3b7b22cbd61b9, 实际签名: a29ec99e61f6c615a3c3b7b22cbd61b9
[2025-08-28 18:28:23] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17563768755388&payment_status=paid
[2025-08-28 18:28:23] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563768755388","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563768755388","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-28 18:27:55","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-28 18:27:55","updated_at":"2025-08-28 18:28:23"}}}
[2025-08-28 18:28:23] [SUCCESS] 订单 ORDER17563768755388 支付状态更新成功
[2025-08-28 18:59:26] [INFO] 收到支付通知
[2025-08-28 18:59:26] [INFO] GET参数: {"pid":"2222","trade_no":"2025082818595240246","out_trade_no":"ORDER17563787362714","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"b7b42be0da385a63b621e9c0354d8d40","sign_type":"MD5"}
[2025-08-28 18:59:26] [INFO] POST参数: []
[2025-08-28 18:59:26] [INFO] 原始输入: 
[2025-08-28 18:59:26] [INFO] 解析参数 - 订单号: ORDER17563787362714, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-28 18:59:26] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17563787362714&pid=2222&trade_no=2025082818595240246&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-28 18:59:26] [INFO] 签名验证 - 期望签名: b7b42be0da385a63b621e9c0354d8d40, 实际签名: b7b42be0da385a63b621e9c0354d8d40
[2025-08-28 18:59:26] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17563787362714&payment_status=paid
[2025-08-28 18:59:26] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563787362714","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563787362714","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-28 18:58:56","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-28 18:58:56","updated_at":"2025-08-28 18:59:26"}}}
[2025-08-28 18:59:26] [SUCCESS] 订单 ORDER17563787362714 支付状态更新成功
[2025-08-28 23:13:14] [INFO] 收到支付通知
[2025-08-28 23:13:14] [INFO] GET参数: {"pid":"2222","trade_no":"2025082823133951582","out_trade_no":"ORDER17563939651290","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"e659db89119922ddf205013ceaa83dc2","sign_type":"MD5"}
[2025-08-28 23:13:14] [INFO] POST参数: []
[2025-08-28 23:13:14] [INFO] 原始输入: 
[2025-08-28 23:13:14] [INFO] 解析参数 - 订单号: ORDER17563939651290, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-28 23:13:14] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17563939651290&pid=2222&trade_no=2025082823133951582&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-28 23:13:14] [INFO] 签名验证 - 期望签名: e659db89119922ddf205013ceaa83dc2, 实际签名: e659db89119922ddf205013ceaa83dc2
[2025-08-28 23:13:14] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17563939651290&payment_status=paid
[2025-08-28 23:13:14] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563939651290","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563939651290","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-28 23:12:45","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-28 23:12:45","updated_at":"2025-08-28 23:13:14"}}}
[2025-08-28 23:13:14] [SUCCESS] 订单 ORDER17563939651290 支付状态更新成功
