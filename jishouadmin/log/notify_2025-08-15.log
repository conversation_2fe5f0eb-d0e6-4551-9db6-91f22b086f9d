[2025-08-15 14:28:37] [INFO] 收到支付通知
[2025-08-15 14:28:37] [INFO] GET参数: {"pid":"2222","trade_no":"2025081514282862336","out_trade_no":"ORDER17552392914677","type":"alipay","name":"product","money":"93.28","trade_status":"TRADE_SUCCESS","sign":"a6d0e552c7ed0c66708ff0927f07c818","sign_type":"MD5"}
[2025-08-15 14:28:37] [INFO] POST参数: []
[2025-08-15 14:28:37] [INFO] 原始输入: 
[2025-08-15 14:28:37] [INFO] 解析参数 - 订单号: ORDER17552392914677, 支付状态: TRADE_SUCCESS, 金额: 93.28
[2025-08-15 14:28:37] [INFO] 签名验证 - 原始字符串: money=93.28&name=product&out_trade_no=ORDER17552392914677&pid=2222&trade_no=2025081514282862336&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-15 14:28:37] [INFO] 签名验证 - 期望签名: a6d0e552c7ed0c66708ff0927f07c818, 实际签名: a6d0e552c7ed0c66708ff0927f07c818
[2025-08-15 14:28:37] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17552392914677&payment_status=paid
[2025-08-15 14:28:37] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17552392914677","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17552392914677","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"93.28","purchase_time":"2025-08-15 14:28:11","delivery_content":"gBCWMRM0","order_status":"paid","customer_contact":"web_1755239289845_3l00yth1c","created_at":"2025-08-15 14:28:11","updated_at":"2025-08-15 14:28:37"}}}
[2025-08-15 14:28:37] [SUCCESS] 订单 ORDER17552392914677 支付状态更新成功
[2025-08-15 14:45:53] [INFO] 收到支付通知
[2025-08-15 14:45:53] [INFO] GET参数: {"pid":"2222","trade_no":"2025081514454433754","out_trade_no":"ORDER17552403263929","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"4c0fc5a74e5a7bf34328bee0a46e4812","sign_type":"MD5"}
[2025-08-15 14:45:53] [INFO] POST参数: []
[2025-08-15 14:45:53] [INFO] 原始输入: 
[2025-08-15 14:45:53] [INFO] 解析参数 - 订单号: ORDER17552403263929, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-15 14:45:53] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17552403263929&pid=2222&trade_no=2025081514454433754&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-15 14:45:53] [INFO] 签名验证 - 期望签名: 4c0fc5a74e5a7bf34328bee0a46e4812, 实际签名: 4c0fc5a74e5a7bf34328bee0a46e4812
[2025-08-15 14:45:53] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17552403263929&payment_status=paid
[2025-08-15 14:45:53] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17552403263929","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17552403263929","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-15 14:45:26","delivery_content":"y1iGWfqvEgFD7Xw","order_status":"paid","customer_contact":"web_1755240325807_hr4jqf0i1","created_at":"2025-08-15 14:45:26","updated_at":"2025-08-15 14:45:53"}}}
[2025-08-15 14:45:53] [SUCCESS] 订单 ORDER17552403263929 支付状态更新成功
[2025-08-15 20:16:30] [INFO] 收到支付通知
[2025-08-15 20:16:30] [INFO] GET参数: {"pid":"2222","trade_no":"2025081520161228869","out_trade_no":"ORDER17552601557548","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"cd5c665dec248a814e8c9bdfc6b9608a","sign_type":"MD5"}
[2025-08-15 20:16:30] [INFO] POST参数: []
[2025-08-15 20:16:30] [INFO] 原始输入: 
[2025-08-15 20:16:30] [INFO] 解析参数 - 订单号: ORDER17552601557548, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-15 20:16:30] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17552601557548&pid=2222&trade_no=2025081520161228869&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-15 20:16:30] [INFO] 签名验证 - 期望签名: cd5c665dec248a814e8c9bdfc6b9608a, 实际签名: cd5c665dec248a814e8c9bdfc6b9608a
[2025-08-15 20:16:30] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17552601557548&payment_status=paid
[2025-08-15 20:16:30] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17552601557548","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17552601557548","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-15 20:15:55","delivery_content":"oTq1xsrMNeZhDB7","order_status":"paid","customer_contact":"web_1755260152976_rlzhkagza","created_at":"2025-08-15 20:15:55","updated_at":"2025-08-15 20:16:30"}}}
[2025-08-15 20:16:30] [SUCCESS] 订单 ORDER17552601557548 支付状态更新成功
[2025-08-15 23:00:25] [INFO] 收到支付通知
[2025-08-15 23:00:25] [INFO] GET参数: {"pid":"2222","trade_no":"2025081523000583958","out_trade_no":"ORDER17552699772881","type":"wxpay","name":"product","money":"26.5","trade_status":"TRADE_SUCCESS","sign":"e8c860ec22dc73d78b83f713824aed97","sign_type":"MD5"}
[2025-08-15 23:00:25] [INFO] POST参数: []
[2025-08-15 23:00:25] [INFO] 原始输入: 
[2025-08-15 23:00:25] [INFO] 解析参数 - 订单号: ORDER17552699772881, 支付状态: TRADE_SUCCESS, 金额: 26.5
[2025-08-15 23:00:25] [INFO] 签名验证 - 原始字符串: money=26.5&name=product&out_trade_no=ORDER17552699772881&pid=2222&trade_no=2025081523000583958&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-15 23:00:25] [INFO] 签名验证 - 期望签名: e8c860ec22dc73d78b83f713824aed97, 实际签名: e8c860ec22dc73d78b83f713824aed97
[2025-08-15 23:00:25] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17552699772881&payment_status=paid
[2025-08-15 23:00:26] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17552699772881","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17552699772881","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"26.50","purchase_time":"2025-08-15 22:59:37","delivery_content":"","order_status":"paid","customer_contact":"7108070826","created_at":"2025-08-15 22:59:37","updated_at":"2025-08-15 23:00:26"}}}
[2025-08-15 23:00:26] [SUCCESS] 订单 ORDER17552699772881 支付状态更新成功
