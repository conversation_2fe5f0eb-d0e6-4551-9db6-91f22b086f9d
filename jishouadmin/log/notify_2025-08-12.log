[2025-08-12 23:39:42] [INFO] 收到支付通知
[2025-08-12 23:39:42] [INFO] GET参数: {"pid":"2222","trade_no":"2025081223315529730","out_trade_no":"ORDER17550126996416","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"01f3920f3a55ad6fdb753035d0d32b78","sign_type":"MD5"}
[2025-08-12 23:39:42] [INFO] POST参数: []
[2025-08-12 23:39:42] [INFO] 原始输入: 
[2025-08-12 23:39:42] [INFO] 解析参数 - 订单号: ORDER17550126996416, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-12 23:39:42] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550126996416&pid=2222&trade_no=2025081223315529730&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-12 23:39:42] [INFO] 签名验证 - 期望签名: 01f3920f3a55ad6fdb753035d0d32b78, 实际签名: 01f3920f3a55ad6fdb753035d0d32b78
[2025-08-12 23:39:42] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550126996416&payment_status=paid
[2025-08-12 23:39:42] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-12 23:39:42] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-12 23:40:47] [INFO] 收到支付通知
[2025-08-12 23:40:47] [INFO] GET参数: {"pid":"2222","trade_no":"2025081223315529730","out_trade_no":"ORDER17550126996416","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"01f3920f3a55ad6fdb753035d0d32b78","sign_type":"MD5"}
[2025-08-12 23:40:47] [INFO] POST参数: []
[2025-08-12 23:40:47] [INFO] 原始输入: 
[2025-08-12 23:40:47] [INFO] 解析参数 - 订单号: ORDER17550126996416, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-12 23:40:47] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550126996416&pid=2222&trade_no=2025081223315529730&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-12 23:40:47] [INFO] 签名验证 - 期望签名: 01f3920f3a55ad6fdb753035d0d32b78, 实际签名: 01f3920f3a55ad6fdb753035d0d32b78
[2025-08-12 23:40:47] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550126996416&payment_status=paid
[2025-08-12 23:40:47] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-12 23:40:47] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-12 23:42:57] [INFO] 收到支付通知
[2025-08-12 23:42:57] [INFO] GET参数: {"pid":"2222","trade_no":"2025081223315529730","out_trade_no":"ORDER17550126996416","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"01f3920f3a55ad6fdb753035d0d32b78","sign_type":"MD5"}
[2025-08-12 23:42:57] [INFO] POST参数: []
[2025-08-12 23:42:57] [INFO] 原始输入: 
[2025-08-12 23:42:57] [INFO] 解析参数 - 订单号: ORDER17550126996416, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-12 23:42:57] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550126996416&pid=2222&trade_no=2025081223315529730&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-12 23:42:57] [INFO] 签名验证 - 期望签名: 01f3920f3a55ad6fdb753035d0d32b78, 实际签名: 01f3920f3a55ad6fdb753035d0d32b78
[2025-08-12 23:42:57] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550126996416&payment_status=paid
[2025-08-12 23:42:57] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-12 23:42:57] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-12 23:58:59] [INFO] 收到支付通知
[2025-08-12 23:58:59] [INFO] GET参数: {"pid":"2222","trade_no":"2025081223315529730","out_trade_no":"ORDER17550126996416","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"01f3920f3a55ad6fdb753035d0d32b78","sign_type":"MD5"}
[2025-08-12 23:58:59] [INFO] POST参数: []
[2025-08-12 23:58:59] [INFO] 原始输入: 
[2025-08-12 23:58:59] [INFO] 解析参数 - 订单号: ORDER17550126996416, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-12 23:58:59] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550126996416&pid=2222&trade_no=2025081223315529730&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-12 23:58:59] [INFO] 签名验证 - 期望签名: 01f3920f3a55ad6fdb753035d0d32b78, 实际签名: 01f3920f3a55ad6fdb753035d0d32b78
[2025-08-12 23:58:59] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550126996416&payment_status=paid
[2025-08-12 23:58:59] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-12 23:58:59] [ERROR] 订单状态更新失败: 订单不存在
