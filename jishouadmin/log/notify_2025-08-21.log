[2025-08-21 01:02:13] [INFO] 收到支付通知
[2025-08-21 01:02:13] [INFO] GET参数: {"pid":"2222","trade_no":"2025082101021156671","out_trade_no":"ORDER17557093019462","type":"wxpay","name":"product","money":"26.5","trade_status":"TRADE_SUCCESS","sign":"ffe4a978d53732744c734e00b33eafed","sign_type":"MD5"}
[2025-08-21 01:02:13] [INFO] POST参数: []
[2025-08-21 01:02:13] [INFO] 原始输入: 
[2025-08-21 01:02:13] [INFO] 解析参数 - 订单号: ORDER17557093019462, 支付状态: TRADE_SUCCESS, 金额: 26.5
[2025-08-21 01:02:13] [INFO] 签名验证 - 原始字符串: money=26.5&name=product&out_trade_no=ORDER17557093019462&pid=2222&trade_no=2025082101021156671&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-21 01:02:13] [INFO] 签名验证 - 期望签名: ffe4a978d53732744c734e00b33eafed, 实际签名: ffe4a978d53732744c734e00b33eafed
[2025-08-21 01:02:13] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17557093019462&payment_status=paid
[2025-08-21 01:02:13] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17557093019462","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17557093019462","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"26.50","purchase_time":"2025-08-21 01:01:41","delivery_content":"","order_status":"paid","customer_contact":"6390445671","created_at":"2025-08-21 01:01:41","updated_at":"2025-08-21 01:02:13"}}}
[2025-08-21 01:02:13] [SUCCESS] 订单 ORDER17557093019462 支付状态更新成功
[2025-08-21 02:51:52] [INFO] 收到支付通知
[2025-08-21 02:51:52] [INFO] GET参数: {"pid":"2222","trade_no":"2025082102513933549","out_trade_no":"ORDER17557158673736","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"d4efe7d2a6cc53f2049613879658c884","sign_type":"MD5"}
[2025-08-21 02:51:52] [INFO] POST参数: []
[2025-08-21 02:51:52] [INFO] 原始输入: 
[2025-08-21 02:51:52] [INFO] 解析参数 - 订单号: ORDER17557158673736, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-21 02:51:52] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17557158673736&pid=2222&trade_no=2025082102513933549&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-21 02:51:52] [INFO] 签名验证 - 期望签名: d4efe7d2a6cc53f2049613879658c884, 实际签名: d4efe7d2a6cc53f2049613879658c884
[2025-08-21 02:51:52] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17557158673736&payment_status=paid
[2025-08-21 02:51:52] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17557158673736","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17557158673736","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-21 02:51:07","delivery_content":"DOTZ7Wdt4CXabJS","order_status":"paid","customer_contact":"web_1755715866145_uh0uus2ao","created_at":"2025-08-21 02:51:07","updated_at":"2025-08-21 02:51:52"}}}
[2025-08-21 02:51:52] [SUCCESS] 订单 ORDER17557158673736 支付状态更新成功
[2025-08-21 15:12:58] [INFO] 收到支付通知
[2025-08-21 15:12:58] [INFO] GET参数: {"pid":"2222","trade_no":"2025082115124739064","out_trade_no":"ORDER17557603346223","type":"wxpay","name":"product","money":"24.54","trade_status":"TRADE_SUCCESS","sign":"3bf3dc8b46c308e00483b962778ea430","sign_type":"MD5"}
[2025-08-21 15:12:58] [INFO] POST参数: []
[2025-08-21 15:12:58] [INFO] 原始输入: 
[2025-08-21 15:12:58] [INFO] 解析参数 - 订单号: ORDER17557603346223, 支付状态: TRADE_SUCCESS, 金额: 24.54
[2025-08-21 15:12:58] [INFO] 签名验证 - 原始字符串: money=24.54&name=product&out_trade_no=ORDER17557603346223&pid=2222&trade_no=2025082115124739064&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-21 15:12:58] [INFO] 签名验证 - 期望签名: 3bf3dc8b46c308e00483b962778ea430, 实际签名: 3bf3dc8b46c308e00483b962778ea430
[2025-08-21 15:12:58] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17557603346223&payment_status=paid
[2025-08-21 15:12:58] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17557603346223","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17557603346223","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"24.54","purchase_time":"2025-08-21 15:12:14","delivery_content":"Kfkmg3I5Wpxi1LD","order_status":"paid","customer_contact":"web_1755760332159_kk2ys683p","created_at":"2025-08-21 15:12:14","updated_at":"2025-08-21 15:12:58"}}}
[2025-08-21 15:12:58] [SUCCESS] 订单 ORDER17557603346223 支付状态更新成功
[2025-08-21 15:41:57] [INFO] 收到支付通知
[2025-08-21 15:41:57] [INFO] GET参数: {"pid":"2222","trade_no":"2025082115415591072","out_trade_no":"ORDER17557620676512","type":"wxpay","name":"product","money":"24.54","trade_status":"TRADE_SUCCESS","sign":"c6d494a620eac6680ad7641c4450ba80","sign_type":"MD5"}
[2025-08-21 15:41:57] [INFO] POST参数: []
[2025-08-21 15:41:57] [INFO] 原始输入: 
[2025-08-21 15:41:57] [INFO] 解析参数 - 订单号: ORDER17557620676512, 支付状态: TRADE_SUCCESS, 金额: 24.54
[2025-08-21 15:41:57] [INFO] 签名验证 - 原始字符串: money=24.54&name=product&out_trade_no=ORDER17557620676512&pid=2222&trade_no=2025082115415591072&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-21 15:41:57] [INFO] 签名验证 - 期望签名: c6d494a620eac6680ad7641c4450ba80, 实际签名: c6d494a620eac6680ad7641c4450ba80
[2025-08-21 15:41:57] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17557620676512&payment_status=paid
[2025-08-21 15:41:57] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17557620676512","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17557620676512","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"24.54","purchase_time":"2025-08-21 15:41:07","delivery_content":"TKsLAiBYNaIQjm6","order_status":"paid","customer_contact":"7534128303","created_at":"2025-08-21 15:41:07","updated_at":"2025-08-21 15:41:57"}}}
[2025-08-21 15:41:57] [SUCCESS] 订单 ORDER17557620676512 支付状态更新成功
[2025-08-21 23:41:23] [INFO] 收到支付通知
[2025-08-21 23:41:23] [INFO] GET参数: {"pid":"2222","trade_no":"2025082123413451544","out_trade_no":"ORDER17557908362549","type":"wxpay","name":"product","money":"0.11","trade_status":"TRADE_SUCCESS","sign":"0a2c1c255f47f4422a86dc1801a50f9f","sign_type":"MD5"}
[2025-08-21 23:41:23] [INFO] POST参数: []
[2025-08-21 23:41:23] [INFO] 原始输入: 
[2025-08-21 23:41:23] [INFO] 解析参数 - 订单号: ORDER17557908362549, 支付状态: TRADE_SUCCESS, 金额: 0.11
[2025-08-21 23:41:23] [INFO] 签名验证 - 原始字符串: money=0.11&name=product&out_trade_no=ORDER17557908362549&pid=2222&trade_no=2025082123413451544&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-21 23:41:23] [INFO] 签名验证 - 期望签名: 0a2c1c255f47f4422a86dc1801a50f9f, 实际签名: 0a2c1c255f47f4422a86dc1801a50f9f
[2025-08-21 23:41:23] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17557908362549&payment_status=paid
[2025-08-21 23:41:23] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17557908362549","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17557908362549","merchant_id":"7346442935","product_name":"支付通道","product_price":"0.11","purchase_time":"2025-08-21 23:40:36","delivery_content":"62344621","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-21 23:40:36","updated_at":"2025-08-21 23:41:23"}}}
[2025-08-21 23:41:23] [SUCCESS] 订单 ORDER17557908362549 支付状态更新成功
