[2025-08-30 00:33:10] [INFO] 收到支付通知
[2025-08-30 00:33:10] [INFO] GET参数: {"pid":"2222","trade_no":"2025083000333835651","out_trade_no":"ORDER17564851606028","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"ad8165a0600a511e080242562ccec615","sign_type":"MD5"}
[2025-08-30 00:33:10] [INFO] POST参数: []
[2025-08-30 00:33:10] [INFO] 原始输入: 
[2025-08-30 00:33:10] [INFO] 解析参数 - 订单号: ORDER17564851606028, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-08-30 00:33:10] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17564851606028&pid=2222&trade_no=2025083000333835651&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-30 00:33:10] [INFO] 签名验证 - 期望签名: ad8165a0600a511e080242562ccec615, 实际签名: ad8165a0600a511e080242562ccec615
[2025-08-30 00:33:10] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17564851606028&payment_status=paid
[2025-08-30 00:33:10] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17564851606028","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17564851606028","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-08-30 00:32:40","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-30 00:32:40","updated_at":"2025-08-30 00:33:10"}}}
[2025-08-30 00:33:10] [SUCCESS] 订单 ORDER17564851606028 支付状态更新成功
[2025-08-30 10:16:20] [INFO] 收到支付通知
[2025-08-30 10:16:20] [INFO] GET参数: {"pid":"2222","trade_no":"2025083010164665202","out_trade_no":"ORDER17565201334968","type":"alipay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"30a940f17959e50814f92cadca840f27","sign_type":"MD5"}
[2025-08-30 10:16:20] [INFO] POST参数: []
[2025-08-30 10:16:20] [INFO] 原始输入: 
[2025-08-30 10:16:20] [INFO] 解析参数 - 订单号: ORDER17565201334968, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-30 10:16:20] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17565201334968&pid=2222&trade_no=2025083010164665202&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-30 10:16:20] [INFO] 签名验证 - 期望签名: 30a940f17959e50814f92cadca840f27, 实际签名: 30a940f17959e50814f92cadca840f27
[2025-08-30 10:16:20] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17565201334968&payment_status=paid
[2025-08-30 10:16:20] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17565201334968","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17565201334968","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-30 10:15:33","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-30 10:15:33","updated_at":"2025-08-30 10:16:20"}}}
[2025-08-30 10:16:20] [SUCCESS] 订单 ORDER17565201334968 支付状态更新成功
[2025-08-30 13:47:24] [INFO] 收到支付通知
[2025-08-30 13:47:24] [INFO] GET参数: {"pid":"2222","trade_no":"2025083013475037074","out_trade_no":"ORDER17565327983057","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"cedeea14c67227357213006deca54f77","sign_type":"MD5"}
[2025-08-30 13:47:24] [INFO] POST参数: []
[2025-08-30 13:47:24] [INFO] 原始输入: 
[2025-08-30 13:47:24] [INFO] 解析参数 - 订单号: ORDER17565327983057, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-30 13:47:24] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17565327983057&pid=2222&trade_no=2025083013475037074&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-30 13:47:24] [INFO] 签名验证 - 期望签名: cedeea14c67227357213006deca54f77, 实际签名: cedeea14c67227357213006deca54f77
[2025-08-30 13:47:24] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17565327983057&payment_status=paid
[2025-08-30 13:47:24] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17565327983057","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17565327983057","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-30 13:46:38","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-30 13:46:38","updated_at":"2025-08-30 13:47:24"}}}
[2025-08-30 13:47:24] [SUCCESS] 订单 ORDER17565327983057 支付状态更新成功
[2025-08-30 22:12:08] [INFO] 收到支付通知
[2025-08-30 22:12:08] [INFO] GET参数: {"pid":"2222","trade_no":"2025083022124195085","out_trade_no":"ORDER17565631016294","type":"alipay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"155e1ad720072b86df51ca4a52d2f083","sign_type":"MD5"}
[2025-08-30 22:12:08] [INFO] POST参数: []
[2025-08-30 22:12:08] [INFO] 原始输入: 
[2025-08-30 22:12:08] [INFO] 解析参数 - 订单号: ORDER17565631016294, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-08-30 22:12:08] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17565631016294&pid=2222&trade_no=2025083022124195085&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-30 22:12:08] [INFO] 签名验证 - 期望签名: 155e1ad720072b86df51ca4a52d2f083, 实际签名: 155e1ad720072b86df51ca4a52d2f083
[2025-08-30 22:12:08] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17565631016294&payment_status=paid
[2025-08-30 22:12:08] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17565631016294","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17565631016294","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-08-30 22:11:41","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-30 22:11:41","updated_at":"2025-08-30 22:12:08"}}}
[2025-08-30 22:12:08] [SUCCESS] 订单 ORDER17565631016294 支付状态更新成功
