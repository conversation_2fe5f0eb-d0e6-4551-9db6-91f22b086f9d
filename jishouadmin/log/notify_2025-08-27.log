[2025-08-27 01:22:17] [INFO] 收到支付通知
[2025-08-27 01:22:17] [INFO] GET参数: {"pid":"2222","trade_no":"2025082701223764436","out_trade_no":"ORDER17562289071607","type":"alipay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"87f5de6cae157c4ada772d42e4ead182","sign_type":"MD5"}
[2025-08-27 01:22:17] [INFO] POST参数: []
[2025-08-27 01:22:17] [INFO] 原始输入: 
[2025-08-27 01:22:17] [INFO] 解析参数 - 订单号: ORDER17562289071607, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-27 01:22:17] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17562289071607&pid=2222&trade_no=2025082701223764436&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-27 01:22:17] [INFO] 签名验证 - 期望签名: 87f5de6cae157c4ada772d42e4ead182, 实际签名: 87f5de6cae157c4ada772d42e4ead182
[2025-08-27 01:22:17] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17562289071607&payment_status=paid
[2025-08-27 01:22:17] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17562289071607","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17562289071607","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-27 01:21:47","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-27 01:21:47","updated_at":"2025-08-27 01:22:17"}}}
[2025-08-27 01:22:17] [SUCCESS] 订单 ORDER17562289071607 支付状态更新成功
[2025-08-27 17:33:22] [INFO] 收到支付通知
[2025-08-27 17:33:22] [INFO] GET参数: {"pid":"2222","trade_no":"2025082717334190429","out_trade_no":"ORDER17562871674043","type":"alipay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"2f7ca630b01c3d294f31646da37cde4d","sign_type":"MD5"}
[2025-08-27 17:33:22] [INFO] POST参数: []
[2025-08-27 17:33:22] [INFO] 原始输入: 
[2025-08-27 17:33:22] [INFO] 解析参数 - 订单号: ORDER17562871674043, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-27 17:33:22] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17562871674043&pid=2222&trade_no=2025082717334190429&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-27 17:33:22] [INFO] 签名验证 - 期望签名: 2f7ca630b01c3d294f31646da37cde4d, 实际签名: 2f7ca630b01c3d294f31646da37cde4d
[2025-08-27 17:33:22] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17562871674043&payment_status=paid
[2025-08-27 17:33:22] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17562871674043","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17562871674043","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-27 17:32:47","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-27 17:32:47","updated_at":"2025-08-27 17:33:22"}}}
[2025-08-27 17:33:22] [SUCCESS] 订单 ORDER17562871674043 支付状态更新成功
[2025-08-27 22:13:10] [INFO] 收到支付通知
[2025-08-27 22:13:10] [INFO] GET参数: {"pid":"2222","trade_no":"2025082722134131289","out_trade_no":"ORDER17563039089151","type":"alipay","name":"product","money":"29.73","trade_status":"TRADE_SUCCESS","sign":"16cc0053561e7b5c03710307ec473157","sign_type":"MD5"}
[2025-08-27 22:13:10] [INFO] POST参数: []
[2025-08-27 22:13:10] [INFO] 原始输入: 
[2025-08-27 22:13:10] [INFO] 解析参数 - 订单号: ORDER17563039089151, 支付状态: TRADE_SUCCESS, 金额: 29.73
[2025-08-27 22:13:10] [INFO] 签名验证 - 原始字符串: money=29.73&name=product&out_trade_no=ORDER17563039089151&pid=2222&trade_no=2025082722134131289&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-27 22:13:10] [INFO] 签名验证 - 期望签名: 16cc0053561e7b5c03710307ec473157, 实际签名: 16cc0053561e7b5c03710307ec473157
[2025-08-27 22:13:10] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17563039089151&payment_status=paid
[2025-08-27 22:13:10] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563039089151","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563039089151","merchant_id":"7346442935","product_name":"助手月度","product_price":"29.73","purchase_time":"2025-08-27 22:11:48","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-27 22:11:48","updated_at":"2025-08-27 22:13:10"}}}
[2025-08-27 22:13:10] [SUCCESS] 订单 ORDER17563039089151 支付状态更新成功
[2025-08-27 23:34:21] [INFO] 收到支付通知
[2025-08-27 23:34:21] [INFO] GET参数: {"pid":"2222","trade_no":"2025082723344548699","out_trade_no":"ORDER17563088327318","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"0a4fa4d21176256676f12e0777c170e3","sign_type":"MD5"}
[2025-08-27 23:34:21] [INFO] POST参数: []
[2025-08-27 23:34:21] [INFO] 原始输入: 
[2025-08-27 23:34:21] [INFO] 解析参数 - 订单号: ORDER17563088327318, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-27 23:34:21] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17563088327318&pid=2222&trade_no=2025082723344548699&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-27 23:34:21] [INFO] 签名验证 - 期望签名: 0a4fa4d21176256676f12e0777c170e3, 实际签名: 0a4fa4d21176256676f12e0777c170e3
[2025-08-27 23:34:21] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17563088327318&payment_status=paid
[2025-08-27 23:34:21] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17563088327318","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17563088327318","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-27 23:33:52","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-27 23:33:52","updated_at":"2025-08-27 23:34:21"}}}
[2025-08-27 23:34:21] [SUCCESS] 订单 ORDER17563088327318 支付状态更新成功
