[2025-08-24 00:14:43] [INFO] 收到支付通知
[2025-08-24 00:14:43] [INFO] GET参数: {"pid":"2222","trade_no":"2025082400150480524","out_trade_no":"ORDER17559656609230","type":"wxpay","name":"product","money":"24.63","trade_status":"TRADE_SUCCESS","sign":"826b0cc3be61661ad3eb3ee299d02993","sign_type":"MD5"}
[2025-08-24 00:14:43] [INFO] POST参数: []
[2025-08-24 00:14:43] [INFO] 原始输入: 
[2025-08-24 00:14:43] [INFO] 解析参数 - 订单号: ORDER17559656609230, 支付状态: TRADE_SUCCESS, 金额: 24.63
[2025-08-24 00:14:43] [INFO] 签名验证 - 原始字符串: money=24.63&name=product&out_trade_no=ORDER17559656609230&pid=2222&trade_no=2025082400150480524&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 00:14:43] [INFO] 签名验证 - 期望签名: 826b0cc3be61661ad3eb3ee299d02993, 实际签名: 826b0cc3be61661ad3eb3ee299d02993
[2025-08-24 00:14:43] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559656609230&payment_status=paid
[2025-08-24 00:14:43] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559656609230","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559656609230","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.63","purchase_time":"2025-08-24 00:14:20","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 00:14:20","updated_at":"2025-08-24 00:14:43"}}}
[2025-08-24 00:14:43] [SUCCESS] 订单 ORDER17559656609230 支付状态更新成功
[2025-08-24 01:04:13] [INFO] 收到支付通知
[2025-08-24 01:04:13] [INFO] GET参数: {"pid":"2222","trade_no":"2025082401041580472","out_trade_no":"ORDER17559686041250","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"25dc1907ec1a6f94258d1cbf0392294b","sign_type":"MD5"}
[2025-08-24 01:04:13] [INFO] POST参数: []
[2025-08-24 01:04:13] [INFO] 原始输入: 
[2025-08-24 01:04:13] [INFO] 解析参数 - 订单号: ORDER17559686041250, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-24 01:04:13] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559686041250&pid=2222&trade_no=2025082401041580472&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 01:04:13] [INFO] 签名验证 - 期望签名: 25dc1907ec1a6f94258d1cbf0392294b, 实际签名: 25dc1907ec1a6f94258d1cbf0392294b
[2025-08-24 01:04:13] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559686041250&payment_status=paid
[2025-08-24 01:04:13] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559686041250","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559686041250","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-24 01:03:24","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 01:03:24","updated_at":"2025-08-24 01:04:13"}}}
[2025-08-24 01:04:13] [SUCCESS] 订单 ORDER17559686041250 支付状态更新成功
[2025-08-24 03:38:04] [INFO] 收到支付通知
[2025-08-24 03:38:04] [INFO] GET参数: {"pid":"2222","trade_no":"2025082403382370494","out_trade_no":"ORDER17559778287639","type":"alipay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"b07c48c7c340b79b9f822d600994404d","sign_type":"MD5"}
[2025-08-24 03:38:04] [INFO] POST参数: []
[2025-08-24 03:38:04] [INFO] 原始输入: 
[2025-08-24 03:38:04] [INFO] 解析参数 - 订单号: ORDER17559778287639, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-24 03:38:04] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559778287639&pid=2222&trade_no=2025082403382370494&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-24 03:38:04] [INFO] 签名验证 - 期望签名: b07c48c7c340b79b9f822d600994404d, 实际签名: b07c48c7c340b79b9f822d600994404d
[2025-08-24 03:38:04] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559778287639&payment_status=paid
[2025-08-24 03:38:04] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559778287639","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559778287639","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-24 03:37:08","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 03:37:08","updated_at":"2025-08-24 03:38:04"}}}
[2025-08-24 03:38:04] [SUCCESS] 订单 ORDER17559778287639 支付状态更新成功
[2025-08-24 04:54:46] [INFO] 收到支付通知
[2025-08-24 04:54:46] [INFO] GET参数: {"pid":"2222","trade_no":"2025082404543523141","out_trade_no":"ORDER17559824266996","type":"wxpay","name":"product","money":"24.63","trade_status":"TRADE_SUCCESS","sign":"9eaebcffc0ab348bb27838efe57341cf","sign_type":"MD5"}
[2025-08-24 04:54:46] [INFO] POST参数: []
[2025-08-24 04:54:46] [INFO] 原始输入: 
[2025-08-24 04:54:46] [INFO] 解析参数 - 订单号: ORDER17559824266996, 支付状态: TRADE_SUCCESS, 金额: 24.63
[2025-08-24 04:54:46] [INFO] 签名验证 - 原始字符串: money=24.63&name=product&out_trade_no=ORDER17559824266996&pid=2222&trade_no=2025082404543523141&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 04:54:46] [INFO] 签名验证 - 期望签名: 9eaebcffc0ab348bb27838efe57341cf, 实际签名: 9eaebcffc0ab348bb27838efe57341cf
[2025-08-24 04:54:46] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559824266996&payment_status=paid
[2025-08-24 04:54:46] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559824266996","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559824266996","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.63","purchase_time":"2025-08-24 04:53:46","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 04:53:46","updated_at":"2025-08-24 04:54:46"}}}
[2025-08-24 04:54:46] [SUCCESS] 订单 ORDER17559824266996 支付状态更新成功
[2025-08-24 10:07:33] [INFO] 收到支付通知
[2025-08-24 10:07:33] [INFO] GET参数: {"pid":"2222","trade_no":"2025082410075369194","out_trade_no":"ORDER17560012197376","type":"wxpay","name":"product","money":"24.63","trade_status":"TRADE_SUCCESS","sign":"516b3fc0ec006e3fe30f3c2f167c58eb","sign_type":"MD5"}
[2025-08-24 10:07:33] [INFO] POST参数: []
[2025-08-24 10:07:33] [INFO] 原始输入: 
[2025-08-24 10:07:33] [INFO] 解析参数 - 订单号: ORDER17560012197376, 支付状态: TRADE_SUCCESS, 金额: 24.63
[2025-08-24 10:07:33] [INFO] 签名验证 - 原始字符串: money=24.63&name=product&out_trade_no=ORDER17560012197376&pid=2222&trade_no=2025082410075369194&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 10:07:33] [INFO] 签名验证 - 期望签名: 516b3fc0ec006e3fe30f3c2f167c58eb, 实际签名: 516b3fc0ec006e3fe30f3c2f167c58eb
[2025-08-24 10:07:33] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17560012197376&payment_status=paid
[2025-08-24 10:07:33] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560012197376","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560012197376","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.63","purchase_time":"2025-08-24 10:06:59","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 10:06:59","updated_at":"2025-08-24 10:07:33"}}}
[2025-08-24 10:07:33] [SUCCESS] 订单 ORDER17560012197376 支付状态更新成功
[2025-08-24 10:55:08] [INFO] 收到支付通知
[2025-08-24 10:55:08] [INFO] GET参数: {"pid":"2222","trade_no":"2025082410552844945","out_trade_no":"ORDER17560040829442","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"e483a2a173fa0876bc6414ad004a1fe0","sign_type":"MD5"}
[2025-08-24 10:55:08] [INFO] POST参数: []
[2025-08-24 10:55:08] [INFO] 原始输入: 
[2025-08-24 10:55:08] [INFO] 解析参数 - 订单号: ORDER17560040829442, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-24 10:55:08] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17560040829442&pid=2222&trade_no=2025082410552844945&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 10:55:08] [INFO] 签名验证 - 期望签名: e483a2a173fa0876bc6414ad004a1fe0, 实际签名: e483a2a173fa0876bc6414ad004a1fe0
[2025-08-24 10:55:08] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17560040829442&payment_status=paid
[2025-08-24 10:55:08] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560040829442","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560040829442","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-24 10:54:42","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 10:54:42","updated_at":"2025-08-24 10:55:08"}}}
[2025-08-24 10:55:08] [SUCCESS] 订单 ORDER17560040829442 支付状态更新成功
[2025-08-24 11:41:17] [INFO] 收到支付通知
[2025-08-24 11:41:17] [INFO] GET参数: {"pid":"2222","trade_no":"2025082411412681115","out_trade_no":"ORDER17560068213220","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"ad0b9c469b6bdcc2b3d3bce644a5c228","sign_type":"MD5"}
[2025-08-24 11:41:17] [INFO] POST参数: []
[2025-08-24 11:41:17] [INFO] 原始输入: 
[2025-08-24 11:41:17] [INFO] 解析参数 - 订单号: ORDER17560068213220, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-24 11:41:17] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17560068213220&pid=2222&trade_no=2025082411412681115&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 11:41:17] [INFO] 签名验证 - 期望签名: ad0b9c469b6bdcc2b3d3bce644a5c228, 实际签名: ad0b9c469b6bdcc2b3d3bce644a5c228
[2025-08-24 11:41:17] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17560068213220&payment_status=paid
[2025-08-24 11:41:17] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560068213220","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560068213220","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-24 11:40:21","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 11:40:21","updated_at":"2025-08-24 11:41:17"}}}
[2025-08-24 11:41:17] [SUCCESS] 订单 ORDER17560068213220 支付状态更新成功
[2025-08-24 12:49:33] [INFO] 收到支付通知
[2025-08-24 12:49:33] [INFO] GET参数: {"pid":"2222","trade_no":"2025082412495773633","out_trade_no":"ORDER17560109629206","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"8f4d22cce773feb2e46a335659b53047","sign_type":"MD5"}
[2025-08-24 12:49:33] [INFO] POST参数: []
[2025-08-24 12:49:33] [INFO] 原始输入: 
[2025-08-24 12:49:33] [INFO] 解析参数 - 订单号: ORDER17560109629206, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-24 12:49:33] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17560109629206&pid=2222&trade_no=2025082412495773633&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 12:49:33] [INFO] 签名验证 - 期望签名: 8f4d22cce773feb2e46a335659b53047, 实际签名: 8f4d22cce773feb2e46a335659b53047
[2025-08-24 12:49:33] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17560109629206&payment_status=paid
[2025-08-24 12:49:33] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560109629206","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560109629206","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-24 12:49:22","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 12:49:22","updated_at":"2025-08-24 12:49:33"}}}
[2025-08-24 12:49:33] [SUCCESS] 订单 ORDER17560109629206 支付状态更新成功
[2025-08-24 17:07:49] [INFO] 收到支付通知
[2025-08-24 17:07:49] [INFO] GET参数: {"pid":"2222","trade_no":"2025082417075468666","out_trade_no":"ORDER17560264195031","type":"wxpay","name":"product","money":"24.63","trade_status":"TRADE_SUCCESS","sign":"a481d44696ea9ccd3acdca2128378ae9","sign_type":"MD5"}
[2025-08-24 17:07:49] [INFO] POST参数: []
[2025-08-24 17:07:49] [INFO] 原始输入: 
[2025-08-24 17:07:49] [INFO] 解析参数 - 订单号: ORDER17560264195031, 支付状态: TRADE_SUCCESS, 金额: 24.63
[2025-08-24 17:07:49] [INFO] 签名验证 - 原始字符串: money=24.63&name=product&out_trade_no=ORDER17560264195031&pid=2222&trade_no=2025082417075468666&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 17:07:49] [INFO] 签名验证 - 期望签名: a481d44696ea9ccd3acdca2128378ae9, 实际签名: a481d44696ea9ccd3acdca2128378ae9
[2025-08-24 17:07:49] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17560264195031&payment_status=paid
[2025-08-24 17:07:49] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560264195031","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560264195031","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.63","purchase_time":"2025-08-24 17:06:59","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 17:06:59","updated_at":"2025-08-24 17:07:49"}}}
[2025-08-24 17:07:49] [SUCCESS] 订单 ORDER17560264195031 支付状态更新成功
[2025-08-24 20:30:16] [INFO] 收到支付通知
[2025-08-24 20:30:16] [INFO] GET参数: {"pid":"2222","trade_no":"2025082420301450140","out_trade_no":"ORDER17560385587099","type":"alipay","name":"product","money":"24.63","trade_status":"TRADE_SUCCESS","sign":"9c85967f1ab09aab90019217b1c070e5","sign_type":"MD5"}
[2025-08-24 20:30:16] [INFO] POST参数: []
[2025-08-24 20:30:16] [INFO] 原始输入: 
[2025-08-24 20:30:16] [INFO] 解析参数 - 订单号: ORDER17560385587099, 支付状态: TRADE_SUCCESS, 金额: 24.63
[2025-08-24 20:30:16] [INFO] 签名验证 - 原始字符串: money=24.63&name=product&out_trade_no=ORDER17560385587099&pid=2222&trade_no=2025082420301450140&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-24 20:30:16] [INFO] 签名验证 - 期望签名: 9c85967f1ab09aab90019217b1c070e5, 实际签名: 9c85967f1ab09aab90019217b1c070e5
[2025-08-24 20:30:16] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17560385587099&payment_status=paid
[2025-08-24 20:30:16] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560385587099","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560385587099","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.63","purchase_time":"2025-08-24 20:29:18","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 20:29:18","updated_at":"2025-08-24 20:30:16"}}}
[2025-08-24 20:30:16] [SUCCESS] 订单 ORDER17560385587099 支付状态更新成功
[2025-08-24 22:25:26] [INFO] 收到支付通知
[2025-08-24 22:25:26] [INFO] GET参数: {"pid":"2222","trade_no":"2025082422254050406","out_trade_no":"ORDER17560454805684","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"1df91adbd65f705cdc492d32ff27cfa1","sign_type":"MD5"}
[2025-08-24 22:25:26] [INFO] POST参数: []
[2025-08-24 22:25:26] [INFO] 原始输入: 
[2025-08-24 22:25:26] [INFO] 解析参数 - 订单号: ORDER17560454805684, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-24 22:25:26] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17560454805684&pid=2222&trade_no=2025082422254050406&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 22:25:26] [INFO] 签名验证 - 期望签名: 1df91adbd65f705cdc492d32ff27cfa1, 实际签名: 1df91adbd65f705cdc492d32ff27cfa1
[2025-08-24 22:25:26] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17560454805684&payment_status=paid
[2025-08-24 22:25:26] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560454805684","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560454805684","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-24 22:24:40","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 22:24:40","updated_at":"2025-08-24 22:25:26"}}}
[2025-08-24 22:25:26] [SUCCESS] 订单 ORDER17560454805684 支付状态更新成功
[2025-08-24 22:27:09] [INFO] 收到支付通知
[2025-08-24 22:27:09] [INFO] GET参数: {"pid":"2222","trade_no":"2025082422272530024","out_trade_no":"ORDER17560455768358","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"24611d72a4de9c1b413c371d4c91b65e","sign_type":"MD5"}
[2025-08-24 22:27:09] [INFO] POST参数: []
[2025-08-24 22:27:09] [INFO] 原始输入: 
[2025-08-24 22:27:09] [INFO] 解析参数 - 订单号: ORDER17560455768358, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-24 22:27:09] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17560455768358&pid=2222&trade_no=2025082422272530024&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-24 22:27:09] [INFO] 签名验证 - 期望签名: 24611d72a4de9c1b413c371d4c91b65e, 实际签名: 24611d72a4de9c1b413c371d4c91b65e
[2025-08-24 22:27:09] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17560455768358&payment_status=paid
[2025-08-24 22:27:09] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560455768358","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560455768358","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-24 22:26:16","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-24 22:26:16","updated_at":"2025-08-24 22:27:09"}}}
[2025-08-24 22:27:09] [SUCCESS] 订单 ORDER17560455768358 支付状态更新成功
