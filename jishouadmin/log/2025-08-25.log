[2025-08-25 16:05:32] 查询订单号: ORDER17561091182038
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17561091182038
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-25 21:03:34] 查询订单号: ORDER17561269769360
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17561269769360
返回: {"code":1,"msg":"succ","trade_no":"2025082521034623165","out_trade_no":"ORDER17561269769360","api_trade_no":null,"bill_trade_no":null,"type":"wxpay","pid":"2222","addtime":"2025-08-25 21:03:46","endtime":null,"name":"\u7535\u5546\u5355\u53f7:ORDER17561269769360","money":"29.73","param":null,"buyer":null,"status":"0","payurl":null}
[2025-08-25 23:14:16] 查询订单号: ORDER17561348505973
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17561348505973
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
