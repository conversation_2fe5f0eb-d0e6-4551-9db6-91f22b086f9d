[2025-08-20 00:27:28] [INFO] 收到支付通知
[2025-08-20 00:27:28] [INFO] GET参数: {"pid":"2222","trade_no":"2025082000265783133","out_trade_no":"ORDER17556207571481","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"ee793090a8b68627ce305c0e778c6035","sign_type":"MD5"}
[2025-08-20 00:27:28] [INFO] POST参数: []
[2025-08-20 00:27:28] [INFO] 原始输入: 
[2025-08-20 00:27:28] [INFO] 解析参数 - 订单号: ORDER17556207571481, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-20 00:27:28] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17556207571481&pid=2222&trade_no=2025082000265783133&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-20 00:27:28] [INFO] 签名验证 - 期望签名: ee793090a8b68627ce305c0e778c6035, 实际签名: ee793090a8b68627ce305c0e778c6035
[2025-08-20 00:27:28] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17556207571481&payment_status=paid
[2025-08-20 00:27:28] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17556207571481","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17556207571481","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-20 00:25:57","delivery_content":"kw6yEtXzN9qlnQO","order_status":"paid","customer_contact":"7636604605","created_at":"2025-08-20 00:25:57","updated_at":"2025-08-20 00:27:28"}}}
[2025-08-20 00:27:28] [SUCCESS] 订单 ORDER17556207571481 支付状态更新成功
[2025-08-20 00:31:30] [INFO] 收到支付通知
[2025-08-20 00:31:30] [INFO] GET参数: {"pid":"2222","trade_no":"2025082000313525829","out_trade_no":"ORDER17556210664774","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"2dd173ad6172c96503dc86a1aaa4146f","sign_type":"MD5"}
[2025-08-20 00:31:30] [INFO] POST参数: []
[2025-08-20 00:31:30] [INFO] 原始输入: 
[2025-08-20 00:31:30] [INFO] 解析参数 - 订单号: ORDER17556210664774, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-20 00:31:30] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17556210664774&pid=2222&trade_no=2025082000313525829&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-20 00:31:30] [INFO] 签名验证 - 期望签名: 2dd173ad6172c96503dc86a1aaa4146f, 实际签名: 2dd173ad6172c96503dc86a1aaa4146f
[2025-08-20 00:31:30] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17556210664774&payment_status=paid
[2025-08-20 00:31:30] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17556210664774","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17556210664774","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-20 00:31:06","delivery_content":"Q2t8vr90fo61Zce","order_status":"paid","customer_contact":"7636604605","created_at":"2025-08-20 00:31:06","updated_at":"2025-08-20 00:31:30"}}}
[2025-08-20 00:31:30] [SUCCESS] 订单 ORDER17556210664774 支付状态更新成功
[2025-08-20 00:56:48] [INFO] 收到支付通知
[2025-08-20 00:56:48] [INFO] GET参数: []
[2025-08-20 00:56:48] [INFO] POST参数: []
[2025-08-20 00:56:48] [INFO] 原始输入: 
[2025-08-20 00:56:48] [ERROR] 缺少必需参数: pid
[2025-08-20 15:15:30] [INFO] 收到支付通知
[2025-08-20 15:15:30] [INFO] GET参数: {"pid":"2222","trade_no":"2025082015152846777","out_trade_no":"ORDER17556740996032","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"4d0cdf09c69e162096a6f9f3797c6446","sign_type":"MD5"}
[2025-08-20 15:15:30] [INFO] POST参数: []
[2025-08-20 15:15:30] [INFO] 原始输入: 
[2025-08-20 15:15:30] [INFO] 解析参数 - 订单号: ORDER17556740996032, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-20 15:15:30] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17556740996032&pid=2222&trade_no=2025082015152846777&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-20 15:15:30] [INFO] 签名验证 - 期望签名: 4d0cdf09c69e162096a6f9f3797c6446, 实际签名: 4d0cdf09c69e162096a6f9f3797c6446
[2025-08-20 15:15:30] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17556740996032&payment_status=paid
[2025-08-20 15:15:30] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17556740996032","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17556740996032","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-20 15:14:59","delivery_content":"aqQMW4B0T5mltjY","order_status":"paid","customer_contact":"7752807838","created_at":"2025-08-20 15:14:59","updated_at":"2025-08-20 15:15:30"}}}
[2025-08-20 15:15:30] [SUCCESS] 订单 ORDER17556740996032 支付状态更新成功
[2025-08-20 15:18:35] [INFO] 收到支付通知
[2025-08-20 15:18:35] [INFO] GET参数: {"pid":"2222","trade_no":"2025082015181011957","out_trade_no":"ORDER17556742637690","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"1392d58ce53ca70a01c627f298f977dc","sign_type":"MD5"}
[2025-08-20 15:18:35] [INFO] POST参数: []
[2025-08-20 15:18:35] [INFO] 原始输入: 
[2025-08-20 15:18:35] [INFO] 解析参数 - 订单号: ORDER17556742637690, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-20 15:18:35] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17556742637690&pid=2222&trade_no=2025082015181011957&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-20 15:18:35] [INFO] 签名验证 - 期望签名: 1392d58ce53ca70a01c627f298f977dc, 实际签名: 1392d58ce53ca70a01c627f298f977dc
[2025-08-20 15:18:35] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17556742637690&payment_status=paid
[2025-08-20 15:18:35] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17556742637690","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17556742637690","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-20 15:17:43","delivery_content":"HoxtdwKkX4PMfRv","order_status":"paid","customer_contact":"web_1755674262169_n3csxqnoh","created_at":"2025-08-20 15:17:43","updated_at":"2025-08-20 15:18:35"}}}
[2025-08-20 15:18:35] [SUCCESS] 订单 ORDER17556742637690 支付状态更新成功
[2025-08-20 18:34:33] [INFO] 收到支付通知
[2025-08-20 18:34:33] [INFO] GET参数: {"pid":"2222","trade_no":"2025082018335718257","out_trade_no":"ORDER17556860036209","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"466b0531c139c379bcf2934d869095f1","sign_type":"MD5"}
[2025-08-20 18:34:33] [INFO] POST参数: []
[2025-08-20 18:34:33] [INFO] 原始输入: 
[2025-08-20 18:34:33] [INFO] 解析参数 - 订单号: ORDER17556860036209, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-20 18:34:33] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17556860036209&pid=2222&trade_no=2025082018335718257&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-20 18:34:33] [INFO] 签名验证 - 期望签名: 466b0531c139c379bcf2934d869095f1, 实际签名: 466b0531c139c379bcf2934d869095f1
[2025-08-20 18:34:33] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17556860036209&payment_status=paid
[2025-08-20 18:34:33] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17556860036209","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17556860036209","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-20 18:33:23","delivery_content":"BFVMWlZfiPgCxet","order_status":"paid","customer_contact":"web_1755686003806_7aupylngl","created_at":"2025-08-20 18:33:23","updated_at":"2025-08-20 18:34:33"}}}
[2025-08-20 18:34:33] [SUCCESS] 订单 ORDER17556860036209 支付状态更新成功
[2025-08-20 20:21:46] [INFO] 收到支付通知
[2025-08-20 20:21:46] [INFO] GET参数: {"pid":"2222","trade_no":"2025082020215413830","out_trade_no":"ORDER17556924791060","type":"alipay","name":"product","money":"0.11","trade_status":"TRADE_SUCCESS","sign":"245262ab96427d93d3215021c4443604","sign_type":"MD5"}
[2025-08-20 20:21:46] [INFO] POST参数: []
[2025-08-20 20:21:46] [INFO] 原始输入: 
[2025-08-20 20:21:46] [INFO] 解析参数 - 订单号: ORDER17556924791060, 支付状态: TRADE_SUCCESS, 金额: 0.11
[2025-08-20 20:21:46] [INFO] 签名验证 - 原始字符串: money=0.11&name=product&out_trade_no=ORDER17556924791060&pid=2222&trade_no=2025082020215413830&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-20 20:21:46] [INFO] 签名验证 - 期望签名: 245262ab96427d93d3215021c4443604, 实际签名: 245262ab96427d93d3215021c4443604
[2025-08-20 20:21:46] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17556924791060&payment_status=paid
[2025-08-20 20:21:46] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17556924791060","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17556924791060","merchant_id":"7346442935","product_name":"支付通道","product_price":"0.11","purchase_time":"2025-08-20 20:21:19","delivery_content":"34214321","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-20 20:21:19","updated_at":"2025-08-20 20:21:46"}}}
[2025-08-20 20:21:46] [SUCCESS] 订单 ORDER17556924791060 支付状态更新成功
[2025-08-20 22:08:54] [INFO] 收到支付通知
[2025-08-20 22:08:54] [INFO] GET参数: []
[2025-08-20 22:08:54] [INFO] POST参数: []
[2025-08-20 22:08:54] [INFO] 原始输入: 
[2025-08-20 22:08:54] [ERROR] 缺少必需参数: pid
[2025-08-20 22:49:27] [INFO] 收到支付通知
[2025-08-20 22:49:27] [INFO] GET参数: {"pid":"2222","trade_no":"2025082022492262677","out_trade_no":"ORDER17557013344125","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"08814cf72d6a35644e4a69310f394ea1","sign_type":"MD5"}
[2025-08-20 22:49:27] [INFO] POST参数: []
[2025-08-20 22:49:27] [INFO] 原始输入: 
[2025-08-20 22:49:27] [INFO] 解析参数 - 订单号: ORDER17557013344125, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-20 22:49:27] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17557013344125&pid=2222&trade_no=2025082022492262677&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-20 22:49:27] [INFO] 签名验证 - 期望签名: 08814cf72d6a35644e4a69310f394ea1, 实际签名: 08814cf72d6a35644e4a69310f394ea1
[2025-08-20 22:49:27] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17557013344125&payment_status=paid
[2025-08-20 22:49:27] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17557013344125","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17557013344125","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-20 22:48:54","delivery_content":"maPsjXvtBkyug6d","order_status":"paid","customer_contact":"web_1755701335455_ueojjvvwp","created_at":"2025-08-20 22:48:54","updated_at":"2025-08-20 22:49:27"}}}
[2025-08-20 22:49:27] [SUCCESS] 订单 ORDER17557013344125 支付状态更新成功
