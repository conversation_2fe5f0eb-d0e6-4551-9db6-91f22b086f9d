[2025-09-01 00:15:12] [INFO] 收到支付通知
[2025-09-01 00:15:12] [INFO] GET参数: {"pid":"2222","trade_no":"2025090100153629813","out_trade_no":"ORDER17566568772935","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"b94013422e466b3f6ffc87afbe21b274","sign_type":"MD5"}
[2025-09-01 00:15:12] [INFO] POST参数: []
[2025-09-01 00:15:12] [INFO] 原始输入: 
[2025-09-01 00:15:12] [INFO] 解析参数 - 订单号: ORDER17566568772935, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-09-01 00:15:12] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17566568772935&pid=2222&trade_no=2025090100153629813&trade_status=TRADE_SUCCESS&type=wxpay
[2025-09-01 00:15:12] [INFO] 签名验证 - 期望签名: b94013422e466b3f6ffc87afbe21b274, 实际签名: b94013422e466b3f6ffc87afbe21b274
[2025-09-01 00:15:12] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17566568772935&payment_status=paid
[2025-09-01 00:15:12] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17566568772935","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17566568772935","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-09-01 00:14:37","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-01 00:14:37","updated_at":"2025-09-01 00:15:12"}}}
[2025-09-01 00:15:12] [SUCCESS] 订单 ORDER17566568772935 支付状态更新成功
[2025-09-01 02:28:10] [INFO] 收到支付通知
[2025-09-01 02:28:10] [INFO] GET参数: {"pid":"2222","trade_no":"2025090102284311369","out_trade_no":"ORDER17566648339881","type":"alipay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"9e9e0be611066ffed2ea094922671a35","sign_type":"MD5"}
[2025-09-01 02:28:10] [INFO] POST参数: []
[2025-09-01 02:28:10] [INFO] 原始输入: 
[2025-09-01 02:28:10] [INFO] 解析参数 - 订单号: ORDER17566648339881, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-09-01 02:28:10] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17566648339881&pid=2222&trade_no=2025090102284311369&trade_status=TRADE_SUCCESS&type=alipay
[2025-09-01 02:28:10] [INFO] 签名验证 - 期望签名: 9e9e0be611066ffed2ea094922671a35, 实际签名: 9e9e0be611066ffed2ea094922671a35
[2025-09-01 02:28:10] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17566648339881&payment_status=paid
[2025-09-01 02:28:10] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17566648339881","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17566648339881","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-09-01 02:27:13","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-01 02:27:13","updated_at":"2025-09-01 02:28:10"}}}
[2025-09-01 02:28:10] [SUCCESS] 订单 ORDER17566648339881 支付状态更新成功
[2025-09-01 08:07:50] [INFO] 收到支付通知
[2025-09-01 08:07:50] [INFO] GET参数: {"pid":"2222","trade_no":"2025090108082429879","out_trade_no":"ORDER17566852154953","type":"alipay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"c9c7ba6993ff1458de17fed7a9432822","sign_type":"MD5"}
[2025-09-01 08:07:50] [INFO] POST参数: []
[2025-09-01 08:07:50] [INFO] 原始输入: 
[2025-09-01 08:07:50] [INFO] 解析参数 - 订单号: ORDER17566852154953, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-09-01 08:07:50] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17566852154953&pid=2222&trade_no=2025090108082429879&trade_status=TRADE_SUCCESS&type=alipay
[2025-09-01 08:07:50] [INFO] 签名验证 - 期望签名: c9c7ba6993ff1458de17fed7a9432822, 实际签名: c9c7ba6993ff1458de17fed7a9432822
[2025-09-01 08:07:50] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17566852154953&payment_status=paid
[2025-09-01 08:07:50] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17566852154953","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17566852154953","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-09-01 08:06:55","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-01 08:06:55","updated_at":"2025-09-01 08:07:50"}}}
[2025-09-01 08:07:50] [SUCCESS] 订单 ORDER17566852154953 支付状态更新成功
[2025-09-01 12:11:21] [INFO] 收到支付通知
[2025-09-01 12:11:21] [INFO] GET参数: {"pid":"2222","trade_no":"2025090112112911144","out_trade_no":"ORDER17566998259992","type":"alipay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"7ebefa410b7c6ce3916487b28dc456f7","sign_type":"MD5"}
[2025-09-01 12:11:21] [INFO] POST参数: []
[2025-09-01 12:11:21] [INFO] 原始输入: 
[2025-09-01 12:11:21] [INFO] 解析参数 - 订单号: ORDER17566998259992, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-09-01 12:11:21] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17566998259992&pid=2222&trade_no=2025090112112911144&trade_status=TRADE_SUCCESS&type=alipay
[2025-09-01 12:11:21] [INFO] 签名验证 - 期望签名: 7ebefa410b7c6ce3916487b28dc456f7, 实际签名: 7ebefa410b7c6ce3916487b28dc456f7
[2025-09-01 12:11:21] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17566998259992&payment_status=paid
[2025-09-01 12:11:21] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17566998259992","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17566998259992","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-09-01 12:10:25","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-01 12:10:25","updated_at":"2025-09-01 12:11:21"}}}
[2025-09-01 12:11:21] [SUCCESS] 订单 ORDER17566998259992 支付状态更新成功
[2025-09-01 17:28:35] [INFO] 收到支付通知
[2025-09-01 17:28:35] [INFO] GET参数: {"pid":"2222","trade_no":"2025090117284539266","out_trade_no":"ORDER17567188624046","type":"alipay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"d6a1e62562f85dabb4629e60e19ea51c","sign_type":"MD5"}
[2025-09-01 17:28:35] [INFO] POST参数: []
[2025-09-01 17:28:35] [INFO] 原始输入: 
[2025-09-01 17:28:35] [INFO] 解析参数 - 订单号: ORDER17567188624046, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-09-01 17:28:35] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17567188624046&pid=2222&trade_no=2025090117284539266&trade_status=TRADE_SUCCESS&type=alipay
[2025-09-01 17:28:35] [INFO] 签名验证 - 期望签名: d6a1e62562f85dabb4629e60e19ea51c, 实际签名: d6a1e62562f85dabb4629e60e19ea51c
[2025-09-01 17:28:35] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17567188624046&payment_status=paid
[2025-09-01 17:28:35] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17567188624046","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17567188624046","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-09-01 17:27:42","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-01 17:27:42","updated_at":"2025-09-01 17:28:35"}}}
[2025-09-01 17:28:35] [SUCCESS] 订单 ORDER17567188624046 支付状态更新成功
