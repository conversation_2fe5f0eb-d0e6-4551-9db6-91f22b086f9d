[2025-08-19 03:51:49] [INFO] 收到支付通知
[2025-08-19 03:51:49] [INFO] GET参数: {"pid":"2222","trade_no":"2025081903511776261","out_trade_no":"ORDER17555466545308","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"5410b3a651d4e06b04c0da0983421cae","sign_type":"MD5"}
[2025-08-19 03:51:49] [INFO] POST参数: []
[2025-08-19 03:51:49] [INFO] 原始输入: 
[2025-08-19 03:51:49] [INFO] 解析参数 - 订单号: ORDER17555466545308, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-19 03:51:49] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17555466545308&pid=2222&trade_no=2025081903511776261&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-19 03:51:49] [INFO] 签名验证 - 期望签名: 5410b3a651d4e06b04c0da0983421cae, 实际签名: 5410b3a651d4e06b04c0da0983421cae
[2025-08-19 03:51:49] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555466545308&payment_status=paid
[2025-08-19 03:51:49] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555466545308","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555466545308","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-19 03:50:54","delivery_content":"2LIfqFYAzl6WsTd","order_status":"paid","customer_contact":"web_1755546654720_5prielizx","created_at":"2025-08-19 03:50:54","updated_at":"2025-08-19 03:51:49"}}}
[2025-08-19 03:51:49] [SUCCESS] 订单 ORDER17555466545308 支付状态更新成功
[2025-08-19 04:17:54] [INFO] 收到支付通知
[2025-08-19 04:17:54] [INFO] GET参数: {"pid":"2222","trade_no":"2025081904172716854","out_trade_no":"ORDER17555482233840","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"f9f56bb7dfb212c20e54faa96db38fc7","sign_type":"MD5"}
[2025-08-19 04:17:54] [INFO] POST参数: []
[2025-08-19 04:17:54] [INFO] 原始输入: 
[2025-08-19 04:17:54] [INFO] 解析参数 - 订单号: ORDER17555482233840, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-19 04:17:54] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17555482233840&pid=2222&trade_no=2025081904172716854&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-19 04:17:54] [INFO] 签名验证 - 期望签名: f9f56bb7dfb212c20e54faa96db38fc7, 实际签名: f9f56bb7dfb212c20e54faa96db38fc7
[2025-08-19 04:17:54] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555482233840&payment_status=paid
[2025-08-19 04:17:54] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555482233840","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555482233840","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-19 04:17:03","delivery_content":"q2vRh3zOr7J6Gkp","order_status":"paid","customer_contact":"7245208257","created_at":"2025-08-19 04:17:03","updated_at":"2025-08-19 04:17:54"}}}
[2025-08-19 04:17:54] [SUCCESS] 订单 ORDER17555482233840 支付状态更新成功
[2025-08-19 14:15:08] [INFO] 收到支付通知
[2025-08-19 14:15:08] [INFO] GET参数: {"pid":"2222","trade_no":"2025081914150742923","out_trade_no":"ORDER17555840705762","type":"wxpay","name":"product","money":"0.11","trade_status":"TRADE_SUCCESS","sign":"52a8c2b9a635a7482bab02c1170180bf","sign_type":"MD5"}
[2025-08-19 14:15:08] [INFO] POST参数: []
[2025-08-19 14:15:08] [INFO] 原始输入: 
[2025-08-19 14:15:08] [INFO] 解析参数 - 订单号: ORDER17555840705762, 支付状态: TRADE_SUCCESS, 金额: 0.11
[2025-08-19 14:15:08] [INFO] 签名验证 - 原始字符串: money=0.11&name=product&out_trade_no=ORDER17555840705762&pid=2222&trade_no=2025081914150742923&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-19 14:15:08] [INFO] 签名验证 - 期望签名: 52a8c2b9a635a7482bab02c1170180bf, 实际签名: 52a8c2b9a635a7482bab02c1170180bf
[2025-08-19 14:15:08] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555840705762&payment_status=paid
[2025-08-19 14:15:08] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555840705762","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555840705762","merchant_id":"5201314876","product_name":"1积分","product_price":"0.11","purchase_time":"2025-08-19 14:14:30","delivery_content":"5","order_status":"paid","customer_contact":"testuser","created_at":"2025-08-19 14:14:30","updated_at":"2025-08-19 14:15:08"}}}
[2025-08-19 14:15:08] [SUCCESS] 订单 ORDER17555840705762 支付状态更新成功
[2025-08-19 14:33:57] [INFO] 收到支付通知
[2025-08-19 14:33:57] [INFO] GET参数: {"pid":"2222","trade_no":"2025081914335271447","out_trade_no":"ORDER17555851836359","type":"wxpay","name":"product","money":"0.11","trade_status":"TRADE_SUCCESS","sign":"af73733d3afbe18a39aa6e5f2e200235","sign_type":"MD5"}
[2025-08-19 14:33:57] [INFO] POST参数: []
[2025-08-19 14:33:57] [INFO] 原始输入: 
[2025-08-19 14:33:57] [INFO] 解析参数 - 订单号: ORDER17555851836359, 支付状态: TRADE_SUCCESS, 金额: 0.11
[2025-08-19 14:33:57] [INFO] 签名验证 - 原始字符串: money=0.11&name=product&out_trade_no=ORDER17555851836359&pid=2222&trade_no=2025081914335271447&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-19 14:33:57] [INFO] 签名验证 - 期望签名: af73733d3afbe18a39aa6e5f2e200235, 实际签名: af73733d3afbe18a39aa6e5f2e200235
[2025-08-19 14:33:57] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555851836359&payment_status=paid
[2025-08-19 14:33:57] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555851836359","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555851836359","merchant_id":"5201314876","product_name":"1积分","product_price":"0.11","purchase_time":"2025-08-19 14:33:03","delivery_content":"5","order_status":"paid","customer_contact":"qingqiu666","created_at":"2025-08-19 14:33:03","updated_at":"2025-08-19 14:33:57"}}}
[2025-08-19 14:33:57] [SUCCESS] 订单 ORDER17555851836359 支付状态更新成功
[2025-08-19 15:54:15] [INFO] 收到支付通知
[2025-08-19 15:54:15] [INFO] GET参数: {"pid":"2222","trade_no":"2025081915541083062","out_trade_no":"ORDER17555900238890","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"d108caf7b7a409cbf1a41f006e2c41e6","sign_type":"MD5"}
[2025-08-19 15:54:15] [INFO] POST参数: []
[2025-08-19 15:54:15] [INFO] 原始输入: 
[2025-08-19 15:54:15] [INFO] 解析参数 - 订单号: ORDER17555900238890, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-19 15:54:15] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17555900238890&pid=2222&trade_no=2025081915541083062&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-19 15:54:15] [INFO] 签名验证 - 期望签名: d108caf7b7a409cbf1a41f006e2c41e6, 实际签名: d108caf7b7a409cbf1a41f006e2c41e6
[2025-08-19 15:54:15] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555900238890&payment_status=paid
[2025-08-19 15:54:15] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555900238890","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555900238890","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-19 15:53:43","delivery_content":"tvS5NrLos2zaCpJ","order_status":"paid","customer_contact":"6415613293","created_at":"2025-08-19 15:53:43","updated_at":"2025-08-19 15:54:15"}}}
[2025-08-19 15:54:15] [SUCCESS] 订单 ORDER17555900238890 支付状态更新成功
