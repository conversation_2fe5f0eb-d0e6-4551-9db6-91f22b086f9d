[2025-08-23 01:27:52] [INFO] 收到支付通知
[2025-08-23 01:27:52] [INFO] GET参数: {"pid":"2222","trade_no":"2025082301274323861","out_trade_no":"ORDER17558836225437","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"e311bea3024f686fb1927e5e91480eaa","sign_type":"MD5"}
[2025-08-23 01:27:52] [INFO] POST参数: []
[2025-08-23 01:27:52] [INFO] 原始输入: 
[2025-08-23 01:27:52] [INFO] 解析参数 - 订单号: ORDER17558836225437, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 01:27:52] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17558836225437&pid=2222&trade_no=2025082301274323861&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 01:27:52] [INFO] 签名验证 - 期望签名: e311bea3024f686fb1927e5e91480eaa, 实际签名: e311bea3024f686fb1927e5e91480eaa
[2025-08-23 01:27:52] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17558836225437&payment_status=paid
[2025-08-23 01:27:52] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17558836225437","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17558836225437","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 01:27:02","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 01:27:02","updated_at":"2025-08-23 01:27:52"}}}
[2025-08-23 01:27:52] [SUCCESS] 订单 ORDER17558836225437 支付状态更新成功
[2025-08-23 03:35:31] [INFO] 收到支付通知
[2025-08-23 03:35:31] [INFO] GET参数: {"pid":"2222","trade_no":"2025082303354229293","out_trade_no":"ORDER17558912886667","type":"wxpay","name":"product","money":"24.63","trade_status":"TRADE_SUCCESS","sign":"03c507a252738c7818cbfe357276c1e0","sign_type":"MD5"}
[2025-08-23 03:35:31] [INFO] POST参数: []
[2025-08-23 03:35:31] [INFO] 原始输入: 
[2025-08-23 03:35:31] [INFO] 解析参数 - 订单号: ORDER17558912886667, 支付状态: TRADE_SUCCESS, 金额: 24.63
[2025-08-23 03:35:31] [INFO] 签名验证 - 原始字符串: money=24.63&name=product&out_trade_no=ORDER17558912886667&pid=2222&trade_no=2025082303354229293&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 03:35:31] [INFO] 签名验证 - 期望签名: 03c507a252738c7818cbfe357276c1e0, 实际签名: 03c507a252738c7818cbfe357276c1e0
[2025-08-23 03:35:31] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17558912886667&payment_status=paid
[2025-08-23 03:35:31] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17558912886667","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17558912886667","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.63","purchase_time":"2025-08-23 03:34:48","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 03:34:48","updated_at":"2025-08-23 03:35:31"}}}
[2025-08-23 03:35:31] [SUCCESS] 订单 ORDER17558912886667 支付状态更新成功
[2025-08-23 07:53:09] [INFO] 收到支付通知
[2025-08-23 07:53:09] [INFO] GET参数: {"pid":"2222","trade_no":"2025082307531240851","out_trade_no":"ORDER17559067447294","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"d9fcce5cf91a77b16a54b7a7a4e4f2d9","sign_type":"MD5"}
[2025-08-23 07:53:09] [INFO] POST参数: []
[2025-08-23 07:53:09] [INFO] 原始输入: 
[2025-08-23 07:53:09] [INFO] 解析参数 - 订单号: ORDER17559067447294, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 07:53:09] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559067447294&pid=2222&trade_no=2025082307531240851&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 07:53:09] [INFO] 签名验证 - 期望签名: d9fcce5cf91a77b16a54b7a7a4e4f2d9, 实际签名: d9fcce5cf91a77b16a54b7a7a4e4f2d9
[2025-08-23 07:53:09] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559067447294&payment_status=paid
[2025-08-23 07:53:09] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559067447294","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559067447294","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 07:52:24","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 07:52:24","updated_at":"2025-08-23 07:53:09"}}}
[2025-08-23 07:53:09] [SUCCESS] 订单 ORDER17559067447294 支付状态更新成功
[2025-08-23 10:54:31] [INFO] 收到支付通知
[2025-08-23 10:54:31] [INFO] GET参数: {"pid":"2222","trade_no":"2025082310545384814","out_trade_no":"ORDER17559176435604","type":"alipay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"bb89b978961e59ff36dc28f80e4f31cf","sign_type":"MD5"}
[2025-08-23 10:54:31] [INFO] POST参数: []
[2025-08-23 10:54:31] [INFO] 原始输入: 
[2025-08-23 10:54:31] [INFO] 解析参数 - 订单号: ORDER17559176435604, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 10:54:31] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559176435604&pid=2222&trade_no=2025082310545384814&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-23 10:54:31] [INFO] 签名验证 - 期望签名: bb89b978961e59ff36dc28f80e4f31cf, 实际签名: bb89b978961e59ff36dc28f80e4f31cf
[2025-08-23 10:54:31] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559176435604&payment_status=paid
[2025-08-23 10:54:31] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559176435604","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559176435604","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 10:54:03","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 10:54:03","updated_at":"2025-08-23 10:54:31"}}}
[2025-08-23 10:54:31] [SUCCESS] 订单 ORDER17559176435604 支付状态更新成功
[2025-08-23 12:21:26] [INFO] 收到支付通知
[2025-08-23 12:21:26] [INFO] GET参数: {"pid":"2222","trade_no":"2025082312213045898","out_trade_no":"ORDER17559228516237","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"fb1664d56e5e42ff6372b4fdf5c0e6fa","sign_type":"MD5"}
[2025-08-23 12:21:26] [INFO] POST参数: []
[2025-08-23 12:21:26] [INFO] 原始输入: 
[2025-08-23 12:21:26] [INFO] 解析参数 - 订单号: ORDER17559228516237, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 12:21:26] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559228516237&pid=2222&trade_no=2025082312213045898&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 12:21:26] [INFO] 签名验证 - 期望签名: fb1664d56e5e42ff6372b4fdf5c0e6fa, 实际签名: fb1664d56e5e42ff6372b4fdf5c0e6fa
[2025-08-23 12:21:26] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559228516237&payment_status=paid
[2025-08-23 12:21:26] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559228516237","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559228516237","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 12:20:51","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 12:20:51","updated_at":"2025-08-23 12:21:26"}}}
[2025-08-23 12:21:26] [SUCCESS] 订单 ORDER17559228516237 支付状态更新成功
[2025-08-23 17:59:46] [INFO] 收到支付通知
[2025-08-23 17:59:46] [INFO] GET参数: {"pid":"2222","trade_no":"2025082317593541110","out_trade_no":"ORDER17559431306902","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"9f312c6330822675f4957119972e9e00","sign_type":"MD5"}
[2025-08-23 17:59:46] [INFO] POST参数: []
[2025-08-23 17:59:46] [INFO] 原始输入: 
[2025-08-23 17:59:46] [INFO] 解析参数 - 订单号: ORDER17559431306902, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 17:59:46] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559431306902&pid=2222&trade_no=2025082317593541110&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 17:59:46] [INFO] 签名验证 - 期望签名: 9f312c6330822675f4957119972e9e00, 实际签名: 9f312c6330822675f4957119972e9e00
[2025-08-23 17:59:46] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559431306902&payment_status=paid
[2025-08-23 17:59:46] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559431306902","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559431306902","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 17:58:50","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 17:58:50","updated_at":"2025-08-23 17:59:46"}}}
[2025-08-23 17:59:46] [SUCCESS] 订单 ORDER17559431306902 支付状态更新成功
[2025-08-23 18:37:41] [INFO] 收到支付通知
[2025-08-23 18:37:41] [INFO] GET参数: {"pid":"2222","trade_no":"2025082318374949523","out_trade_no":"ORDER17559454194118","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"c205b996daa8ef9bc1573c0867cfb990","sign_type":"MD5"}
[2025-08-23 18:37:41] [INFO] POST参数: []
[2025-08-23 18:37:41] [INFO] 原始输入: 
[2025-08-23 18:37:41] [INFO] 解析参数 - 订单号: ORDER17559454194118, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 18:37:41] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559454194118&pid=2222&trade_no=2025082318374949523&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 18:37:41] [INFO] 签名验证 - 期望签名: c205b996daa8ef9bc1573c0867cfb990, 实际签名: c205b996daa8ef9bc1573c0867cfb990
[2025-08-23 18:37:41] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559454194118&payment_status=paid
[2025-08-23 18:37:41] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559454194118","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559454194118","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 18:36:59","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 18:36:59","updated_at":"2025-08-23 18:37:41"}}}
[2025-08-23 18:37:41] [SUCCESS] 订单 ORDER17559454194118 支付状态更新成功
[2025-08-23 18:43:03] [INFO] 收到支付通知
[2025-08-23 18:43:03] [INFO] GET参数: {"pid":"2222","trade_no":"2025082318432374604","out_trade_no":"ORDER17559457581718","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"f2b9b92eea6865dabb3e62afd546b5aa","sign_type":"MD5"}
[2025-08-23 18:43:03] [INFO] POST参数: []
[2025-08-23 18:43:03] [INFO] 原始输入: 
[2025-08-23 18:43:03] [INFO] 解析参数 - 订单号: ORDER17559457581718, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 18:43:03] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559457581718&pid=2222&trade_no=2025082318432374604&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 18:43:03] [INFO] 签名验证 - 期望签名: f2b9b92eea6865dabb3e62afd546b5aa, 实际签名: f2b9b92eea6865dabb3e62afd546b5aa
[2025-08-23 18:43:03] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559457581718&payment_status=paid
[2025-08-23 18:43:03] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559457581718","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559457581718","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 18:42:38","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 18:42:38","updated_at":"2025-08-23 18:43:03"}}}
[2025-08-23 18:43:03] [SUCCESS] 订单 ORDER17559457581718 支付状态更新成功
[2025-08-23 18:43:31] [INFO] 收到支付通知
[2025-08-23 18:43:31] [INFO] GET参数: {"pid":"2222","trade_no":"2025082318433873746","out_trade_no":"ORDER17559457719475","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"67dcbad0584a4385a97d018c22b46437","sign_type":"MD5"}
[2025-08-23 18:43:31] [INFO] POST参数: []
[2025-08-23 18:43:31] [INFO] 原始输入: 
[2025-08-23 18:43:31] [INFO] 解析参数 - 订单号: ORDER17559457719475, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 18:43:31] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559457719475&pid=2222&trade_no=2025082318433873746&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 18:43:31] [INFO] 签名验证 - 期望签名: 67dcbad0584a4385a97d018c22b46437, 实际签名: 67dcbad0584a4385a97d018c22b46437
[2025-08-23 18:43:31] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559457719475&payment_status=paid
[2025-08-23 18:43:31] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559457719475","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559457719475","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 18:42:51","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 18:42:51","updated_at":"2025-08-23 18:43:31"}}}
[2025-08-23 18:43:31] [SUCCESS] 订单 ORDER17559457719475 支付状态更新成功
[2025-08-23 18:49:04] [INFO] 收到支付通知
[2025-08-23 18:49:04] [INFO] GET参数: {"pid":"2222","trade_no":"2025082318490799790","out_trade_no":"ORDER17559460883360","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"871daeaba9afef73a2ad12849bb4c9e1","sign_type":"MD5"}
[2025-08-23 18:49:04] [INFO] POST参数: []
[2025-08-23 18:49:04] [INFO] 原始输入: 
[2025-08-23 18:49:04] [INFO] 解析参数 - 订单号: ORDER17559460883360, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 18:49:04] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559460883360&pid=2222&trade_no=2025082318490799790&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 18:49:04] [INFO] 签名验证 - 期望签名: 871daeaba9afef73a2ad12849bb4c9e1, 实际签名: 871daeaba9afef73a2ad12849bb4c9e1
[2025-08-23 18:49:04] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559460883360&payment_status=paid
[2025-08-23 18:49:04] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559460883360","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559460883360","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 18:48:08","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 18:48:08","updated_at":"2025-08-23 18:49:04"}}}
[2025-08-23 18:49:04] [SUCCESS] 订单 ORDER17559460883360 支付状态更新成功
[2025-08-23 20:26:17] [INFO] 收到支付通知
[2025-08-23 20:26:17] [INFO] GET参数: {"pid":"2222","trade_no":"2025082320263524306","out_trade_no":"ORDER17559519559011","type":"wxpay","name":"product","money":"24.63","trade_status":"TRADE_SUCCESS","sign":"b08b242bb5715dbf0e87d1b12d79a463","sign_type":"MD5"}
[2025-08-23 20:26:17] [INFO] POST参数: []
[2025-08-23 20:26:17] [INFO] 原始输入: 
[2025-08-23 20:26:17] [INFO] 解析参数 - 订单号: ORDER17559519559011, 支付状态: TRADE_SUCCESS, 金额: 24.63
[2025-08-23 20:26:17] [INFO] 签名验证 - 原始字符串: money=24.63&name=product&out_trade_no=ORDER17559519559011&pid=2222&trade_no=2025082320263524306&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 20:26:17] [INFO] 签名验证 - 期望签名: b08b242bb5715dbf0e87d1b12d79a463, 实际签名: b08b242bb5715dbf0e87d1b12d79a463
[2025-08-23 20:26:17] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559519559011&payment_status=paid
[2025-08-23 20:26:17] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559519559011","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559519559011","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.63","purchase_time":"2025-08-23 20:25:55","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 20:25:55","updated_at":"2025-08-23 20:26:17"}}}
[2025-08-23 20:26:17] [SUCCESS] 订单 ORDER17559519559011 支付状态更新成功
[2025-08-23 20:26:56] [INFO] 收到支付通知
[2025-08-23 20:26:56] [INFO] GET参数: {"pid":"2222","trade_no":"2025082320264581714","out_trade_no":"ORDER17559519494923","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"05dcb1aa0ced236dbe980f56f8e30a72","sign_type":"MD5"}
[2025-08-23 20:26:56] [INFO] POST参数: []
[2025-08-23 20:26:56] [INFO] 原始输入: 
[2025-08-23 20:26:56] [INFO] 解析参数 - 订单号: ORDER17559519494923, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-23 20:26:56] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17559519494923&pid=2222&trade_no=2025082320264581714&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-23 20:26:56] [INFO] 签名验证 - 期望签名: 05dcb1aa0ced236dbe980f56f8e30a72, 实际签名: 05dcb1aa0ced236dbe980f56f8e30a72
[2025-08-23 20:26:56] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17559519494923&payment_status=paid
[2025-08-23 20:26:56] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17559519494923","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17559519494923","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-23 20:25:49","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-23 20:25:49","updated_at":"2025-08-23 20:26:56"}}}
[2025-08-23 20:26:56] [SUCCESS] 订单 ORDER17559519494923 支付状态更新成功
