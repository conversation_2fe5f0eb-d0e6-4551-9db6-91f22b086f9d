[2025-08-25 11:32:03] [INFO] 收到支付通知
[2025-08-25 11:32:03] [INFO] GET参数: {"pid":"2222","trade_no":"2025082511320550416","out_trade_no":"ORDER17560926374415","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"4395deab69d6dd7661119b59e939171d","sign_type":"MD5"}
[2025-08-25 11:32:03] [INFO] POST参数: []
[2025-08-25 11:32:03] [INFO] 原始输入: 
[2025-08-25 11:32:03] [INFO] 解析参数 - 订单号: ORDER17560926374415, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-25 11:32:03] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17560926374415&pid=2222&trade_no=2025082511320550416&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-25 11:32:03] [INFO] 签名验证 - 期望签名: 4395deab69d6dd7661119b59e939171d, 实际签名: 4395deab69d6dd7661119b59e939171d
[2025-08-25 11:32:03] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17560926374415&payment_status=paid
[2025-08-25 11:32:03] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17560926374415","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17560926374415","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-25 11:30:37","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-25 11:30:37","updated_at":"2025-08-25 11:32:03"}}}
[2025-08-25 11:32:03] [SUCCESS] 订单 ORDER17560926374415 支付状态更新成功
[2025-08-25 13:52:26] [INFO] 收到支付通知
[2025-08-25 13:52:26] [INFO] GET参数: {"pid":"2222","trade_no":"2025082513521160405","out_trade_no":"ORDER17561010667338","type":"wxpay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"7df7fcd728ca2d2229c2b03df8c274ef","sign_type":"MD5"}
[2025-08-25 13:52:26] [INFO] POST参数: []
[2025-08-25 13:52:26] [INFO] 原始输入: 
[2025-08-25 13:52:26] [INFO] 解析参数 - 订单号: ORDER17561010667338, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-25 13:52:26] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17561010667338&pid=2222&trade_no=2025082513521160405&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-25 13:52:26] [INFO] 签名验证 - 期望签名: 7df7fcd728ca2d2229c2b03df8c274ef, 实际签名: 7df7fcd728ca2d2229c2b03df8c274ef
[2025-08-25 13:52:26] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17561010667338&payment_status=paid
[2025-08-25 13:52:26] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17561010667338","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17561010667338","merchant_id":"7346442935","product_name":"助手永久","product_price":"40.33","purchase_time":"2025-08-25 13:51:06","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-25 13:51:06","updated_at":"2025-08-25 13:52:26"}}}
[2025-08-25 13:52:26] [SUCCESS] 订单 ORDER17561010667338 支付状态更新成功
[2025-08-25 14:36:47] [INFO] 收到支付通知
[2025-08-25 14:36:47] [INFO] GET参数: {"pid":"2222","trade_no":"2025082514370327979","out_trade_no":"ORDER17561037803287","type":"wxpay","name":"product","money":"29.73","trade_status":"TRADE_SUCCESS","sign":"c7081e224870c359db4d555bfd8a4328","sign_type":"MD5"}
[2025-08-25 14:36:47] [INFO] POST参数: []
[2025-08-25 14:36:47] [INFO] 原始输入: 
[2025-08-25 14:36:47] [INFO] 解析参数 - 订单号: ORDER17561037803287, 支付状态: TRADE_SUCCESS, 金额: 29.73
[2025-08-25 14:36:47] [INFO] 签名验证 - 原始字符串: money=29.73&name=product&out_trade_no=ORDER17561037803287&pid=2222&trade_no=2025082514370327979&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-25 14:36:47] [INFO] 签名验证 - 期望签名: c7081e224870c359db4d555bfd8a4328, 实际签名: c7081e224870c359db4d555bfd8a4328
[2025-08-25 14:36:47] [INFO] 调用订单状态更新接口: http://103.216.175.76:10083/set_order_payment_status.php?order_id=ORDER17561037803287&payment_status=paid
[2025-08-25 14:36:47] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17561037803287","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17561037803287","merchant_id":"7346442935","product_name":"助手月度","product_price":"29.73","purchase_time":"2025-08-25 14:36:20","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-25 14:36:20","updated_at":"2025-08-25 14:36:47"}}}
[2025-08-25 14:36:47] [SUCCESS] 订单 ORDER17561037803287 支付状态更新成功
