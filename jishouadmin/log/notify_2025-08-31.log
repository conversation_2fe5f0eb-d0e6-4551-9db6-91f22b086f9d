[2025-08-31 00:25:19] [INFO] 收到支付通知
[2025-08-31 00:25:19] [INFO] GET参数: {"pid":"2222","trade_no":"2025083100254398002","out_trade_no":"ORDER17565710806402","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"c025c2b58256604e7150af691d54d380","sign_type":"MD5"}
[2025-08-31 00:25:19] [INFO] POST参数: []
[2025-08-31 00:25:19] [INFO] 原始输入: 
[2025-08-31 00:25:19] [INFO] 解析参数 - 订单号: ORDER17565710806402, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-08-31 00:25:19] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17565710806402&pid=2222&trade_no=2025083100254398002&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-31 00:25:19] [INFO] 签名验证 - 期望签名: c025c2b58256604e7150af691d54d380, 实际签名: c025c2b58256604e7150af691d54d380
[2025-08-31 00:25:19] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17565710806402&payment_status=paid
[2025-08-31 00:25:19] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17565710806402","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17565710806402","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-08-31 00:24:40","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-31 00:24:40","updated_at":"2025-08-31 00:25:19"}}}
[2025-08-31 00:25:19] [SUCCESS] 订单 ORDER17565710806402 支付状态更新成功
[2025-08-31 06:34:44] [INFO] 收到支付通知
[2025-08-31 06:34:44] [INFO] GET参数: {"pid":"2222","trade_no":"2025083106351745597","out_trade_no":"ORDER17565932558940","type":"alipay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"84752594e5ea8e88af2d44cc86eb5eea","sign_type":"MD5"}
[2025-08-31 06:34:44] [INFO] POST参数: []
[2025-08-31 06:34:44] [INFO] 原始输入: 
[2025-08-31 06:34:44] [INFO] 解析参数 - 订单号: ORDER17565932558940, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-31 06:34:44] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17565932558940&pid=2222&trade_no=2025083106351745597&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-31 06:34:44] [INFO] 签名验证 - 期望签名: 84752594e5ea8e88af2d44cc86eb5eea, 实际签名: 84752594e5ea8e88af2d44cc86eb5eea
[2025-08-31 06:34:44] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17565932558940&payment_status=paid
[2025-08-31 06:34:44] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17565932558940","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17565932558940","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-08-31 06:34:15","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-31 06:34:15","updated_at":"2025-08-31 06:34:44"}}}
[2025-08-31 06:34:44] [SUCCESS] 订单 ORDER17565932558940 支付状态更新成功
[2025-08-31 15:46:40] [INFO] 收到支付通知
[2025-08-31 15:46:40] [INFO] GET参数: {"pid":"2222","trade_no":"2025083115470041745","out_trade_no":"ORDER17566263549860","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"f6b598e7865ced835bd369c16ef49873","sign_type":"MD5"}
[2025-08-31 15:46:40] [INFO] POST参数: []
[2025-08-31 15:46:40] [INFO] 原始输入: 
[2025-08-31 15:46:40] [INFO] 解析参数 - 订单号: ORDER17566263549860, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-08-31 15:46:40] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17566263549860&pid=2222&trade_no=2025083115470041745&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-31 15:46:40] [INFO] 签名验证 - 期望签名: f6b598e7865ced835bd369c16ef49873, 实际签名: f6b598e7865ced835bd369c16ef49873
[2025-08-31 15:46:40] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17566263549860&payment_status=paid
[2025-08-31 15:46:40] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17566263549860","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17566263549860","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-08-31 15:45:54","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-31 15:45:54","updated_at":"2025-08-31 15:46:40"}}}
[2025-08-31 15:46:40] [SUCCESS] 订单 ORDER17566263549860 支付状态更新成功
[2025-08-31 22:54:45] [INFO] 收到支付通知
[2025-08-31 22:54:45] [INFO] GET参数: {"pid":"2222","trade_no":"2025083122551948377","out_trade_no":"ORDER17566520452893","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"09354b741afe2f4d6bc1ccabd124aae7","sign_type":"MD5"}
[2025-08-31 22:54:45] [INFO] POST参数: []
[2025-08-31 22:54:45] [INFO] 原始输入: 
[2025-08-31 22:54:45] [INFO] 解析参数 - 订单号: ORDER17566520452893, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-08-31 22:54:45] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17566520452893&pid=2222&trade_no=2025083122551948377&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-31 22:54:45] [INFO] 签名验证 - 期望签名: 09354b741afe2f4d6bc1ccabd124aae7, 实际签名: 09354b741afe2f4d6bc1ccabd124aae7
[2025-08-31 22:54:45] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17566520452893&payment_status=paid
[2025-08-31 22:54:45] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17566520452893","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17566520452893","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-08-31 22:54:05","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-31 22:54:05","updated_at":"2025-08-31 22:54:45"}}}
[2025-08-31 22:54:45] [SUCCESS] 订单 ORDER17566520452893 支付状态更新成功
[2025-08-31 23:53:08] [INFO] 收到支付通知
[2025-08-31 23:53:08] [INFO] GET参数: {"pid":"2222","trade_no":"2025083123534342266","out_trade_no":"ORDER17566555472420","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"6c9c95a70ef5d9efa8c81f240f092937","sign_type":"MD5"}
[2025-08-31 23:53:08] [INFO] POST参数: []
[2025-08-31 23:53:08] [INFO] 原始输入: 
[2025-08-31 23:53:08] [INFO] 解析参数 - 订单号: ORDER17566555472420, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-08-31 23:53:08] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17566555472420&pid=2222&trade_no=2025083123534342266&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-31 23:53:08] [INFO] 签名验证 - 期望签名: 6c9c95a70ef5d9efa8c81f240f092937, 实际签名: 6c9c95a70ef5d9efa8c81f240f092937
[2025-08-31 23:53:08] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17566555472420&payment_status=paid
[2025-08-31 23:53:08] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17566555472420","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17566555472420","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-08-31 23:52:27","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-31 23:52:27","updated_at":"2025-08-31 23:53:08"}}}
[2025-08-31 23:53:08] [SUCCESS] 订单 ORDER17566555472420 支付状态更新成功
[2025-08-31 23:58:11] [INFO] 收到支付通知
[2025-08-31 23:58:11] [INFO] GET参数: {"pid":"2222","trade_no":"2025083123584817530","out_trade_no":"ORDER17566557184224","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"f8c5ac573fb37bb4862f4fc481eaff4f","sign_type":"MD5"}
[2025-08-31 23:58:11] [INFO] POST参数: []
[2025-08-31 23:58:11] [INFO] 原始输入: 
[2025-08-31 23:58:11] [INFO] 解析参数 - 订单号: ORDER17566557184224, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-08-31 23:58:11] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17566557184224&pid=2222&trade_no=2025083123584817530&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-31 23:58:11] [INFO] 签名验证 - 期望签名: f8c5ac573fb37bb4862f4fc481eaff4f, 实际签名: f8c5ac573fb37bb4862f4fc481eaff4f
[2025-08-31 23:58:11] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17566557184224&payment_status=paid
[2025-08-31 23:58:11] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17566557184224","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17566557184224","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-08-31 23:55:18","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-31 23:55:18","updated_at":"2025-08-31 23:58:11"}}}
[2025-08-31 23:58:11] [SUCCESS] 订单 ORDER17566557184224 支付状态更新成功
