[2025-08-22 15:29:07] [INFO] 收到支付通知
[2025-08-22 15:29:07] [INFO] GET参数: {"pid":"2222","trade_no":"2025082215282460506","out_trade_no":"ORDER17558476728505","type":"wxpay","name":"product","money":"24.54","trade_status":"TRADE_SUCCESS","sign":"1d58c82cdd7b2f6bac4984805f143593","sign_type":"MD5"}
[2025-08-22 15:29:07] [INFO] POST参数: []
[2025-08-22 15:29:07] [INFO] 原始输入: 
[2025-08-22 15:29:07] [INFO] 解析参数 - 订单号: ORDER17558476728505, 支付状态: TRADE_SUCCESS, 金额: 24.54
[2025-08-22 15:29:07] [INFO] 签名验证 - 原始字符串: money=24.54&name=product&out_trade_no=ORDER17558476728505&pid=2222&trade_no=2025082215282460506&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-22 15:29:07] [INFO] 签名验证 - 期望签名: 1d58c82cdd7b2f6bac4984805f143593, 实际签名: 1d58c82cdd7b2f6bac4984805f143593
[2025-08-22 15:29:07] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17558476728505&payment_status=paid
[2025-08-22 15:29:07] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17558476728505","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17558476728505","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"24.54","purchase_time":"2025-08-22 15:27:52","delivery_content":"TYohu2JRKdBskc6","order_status":"paid","customer_contact":"web_1755847672441_tfrab62ov","created_at":"2025-08-22 15:27:52","updated_at":"2025-08-22 15:29:07"}}}
[2025-08-22 15:29:07] [SUCCESS] 订单 ORDER17558476728505 支付状态更新成功
[2025-08-22 22:26:40] [INFO] 收到支付通知
[2025-08-22 22:26:40] [INFO] GET参数: {"pid":"2222","trade_no":"2025082222265437977","out_trade_no":"ORDER17558727637055","type":"alipay","name":"product","money":"40.33","trade_status":"TRADE_SUCCESS","sign":"ea7f647da1f95c8042bfb842a5f05843","sign_type":"MD5"}
[2025-08-22 22:26:40] [INFO] POST参数: []
[2025-08-22 22:26:40] [INFO] 原始输入: 
[2025-08-22 22:26:40] [INFO] 解析参数 - 订单号: ORDER17558727637055, 支付状态: TRADE_SUCCESS, 金额: 40.33
[2025-08-22 22:26:40] [INFO] 签名验证 - 原始字符串: money=40.33&name=product&out_trade_no=ORDER17558727637055&pid=2222&trade_no=2025082222265437977&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-22 22:26:40] [INFO] 签名验证 - 期望签名: ea7f647da1f95c8042bfb842a5f05843, 实际签名: ea7f647da1f95c8042bfb842a5f05843
[2025-08-22 22:26:40] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17558727637055&payment_status=paid
[2025-08-22 22:26:40] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17558727637055","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17558727637055","merchant_id":"7346442935","product_name":"支付通道2","product_price":"40.33","purchase_time":"2025-08-22 22:26:03","delivery_content":"1234342","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-08-22 22:26:03","updated_at":"2025-08-22 22:26:40"}}}
[2025-08-22 22:26:40] [SUCCESS] 订单 ORDER17558727637055 支付状态更新成功
