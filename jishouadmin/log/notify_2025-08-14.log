[2025-08-14 09:22:54] [INFO] 收到支付通知
[2025-08-14 09:22:54] [INFO] GET参数: {"pid":"2222","trade_no":"2025081409223042210","out_trade_no":"ORDER17551345364478","type":"wxpay","name":"product","money":"1.06","trade_status":"TRADE_SUCCESS","sign":"b8d594628bab5f157cfe3f4601401921","sign_type":"MD5"}
[2025-08-14 09:22:54] [INFO] POST参数: []
[2025-08-14 09:22:54] [INFO] 原始输入: 
[2025-08-14 09:22:54] [INFO] 解析参数 - 订单号: ORDER17551345364478, 支付状态: TRADE_SUCCESS, 金额: 1.06
[2025-08-14 09:22:54] [INFO] 签名验证 - 原始字符串: money=1.06&name=product&out_trade_no=ORDER17551345364478&pid=2222&trade_no=2025081409223042210&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-14 09:22:54] [INFO] 签名验证 - 期望签名: b8d594628bab5f157cfe3f4601401921, 实际签名: b8d594628bab5f157cfe3f4601401921
[2025-08-14 09:22:54] [INFO] 调用订单状态更新接口: http://154.219.106.158:7893/set_order_payment_status.php?order_id=ORDER17551345364478&payment_status=paid
[2025-08-14 09:22:54] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-14 09:22:54] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-14 09:24:04] [INFO] 收到支付通知
[2025-08-14 09:24:04] [INFO] GET参数: {"pid":"2222","trade_no":"2025081409223042210","out_trade_no":"ORDER17551345364478","type":"wxpay","name":"product","money":"1.06","trade_status":"TRADE_SUCCESS","sign":"b8d594628bab5f157cfe3f4601401921","sign_type":"MD5"}
[2025-08-14 09:24:04] [INFO] POST参数: []
[2025-08-14 09:24:04] [INFO] 原始输入: 
[2025-08-14 09:24:04] [INFO] 解析参数 - 订单号: ORDER17551345364478, 支付状态: TRADE_SUCCESS, 金额: 1.06
[2025-08-14 09:24:04] [INFO] 签名验证 - 原始字符串: money=1.06&name=product&out_trade_no=ORDER17551345364478&pid=2222&trade_no=2025081409223042210&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-14 09:24:04] [INFO] 签名验证 - 期望签名: b8d594628bab5f157cfe3f4601401921, 实际签名: b8d594628bab5f157cfe3f4601401921
[2025-08-14 09:24:04] [INFO] 调用订单状态更新接口: http://154.219.106.158:7893/set_order_payment_status.php?order_id=ORDER17551345364478&payment_status=paid
[2025-08-14 09:24:04] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-14 09:24:04] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-14 09:26:14] [INFO] 收到支付通知
[2025-08-14 09:26:14] [INFO] GET参数: {"pid":"2222","trade_no":"2025081409223042210","out_trade_no":"ORDER17551345364478","type":"wxpay","name":"product","money":"1.06","trade_status":"TRADE_SUCCESS","sign":"b8d594628bab5f157cfe3f4601401921","sign_type":"MD5"}
[2025-08-14 09:26:14] [INFO] POST参数: []
[2025-08-14 09:26:14] [INFO] 原始输入: 
[2025-08-14 09:26:14] [INFO] 解析参数 - 订单号: ORDER17551345364478, 支付状态: TRADE_SUCCESS, 金额: 1.06
[2025-08-14 09:26:14] [INFO] 签名验证 - 原始字符串: money=1.06&name=product&out_trade_no=ORDER17551345364478&pid=2222&trade_no=2025081409223042210&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-14 09:26:14] [INFO] 签名验证 - 期望签名: b8d594628bab5f157cfe3f4601401921, 实际签名: b8d594628bab5f157cfe3f4601401921
[2025-08-14 09:26:14] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551345364478&payment_status=paid
[2025-08-14 09:26:14] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551345364478","old_status":"paid","new_status":"paid","order_info":{"order_id":"ORDER17551345364478","merchant_id":"8034567958","product_name":"通道测试","product_price":"1.06","purchase_time":"2025-08-14 09:22:16","delivery_content":"1","order_status":"paid","customer_contact":"8034567958","created_at":"2025-08-14 09:22:16","updated_at":"2025-08-14 09:26:12"}}}
[2025-08-14 09:26:14] [SUCCESS] 订单 ORDER17551345364478 支付状态更新成功
[2025-08-14 09:29:42] [INFO] 收到支付通知
[2025-08-14 09:29:42] [INFO] GET参数: {"pid":"2222","trade_no":"2025081409293985552","out_trade_no":"ORDER17551349654404","type":"wxpay","name":"product","money":"1.06","trade_status":"TRADE_SUCCESS","sign":"ac43ad432193786f86153dfcacd07564","sign_type":"MD5"}
[2025-08-14 09:29:42] [INFO] POST参数: []
[2025-08-14 09:29:42] [INFO] 原始输入: 
[2025-08-14 09:29:42] [INFO] 解析参数 - 订单号: ORDER17551349654404, 支付状态: TRADE_SUCCESS, 金额: 1.06
[2025-08-14 09:29:42] [INFO] 签名验证 - 原始字符串: money=1.06&name=product&out_trade_no=ORDER17551349654404&pid=2222&trade_no=2025081409293985552&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-14 09:29:42] [INFO] 签名验证 - 期望签名: ac43ad432193786f86153dfcacd07564, 实际签名: ac43ad432193786f86153dfcacd07564
[2025-08-14 09:29:42] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551349654404&payment_status=paid
[2025-08-14 09:29:42] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551349654404","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551349654404","merchant_id":"8034567958","product_name":"通道测试","product_price":"1.06","purchase_time":"2025-08-14 09:29:25","delivery_content":"1","order_status":"paid","customer_contact":"web_1755134959676_f6fern21r","created_at":"2025-08-14 09:29:25","updated_at":"2025-08-14 09:29:42"}}}
[2025-08-14 09:29:42] [SUCCESS] 订单 ORDER17551349654404 支付状态更新成功
[2025-08-14 11:46:36] [INFO] 收到支付通知
[2025-08-14 11:46:36] [INFO] GET参数: {"pid":"2222","trade_no":"2025081411454344245","out_trade_no":"ORDER17551431297319","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"f90e43d1ed7ff768a8fad2ef735ca263","sign_type":"MD5"}
[2025-08-14 11:46:36] [INFO] POST参数: []
[2025-08-14 11:46:36] [INFO] 原始输入: 
[2025-08-14 11:46:36] [INFO] 解析参数 - 订单号: ORDER17551431297319, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-14 11:46:36] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17551431297319&pid=2222&trade_no=2025081411454344245&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-14 11:46:36] [INFO] 签名验证 - 期望签名: f90e43d1ed7ff768a8fad2ef735ca263, 实际签名: f90e43d1ed7ff768a8fad2ef735ca263
[2025-08-14 11:46:36] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551431297319&payment_status=paid
[2025-08-14 11:46:36] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551431297319","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551431297319","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-14 11:45:29","delivery_content":"bch9e2SA7LnK0Bl","order_status":"paid","customer_contact":"web_1755143129750_3l9ltri47","created_at":"2025-08-14 11:45:29","updated_at":"2025-08-14 11:46:36"}}}
[2025-08-14 11:46:36] [SUCCESS] 订单 ORDER17551431297319 支付状态更新成功
[2025-08-14 11:53:06] [INFO] 收到支付通知
[2025-08-14 11:53:06] [INFO] GET参数: {"pid":"2222","trade_no":"2025081411503480102","out_trade_no":"ORDER17551434219069","type":"alipay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"432eb3d0442312f2fd231b27fa3c0ba6","sign_type":"MD5"}
[2025-08-14 11:53:06] [INFO] POST参数: []
[2025-08-14 11:53:06] [INFO] 原始输入: 
[2025-08-14 11:53:06] [INFO] 解析参数 - 订单号: ORDER17551434219069, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-14 11:53:06] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17551434219069&pid=2222&trade_no=2025081411503480102&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-14 11:53:06] [INFO] 签名验证 - 期望签名: 432eb3d0442312f2fd231b27fa3c0ba6, 实际签名: 432eb3d0442312f2fd231b27fa3c0ba6
[2025-08-14 11:53:06] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551434219069&payment_status=paid
[2025-08-14 11:53:06] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551434219069","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551434219069","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.93","purchase_time":"2025-08-14 11:50:21","delivery_content":"j48Dn23B","order_status":"paid","customer_contact":"web_1755143421534_vt5y10p7e","created_at":"2025-08-14 11:50:21","updated_at":"2025-08-14 11:53:06"}}}
[2025-08-14 11:53:06] [SUCCESS] 订单 ORDER17551434219069 支付状态更新成功
[2025-08-14 12:03:33] [INFO] 收到支付通知
[2025-08-14 12:03:33] [INFO] GET参数: {"pid":"2222","trade_no":"2025081411571047499","out_trade_no":"ORDER17551438136442","type":"alipay","name":"product","money":"305.28","trade_status":"TRADE_SUCCESS","sign":"228acf1443395dc0fdb8afe20ecc8297","sign_type":"MD5"}
[2025-08-14 12:03:33] [INFO] POST参数: []
[2025-08-14 12:03:33] [INFO] 原始输入: 
[2025-08-14 12:03:33] [INFO] 解析参数 - 订单号: ORDER17551438136442, 支付状态: TRADE_SUCCESS, 金额: 305.28
[2025-08-14 12:03:33] [INFO] 签名验证 - 原始字符串: money=305.28&name=product&out_trade_no=ORDER17551438136442&pid=2222&trade_no=2025081411571047499&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-14 12:03:33] [INFO] 签名验证 - 期望签名: 228acf1443395dc0fdb8afe20ecc8297, 实际签名: 228acf1443395dc0fdb8afe20ecc8297
[2025-08-14 12:03:33] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551438136442&payment_status=paid
[2025-08-14 12:03:33] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551438136442","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551438136442","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"305.28","purchase_time":"2025-08-14 11:56:53","delivery_content":"","order_status":"paid","customer_contact":"web_1755143813184_qp8zn03zj","created_at":"2025-08-14 11:56:53","updated_at":"2025-08-14 12:03:33"}}}
[2025-08-14 12:03:33] [SUCCESS] 订单 ORDER17551438136442 支付状态更新成功
[2025-08-14 12:05:09] [INFO] 收到支付通知
[2025-08-14 12:05:09] [INFO] GET参数: {"pid":"2222","trade_no":"2025081412050898284","out_trade_no":"ORDER17551442941375","type":"alipay","name":"product","money":"56.84","trade_status":"TRADE_SUCCESS","sign":"511294867c5c8261af870e4b367510d7","sign_type":"MD5"}
[2025-08-14 12:05:09] [INFO] POST参数: []
[2025-08-14 12:05:09] [INFO] 原始输入: 
[2025-08-14 12:05:09] [INFO] 解析参数 - 订单号: ORDER17551442941375, 支付状态: TRADE_SUCCESS, 金额: 56.84
[2025-08-14 12:05:09] [INFO] 签名验证 - 原始字符串: money=56.84&name=product&out_trade_no=ORDER17551442941375&pid=2222&trade_no=2025081412050898284&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-14 12:05:09] [INFO] 签名验证 - 期望签名: 511294867c5c8261af870e4b367510d7, 实际签名: 511294867c5c8261af870e4b367510d7
[2025-08-14 12:05:09] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551442941375&payment_status=paid
[2025-08-14 12:05:09] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551442941375","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551442941375","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"56.84","purchase_time":"2025-08-14 12:04:54","delivery_content":"CvUte3P6sErFyI9","order_status":"paid","customer_contact":"web_1755144294593_w5g14rq58","created_at":"2025-08-14 12:04:54","updated_at":"2025-08-14 12:05:09"}}}
[2025-08-14 12:05:09] [SUCCESS] 订单 ORDER17551442941375 支付状态更新成功
[2025-08-14 14:34:46] [INFO] 收到支付通知
[2025-08-14 14:34:46] [INFO] GET参数: {"pid":"2222","trade_no":"2025081414342075982","out_trade_no":"ORDER17551532432838","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"713920a8640a6d34f0fbbbfb3a4a3eb2","sign_type":"MD5"}
[2025-08-14 14:34:46] [INFO] POST参数: []
[2025-08-14 14:34:46] [INFO] 原始输入: 
[2025-08-14 14:34:46] [INFO] 解析参数 - 订单号: ORDER17551532432838, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-14 14:34:46] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17551532432838&pid=2222&trade_no=2025081414342075982&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-14 14:34:46] [INFO] 签名验证 - 期望签名: 713920a8640a6d34f0fbbbfb3a4a3eb2, 实际签名: 713920a8640a6d34f0fbbbfb3a4a3eb2
[2025-08-14 14:34:46] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551532432838&payment_status=paid
[2025-08-14 14:34:46] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551532432838","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551532432838","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-14 14:34:03","delivery_content":"dpX0zGeU6H5LalP","order_status":"paid","customer_contact":"8318790805","created_at":"2025-08-14 14:34:03","updated_at":"2025-08-14 14:34:46"}}}
[2025-08-14 14:34:46] [SUCCESS] 订单 ORDER17551532432838 支付状态更新成功
[2025-08-14 17:08:04] [INFO] 收到支付通知
[2025-08-14 17:08:04] [INFO] GET参数: {"pid":"2222","trade_no":"2025081417072144204","out_trade_no":"ORDER17551622014859","type":"alipay","name":"product","money":"1.06","trade_status":"TRADE_SUCCESS","sign":"21d10abe06629dee4fa6430fedc88f2b","sign_type":"MD5"}
[2025-08-14 17:08:04] [INFO] POST参数: []
[2025-08-14 17:08:04] [INFO] 原始输入: 
[2025-08-14 17:08:04] [INFO] 解析参数 - 订单号: ORDER17551622014859, 支付状态: TRADE_SUCCESS, 金额: 1.06
[2025-08-14 17:08:04] [INFO] 签名验证 - 原始字符串: money=1.06&name=product&out_trade_no=ORDER17551622014859&pid=2222&trade_no=2025081417072144204&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-14 17:08:04] [INFO] 签名验证 - 期望签名: 21d10abe06629dee4fa6430fedc88f2b, 实际签名: 21d10abe06629dee4fa6430fedc88f2b
[2025-08-14 17:08:04] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551622014859&payment_status=paid
[2025-08-14 17:08:04] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551622014859","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551622014859","merchant_id":"8034567958","product_name":"通道测试","product_price":"1.06","purchase_time":"2025-08-14 17:03:21","delivery_content":"1","order_status":"paid","customer_contact":"7832003851","created_at":"2025-08-14 17:03:21","updated_at":"2025-08-14 17:08:04"}}}
[2025-08-14 17:08:04] [SUCCESS] 订单 ORDER17551622014859 支付状态更新成功
[2025-08-14 19:25:42] [INFO] 收到支付通知
[2025-08-14 19:25:42] [INFO] GET参数: {"pid":"2222","trade_no":"2025081419252377112","out_trade_no":"ORDER17551707093592","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"067c7d658ad1f7ea5ea395701493a741","sign_type":"MD5"}
[2025-08-14 19:25:42] [INFO] POST参数: []
[2025-08-14 19:25:42] [INFO] 原始输入: 
[2025-08-14 19:25:42] [INFO] 解析参数 - 订单号: ORDER17551707093592, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-14 19:25:42] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17551707093592&pid=2222&trade_no=2025081419252377112&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-14 19:25:42] [INFO] 签名验证 - 期望签名: 067c7d658ad1f7ea5ea395701493a741, 实际签名: 067c7d658ad1f7ea5ea395701493a741
[2025-08-14 19:25:42] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551707093592&payment_status=paid
[2025-08-14 19:25:42] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551707093592","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551707093592","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-14 19:25:09","delivery_content":"QLrM1SGZJPjuW79","order_status":"paid","customer_contact":"web_1755170708613_3s772l2ca","created_at":"2025-08-14 19:25:09","updated_at":"2025-08-14 19:25:42"}}}
[2025-08-14 19:25:42] [SUCCESS] 订单 ORDER17551707093592 支付状态更新成功
[2025-08-14 20:45:45] [INFO] 收到支付通知
[2025-08-14 20:45:45] [INFO] GET参数: {"pid":"2222","trade_no":"2025081420451839065","out_trade_no":"ORDER17551755042551","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"357e51bbacb8c87ca7411f6bd5e06841","sign_type":"MD5"}
[2025-08-14 20:45:45] [INFO] POST参数: []
[2025-08-14 20:45:45] [INFO] 原始输入: 
[2025-08-14 20:45:45] [INFO] 解析参数 - 订单号: ORDER17551755042551, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-14 20:45:45] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17551755042551&pid=2222&trade_no=2025081420451839065&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-14 20:45:45] [INFO] 签名验证 - 期望签名: 357e51bbacb8c87ca7411f6bd5e06841, 实际签名: 357e51bbacb8c87ca7411f6bd5e06841
[2025-08-14 20:45:45] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17551755042551&payment_status=paid
[2025-08-14 20:45:45] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17551755042551","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17551755042551","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-14 20:45:04","delivery_content":"40f23tMv6ZTRhyk","order_status":"paid","customer_contact":"web_1755175502933_7tyv7y2w6","created_at":"2025-08-14 20:45:04","updated_at":"2025-08-14 20:45:45"}}}
[2025-08-14 20:45:45] [SUCCESS] 订单 ORDER17551755042551 支付状态更新成功
