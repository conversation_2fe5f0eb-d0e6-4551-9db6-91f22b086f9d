[2025-08-18 01:12:25] [INFO] 收到支付通知
[2025-08-18 01:12:25] [INFO] GET参数: {"pid":"2222","trade_no":"2025081801110858702","out_trade_no":"ORDER17554506456054","type":"wxpay","name":"product","money":"24.54","trade_status":"TRADE_SUCCESS","sign":"459636019de7891f05dc1bb6b9d58013","sign_type":"MD5"}
[2025-08-18 01:12:25] [INFO] POST参数: []
[2025-08-18 01:12:25] [INFO] 原始输入: 
[2025-08-18 01:12:25] [INFO] 解析参数 - 订单号: ORDER17554506456054, 支付状态: TRADE_SUCCESS, 金额: 24.54
[2025-08-18 01:12:25] [INFO] 签名验证 - 原始字符串: money=24.54&name=product&out_trade_no=ORDER17554506456054&pid=2222&trade_no=2025081801110858702&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-18 01:12:25] [INFO] 签名验证 - 期望签名: 459636019de7891f05dc1bb6b9d58013, 实际签名: 459636019de7891f05dc1bb6b9d58013
[2025-08-18 01:12:25] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554506456054&payment_status=paid
[2025-08-18 01:12:25] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554506456054","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554506456054","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"24.54","purchase_time":"2025-08-18 01:10:45","delivery_content":"BxmIcsY1wANVoTC","order_status":"paid","customer_contact":"7636604605","created_at":"2025-08-18 01:10:45","updated_at":"2025-08-18 01:12:25"}}}
[2025-08-18 01:12:25] [SUCCESS] 订单 ORDER17554506456054 支付状态更新成功
[2025-08-18 01:43:19] [INFO] 收到支付通知
[2025-08-18 01:43:19] [INFO] GET参数: {"pid":"2222","trade_no":"2025081801431960221","out_trade_no":"ORDER17554525728888","type":"wxpay","name":"product","money":"24.54","trade_status":"TRADE_SUCCESS","sign":"ab2e56bca070d9b77c6053b136acb438","sign_type":"MD5"}
[2025-08-18 01:43:19] [INFO] POST参数: []
[2025-08-18 01:43:19] [INFO] 原始输入: 
[2025-08-18 01:43:19] [INFO] 解析参数 - 订单号: ORDER17554525728888, 支付状态: TRADE_SUCCESS, 金额: 24.54
[2025-08-18 01:43:19] [INFO] 签名验证 - 原始字符串: money=24.54&name=product&out_trade_no=ORDER17554525728888&pid=2222&trade_no=2025081801431960221&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-18 01:43:19] [INFO] 签名验证 - 期望签名: ab2e56bca070d9b77c6053b136acb438, 实际签名: ab2e56bca070d9b77c6053b136acb438
[2025-08-18 01:43:19] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554525728888&payment_status=paid
[2025-08-18 01:43:19] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554525728888","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554525728888","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"24.54","purchase_time":"2025-08-18 01:42:52","delivery_content":"f5wMoSYe8FEpGvu","order_status":"paid","customer_contact":"8476018372","created_at":"2025-08-18 01:42:52","updated_at":"2025-08-18 01:43:19"}}}
[2025-08-18 01:43:19] [SUCCESS] 订单 ORDER17554525728888 支付状态更新成功
[2025-08-18 01:45:35] [INFO] 收到支付通知
[2025-08-18 01:45:35] [INFO] GET参数: {"pid":"2222","trade_no":"2025081801453843698","out_trade_no":"ORDER17554527172642","type":"wxpay","name":"product","money":"24.54","trade_status":"TRADE_SUCCESS","sign":"e220e92d6a04a386a15409e6bc22a8e4","sign_type":"MD5"}
[2025-08-18 01:45:35] [INFO] POST参数: []
[2025-08-18 01:45:35] [INFO] 原始输入: 
[2025-08-18 01:45:35] [INFO] 解析参数 - 订单号: ORDER17554527172642, 支付状态: TRADE_SUCCESS, 金额: 24.54
[2025-08-18 01:45:35] [INFO] 签名验证 - 原始字符串: money=24.54&name=product&out_trade_no=ORDER17554527172642&pid=2222&trade_no=2025081801453843698&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-18 01:45:35] [INFO] 签名验证 - 期望签名: e220e92d6a04a386a15409e6bc22a8e4, 实际签名: e220e92d6a04a386a15409e6bc22a8e4
[2025-08-18 01:45:35] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554527172642&payment_status=paid
[2025-08-18 01:45:35] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554527172642","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554527172642","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"24.54","purchase_time":"2025-08-18 01:45:17","delivery_content":"bSjJ8MYxq1Owulp","order_status":"paid","customer_contact":"8476018372","created_at":"2025-08-18 01:45:17","updated_at":"2025-08-18 01:45:35"}}}
[2025-08-18 01:45:35] [SUCCESS] 订单 ORDER17554527172642 支付状态更新成功
[2025-08-18 10:07:44] [INFO] 收到支付通知
[2025-08-18 10:07:44] [INFO] GET参数: {"pid":"2222","trade_no":"2025081810073489506","out_trade_no":"ORDER17554828322282","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"63fa40a48228cf71192ebd284b74a80d","sign_type":"MD5"}
[2025-08-18 10:07:44] [INFO] POST参数: []
[2025-08-18 10:07:44] [INFO] 原始输入: 
[2025-08-18 10:07:44] [INFO] 解析参数 - 订单号: ORDER17554828322282, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-18 10:07:44] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17554828322282&pid=2222&trade_no=2025081810073489506&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-18 10:07:44] [INFO] 签名验证 - 期望签名: 63fa40a48228cf71192ebd284b74a80d, 实际签名: 63fa40a48228cf71192ebd284b74a80d
[2025-08-18 10:07:44] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554828322282&payment_status=paid
[2025-08-18 10:07:44] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554828322282","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554828322282","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.93","purchase_time":"2025-08-18 10:07:12","delivery_content":"TZx5YENg","order_status":"paid","customer_contact":"web_1755482831509_3pphhydus","created_at":"2025-08-18 10:07:12","updated_at":"2025-08-18 10:07:44"}}}
[2025-08-18 10:07:44] [SUCCESS] 订单 ORDER17554828322282 支付状态更新成功
[2025-08-18 20:11:32] [INFO] 收到支付通知
[2025-08-18 20:11:32] [INFO] GET参数: {"pid":"2222","trade_no":"2025081820105979790","out_trade_no":"ORDER17555190362607","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"bd3238b309279b1025cc04713ab23609","sign_type":"MD5"}
[2025-08-18 20:11:32] [INFO] POST参数: []
[2025-08-18 20:11:32] [INFO] 原始输入: 
[2025-08-18 20:11:32] [INFO] 解析参数 - 订单号: ORDER17555190362607, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-08-18 20:11:32] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17555190362607&pid=2222&trade_no=2025081820105979790&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-18 20:11:32] [INFO] 签名验证 - 期望签名: bd3238b309279b1025cc04713ab23609, 实际签名: bd3238b309279b1025cc04713ab23609
[2025-08-18 20:11:32] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555190362607&payment_status=paid
[2025-08-18 20:11:32] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555190362607","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555190362607","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.93","purchase_time":"2025-08-18 20:10:36","delivery_content":"JchyOW53","order_status":"paid","customer_contact":"web_1755519036101_cbzd67kpq","created_at":"2025-08-18 20:10:36","updated_at":"2025-08-18 20:11:32"}}}
[2025-08-18 20:11:32] [SUCCESS] 订单 ORDER17555190362607 支付状态更新成功
[2025-08-18 21:33:01] [INFO] 收到支付通知
[2025-08-18 21:33:01] [INFO] GET参数: {"pid":"2222","trade_no":"2025081821330717773","out_trade_no":"ORDER17555239647048","type":"wxpay","name":"product","money":"0.85","trade_status":"TRADE_SUCCESS","sign":"c27ba02034250ab237d3486c21993898","sign_type":"MD5"}
[2025-08-18 21:33:01] [INFO] POST参数: []
[2025-08-18 21:33:01] [INFO] 原始输入: 
[2025-08-18 21:33:01] [INFO] 解析参数 - 订单号: ORDER17555239647048, 支付状态: TRADE_SUCCESS, 金额: 0.85
[2025-08-18 21:33:01] [INFO] 签名验证 - 原始字符串: money=0.85&name=product&out_trade_no=ORDER17555239647048&pid=2222&trade_no=2025081821330717773&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-18 21:33:01] [INFO] 签名验证 - 期望签名: c27ba02034250ab237d3486c21993898, 实际签名: c27ba02034250ab237d3486c21993898
[2025-08-18 21:33:01] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555239647048&payment_status=paid
[2025-08-18 21:33:01] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555239647048","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555239647048","merchant_id":"5201314876","product_name":"1积分","product_price":"0.85","purchase_time":"2025-08-18 21:32:44","delivery_content":"3","order_status":"paid","customer_contact":"testuser","created_at":"2025-08-18 21:32:44","updated_at":"2025-08-18 21:33:01"}}}
[2025-08-18 21:33:01] [SUCCESS] 订单 ORDER17555239647048 支付状态更新成功
[2025-08-18 21:35:52] [INFO] 收到支付通知
[2025-08-18 21:35:52] [INFO] GET参数: {"pid":"2222","trade_no":"2025081821360155631","out_trade_no":"ORDER17555241376969","type":"wxpay","name":"product","money":"0.85","trade_status":"TRADE_SUCCESS","sign":"8bf746a98ec17bfb8e859c2d66361725","sign_type":"MD5"}
[2025-08-18 21:35:52] [INFO] POST参数: []
[2025-08-18 21:35:52] [INFO] 原始输入: 
[2025-08-18 21:35:52] [INFO] 解析参数 - 订单号: ORDER17555241376969, 支付状态: TRADE_SUCCESS, 金额: 0.85
[2025-08-18 21:35:52] [INFO] 签名验证 - 原始字符串: money=0.85&name=product&out_trade_no=ORDER17555241376969&pid=2222&trade_no=2025081821360155631&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-18 21:35:52] [INFO] 签名验证 - 期望签名: 8bf746a98ec17bfb8e859c2d66361725, 实际签名: 8bf746a98ec17bfb8e859c2d66361725
[2025-08-18 21:35:52] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555241376969&payment_status=paid
[2025-08-18 21:35:52] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555241376969","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555241376969","merchant_id":"5201314876","product_name":"1积分","product_price":"0.85","purchase_time":"2025-08-18 21:35:37","delivery_content":"6","order_status":"paid","customer_contact":"testuser","created_at":"2025-08-18 21:35:37","updated_at":"2025-08-18 21:35:52"}}}
[2025-08-18 21:35:52] [SUCCESS] 订单 ORDER17555241376969 支付状态更新成功
[2025-08-18 21:40:48] [INFO] 收到支付通知
[2025-08-18 21:40:48] [INFO] GET参数: {"pid":"2222","trade_no":"2025081821405684502","out_trade_no":"ORDER17555244322985","type":"alipay","name":"product","money":"0.11","trade_status":"TRADE_SUCCESS","sign":"1f84cddde70e1163dac497b82e237b95","sign_type":"MD5"}
[2025-08-18 21:40:48] [INFO] POST参数: []
[2025-08-18 21:40:48] [INFO] 原始输入: 
[2025-08-18 21:40:48] [INFO] 解析参数 - 订单号: ORDER17555244322985, 支付状态: TRADE_SUCCESS, 金额: 0.11
[2025-08-18 21:40:48] [INFO] 签名验证 - 原始字符串: money=0.11&name=product&out_trade_no=ORDER17555244322985&pid=2222&trade_no=2025081821405684502&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-18 21:40:48] [INFO] 签名验证 - 期望签名: 1f84cddde70e1163dac497b82e237b95, 实际签名: 1f84cddde70e1163dac497b82e237b95
[2025-08-18 21:40:48] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555244322985&payment_status=paid
[2025-08-18 21:40:48] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555244322985","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555244322985","merchant_id":"5201314876","product_name":"1积分","product_price":"0.11","purchase_time":"2025-08-18 21:40:32","delivery_content":"85","order_status":"paid","customer_contact":"mikaceshi","created_at":"2025-08-18 21:40:32","updated_at":"2025-08-18 21:40:48"}}}
[2025-08-18 21:40:48] [SUCCESS] 订单 ORDER17555244322985 支付状态更新成功
[2025-08-18 21:59:15] [INFO] 收到支付通知
[2025-08-18 21:59:15] [INFO] GET参数: {"pid":"2222","trade_no":"2025081821592147669","out_trade_no":"ORDER17555255373060","type":"alipay","name":"product","money":"0.11","trade_status":"TRADE_SUCCESS","sign":"0cfca5ad3c819d69c432c129af6fd5bd","sign_type":"MD5"}
[2025-08-18 21:59:15] [INFO] POST参数: []
[2025-08-18 21:59:15] [INFO] 原始输入: 
[2025-08-18 21:59:15] [INFO] 解析参数 - 订单号: ORDER17555255373060, 支付状态: TRADE_SUCCESS, 金额: 0.11
[2025-08-18 21:59:15] [INFO] 签名验证 - 原始字符串: money=0.11&name=product&out_trade_no=ORDER17555255373060&pid=2222&trade_no=2025081821592147669&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-18 21:59:15] [INFO] 签名验证 - 期望签名: 0cfca5ad3c819d69c432c129af6fd5bd, 实际签名: 0cfca5ad3c819d69c432c129af6fd5bd
[2025-08-18 21:59:15] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17555255373060&payment_status=paid
[2025-08-18 21:59:15] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17555255373060","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17555255373060","merchant_id":"5201314876","product_name":"1积分","product_price":"0.11","purchase_time":"2025-08-18 21:58:57","delivery_content":"5","order_status":"paid","customer_contact":"mikaceshi","created_at":"2025-08-18 21:58:57","updated_at":"2025-08-18 21:59:15"}}}
[2025-08-18 21:59:15] [SUCCESS] 订单 ORDER17555255373060 支付状态更新成功
