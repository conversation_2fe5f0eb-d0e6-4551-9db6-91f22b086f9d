[2025-08-28 12:56:36] 查询订单号: ORDER17563569333563
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17563569333563
返回: {"code":1,"msg":"succ","trade_no":"2025082812565939789","out_trade_no":"ORDER17563569333563","api_trade_no":null,"bill_trade_no":null,"type":"wxpay","pid":"2222","addtime":"2025-08-28 12:56:59","endtime":null,"name":"\u7535\u5546\u5355\u53f7:ORDER17563569333563","money":"40.33","param":null,"buyer":null,"status":"0","payurl":null}
[2025-08-28 18:59:01] 查询订单号: ORDER17563787362714
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17563787362714
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-28 20:18:24] 查询订单号: ORDER17563835013206
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17563835013206
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-28 20:18:33] 查询订单号: ORDER17563835013206
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17563835013206
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-28 23:09:48] 查询订单号: ORDER17563937776648
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17563937776648
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
