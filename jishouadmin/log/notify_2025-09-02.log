[2025-09-02 11:31:57] [INFO] 收到支付通知
[2025-09-02 11:31:57] [INFO] GET参数: {"pid":"2222","trade_no":"2025090211323231006","out_trade_no":"ORDER17567838938780","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"9a66a8624a634719cad0cf34882174bf","sign_type":"MD5"}
[2025-09-02 11:31:57] [INFO] POST参数: []
[2025-09-02 11:31:57] [INFO] 原始输入: 
[2025-09-02 11:31:57] [INFO] 解析参数 - 订单号: ORDER17567838938780, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-09-02 11:31:57] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17567838938780&pid=2222&trade_no=2025090211323231006&trade_status=TRADE_SUCCESS&type=wxpay
[2025-09-02 11:31:57] [INFO] 签名验证 - 期望签名: 9a66a8624a634719cad0cf34882174bf, 实际签名: 9a66a8624a634719cad0cf34882174bf
[2025-09-02 11:31:57] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17567838938780&payment_status=paid
[2025-09-02 11:31:57] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17567838938780","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17567838938780","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-09-02 11:31:33","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-02 11:31:33","updated_at":"2025-09-02 11:31:57"}}}
[2025-09-02 11:31:57] [SUCCESS] 订单 ORDER17567838938780 支付状态更新成功
[2025-09-02 14:55:49] [INFO] 收到支付通知
[2025-09-02 14:55:49] [INFO] GET参数: {"pid":"2222","trade_no":"2025090214562775293","out_trade_no":"ORDER17567960896096","type":"wxpay","name":"product","money":"24.53","trade_status":"TRADE_SUCCESS","sign":"a14e16aed1c8c964f7010784c4cd7342","sign_type":"MD5"}
[2025-09-02 14:55:49] [INFO] POST参数: []
[2025-09-02 14:55:49] [INFO] 原始输入: 
[2025-09-02 14:55:49] [INFO] 解析参数 - 订单号: ORDER17567960896096, 支付状态: TRADE_SUCCESS, 金额: 24.53
[2025-09-02 14:55:49] [INFO] 签名验证 - 原始字符串: money=24.53&name=product&out_trade_no=ORDER17567960896096&pid=2222&trade_no=2025090214562775293&trade_status=TRADE_SUCCESS&type=wxpay
[2025-09-02 14:55:49] [INFO] 签名验证 - 期望签名: a14e16aed1c8c964f7010784c4cd7342, 实际签名: a14e16aed1c8c964f7010784c4cd7342
[2025-09-02 14:55:49] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17567960896096&payment_status=paid
[2025-09-02 14:55:49] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17567960896096","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17567960896096","merchant_id":"7346442935","product_name":"助手永久","product_price":"24.53","purchase_time":"2025-09-02 14:54:49","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-02 14:54:49","updated_at":"2025-09-02 14:55:49"}}}
[2025-09-02 14:55:49] [SUCCESS] 订单 ORDER17567960896096 支付状态更新成功
[2025-09-02 18:41:54] [INFO] 收到支付通知
[2025-09-02 18:41:54] [INFO] GET参数: {"pid":"2222","trade_no":"2025090218423375372","out_trade_no":"ORDER17568096877418","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"0d9fdb96e976d1ff10c10d92839e451e","sign_type":"MD5"}
[2025-09-02 18:41:54] [INFO] POST参数: []
[2025-09-02 18:41:54] [INFO] 原始输入: 
[2025-09-02 18:41:54] [INFO] 解析参数 - 订单号: ORDER17568096877418, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-09-02 18:41:54] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17568096877418&pid=2222&trade_no=2025090218423375372&trade_status=TRADE_SUCCESS&type=wxpay
[2025-09-02 18:41:54] [INFO] 签名验证 - 期望签名: 0d9fdb96e976d1ff10c10d92839e451e, 实际签名: 0d9fdb96e976d1ff10c10d92839e451e
[2025-09-02 18:41:54] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17568096877418&payment_status=paid
[2025-09-02 18:41:54] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17568096877418","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17568096877418","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-09-02 18:41:27","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-02 18:41:27","updated_at":"2025-09-02 18:41:54"}}}
[2025-09-02 18:41:54] [SUCCESS] 订单 ORDER17568096877418 支付状态更新成功
[2025-09-02 19:01:30] [INFO] 收到支付通知
[2025-09-02 19:01:30] [INFO] GET参数: {"pid":"2222","trade_no":"2025090219020280343","out_trade_no":"ORDER17568108449422","type":"alipay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"b833e5ed4c7b8bd95f9e5d8edcb3a022","sign_type":"MD5"}
[2025-09-02 19:01:30] [INFO] POST参数: []
[2025-09-02 19:01:30] [INFO] 原始输入: 
[2025-09-02 19:01:30] [INFO] 解析参数 - 订单号: ORDER17568108449422, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-09-02 19:01:30] [INFO] 签名验证 - 原始字符串: money=13.93&name=product&out_trade_no=ORDER17568108449422&pid=2222&trade_no=2025090219020280343&trade_status=TRADE_SUCCESS&type=alipay
[2025-09-02 19:01:30] [INFO] 签名验证 - 期望签名: b833e5ed4c7b8bd95f9e5d8edcb3a022, 实际签名: b833e5ed4c7b8bd95f9e5d8edcb3a022
[2025-09-02 19:01:30] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17568108449422&payment_status=paid
[2025-09-02 19:01:30] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17568108449422","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17568108449422","merchant_id":"7346442935","product_name":"助手月度","product_price":"13.93","purchase_time":"2025-09-02 19:00:44","delivery_content":"","order_status":"paid","customer_contact":"<EMAIL>","created_at":"2025-09-02 19:00:44","updated_at":"2025-09-02 19:01:30"}}}
[2025-09-02 19:01:30] [SUCCESS] 订单 ORDER17568108449422 支付状态更新成功
[2025-09-02 23:30:56] [INFO] 收到支付通知
[2025-09-02 23:30:56] [INFO] GET参数: {"pid":"2309","trade_no":"2025090223311039363","out_trade_no":"ORDER17568269063850","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"e4a179de8da1f19ea6f482e3eaaa2cbe","sign_type":"MD5"}
[2025-09-02 23:30:56] [INFO] POST参数: []
[2025-09-02 23:30:56] [INFO] 原始输入: 
[2025-09-02 23:30:56] [INFO] 解析参数 - 订单号: ORDER17568269063850, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-09-02 23:30:56] [ERROR] 商户ID验证失败: 期望 2222, 实际 2309
[2025-09-02 23:32:00] [INFO] 收到支付通知
[2025-09-02 23:32:00] [INFO] GET参数: {"pid":"2309","trade_no":"2025090223311039363","out_trade_no":"ORDER17568269063850","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"e4a179de8da1f19ea6f482e3eaaa2cbe","sign_type":"MD5"}
[2025-09-02 23:32:00] [INFO] POST参数: []
[2025-09-02 23:32:00] [INFO] 原始输入: 
[2025-09-02 23:32:00] [INFO] 解析参数 - 订单号: ORDER17568269063850, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-09-02 23:32:00] [ERROR] 商户ID验证失败: 期望 2222, 实际 2309
[2025-09-02 23:34:06] [INFO] 收到支付通知
[2025-09-02 23:34:06] [INFO] GET参数: {"pid":"2309","trade_no":"2025090223311039363","out_trade_no":"ORDER17568269063850","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"e4a179de8da1f19ea6f482e3eaaa2cbe","sign_type":"MD5"}
[2025-09-02 23:34:06] [INFO] POST参数: []
[2025-09-02 23:34:06] [INFO] 原始输入: 
[2025-09-02 23:34:06] [INFO] 解析参数 - 订单号: ORDER17568269063850, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-09-02 23:34:06] [ERROR] 商户ID验证失败: 期望 2222, 实际 2309
[2025-09-02 23:50:09] [INFO] 收到支付通知
[2025-09-02 23:50:09] [INFO] GET参数: {"pid":"2309","trade_no":"2025090223311039363","out_trade_no":"ORDER17568269063850","type":"wxpay","name":"product","money":"13.93","trade_status":"TRADE_SUCCESS","sign":"e4a179de8da1f19ea6f482e3eaaa2cbe","sign_type":"MD5"}
[2025-09-02 23:50:09] [INFO] POST参数: []
[2025-09-02 23:50:09] [INFO] 原始输入: 
[2025-09-02 23:50:09] [INFO] 解析参数 - 订单号: ORDER17568269063850, 支付状态: TRADE_SUCCESS, 金额: 13.93
[2025-09-02 23:50:09] [ERROR] 商户ID验证失败: 期望 2222, 实际 2309
