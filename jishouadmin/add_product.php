<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
 * API 7: 新增商品
 * 所需参数: 商户秘钥, 商品名称, 商品价格, 商品简介
 * 返回: 商品上传状态
 */

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');require_once 'config.php';

// 获取参数
$merchant_secret = $_GET['merchant_secret'] ?? '';
$product_name = $_GET['product_name'] ?? '';
$product_price = $_GET['product_price'] ?? '';
$product_description = $_GET['product_description'] ?? '';

// 验证参数
if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($product_name)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品名称不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($product_price) || !is_numeric($product_price) || $product_price <= 0) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品价格必须大于0',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 验证商户
    $stmt = $pdo->prepare("SELECT * FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户秘钥无效',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 插入商品
    $stmt = $pdo->prepare("INSERT INTO products (merchant_id, product_name, product_price, product_description, stock_quantity, status) VALUES (?, ?, ?, ?, 0, 'active')");
    $stmt->execute([
        $merchant['merchant_id'],
        $product_name,
        floatval($product_price),
        $product_description
    ]);
    
    $product_id = $pdo->lastInsertId();
    
    $result = [
        'product_id' => $product_id,
        'merchant_id' => $merchant['merchant_id'],
        'product_name' => $product_name,
        'product_price' => $product_price,
        'product_description' => $product_description,
        'stock_quantity' => 0,
        'status' => 'active'
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '商品添加成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品添加失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 