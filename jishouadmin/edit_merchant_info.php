<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');require_once 'config.php';

$merchant_secret = $_GET['merchant_secret'] ?? '';
$shop_name = $_GET['shop_name'] ?? '';
$shop_description = $_GET['shop_description'] ?? '';

if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 根据商户秘钥查找商户
    $stmt = $pdo->prepare("SELECT merchant_id, shop_name, shop_description FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户不存在或秘钥错误',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建更新字段
    $update_fields = [];
    $update_values = [];
    
    if (!empty($shop_name)) {
        $update_fields[] = "shop_name = ?";
        $update_values[] = $shop_name;
    }
    
    if (!empty($shop_description)) {
        $update_fields[] = "shop_description = ?";
        $update_values[] = $shop_description;
    }
    
    // 如果没有要更新的字段
    if (empty($update_fields)) {
        echo json_encode([
            'status' => 'success',
            'message' => '没有需要更新的信息',
            'data' => [
                'merchant_id' => $merchant['merchant_id'],
                'shop_name' => $merchant['shop_name'],
                'shop_description' => $merchant['shop_description'],
                'updated_fields' => []
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 添加更新时间
    $update_fields[] = "updated_at = NOW()";
    
    // 添加WHERE条件
    $update_values[] = $merchant_secret;
    
    // 执行更新
    $sql = "UPDATE merchants SET " . implode(", ", $update_fields) . " WHERE merchant_secret = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($update_values);
    
    // 获取更新后的商户信息
    $stmt = $pdo->prepare("SELECT merchant_id, shop_name, shop_description, merchant_balance, payment_qr_code, created_at, updated_at FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $updated_merchant = $stmt->fetch();
    
    // 构建更新的字段列表
    $updated_fields = [];
    if (!empty($shop_name)) {
        $updated_fields[] = 'shop_name';
    }
    if (!empty($shop_description)) {
        $updated_fields[] = 'shop_description';
    }
    
    $result = [
        'merchant_id' => $updated_merchant['merchant_id'],
        'shop_name' => $updated_merchant['shop_name'],
        'shop_description' => $updated_merchant['shop_description'],
        'merchant_balance' => $updated_merchant['merchant_balance'],
        'payment_qr_code' => $updated_merchant['payment_qr_code'],
        'created_at' => $updated_merchant['created_at'],
        'updated_at' => $updated_merchant['updated_at'],
        'updated_fields' => $updated_fields
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '商户信息更新成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '更新商户信息失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 