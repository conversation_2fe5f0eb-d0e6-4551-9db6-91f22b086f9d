<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MIKA云寄售投诉工单系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --success: #27ae60;
            --warning: #f39c12;
            --danger: #e74c3c;
            --light: #f8f9fa;
            --dark: #1a2530;
            --gray: #7f8c8d;
            --border: #e0e6ed;
            --tab-color: #4CAF50; /* 浅绿色标签页 */
        }
        
        body {
            background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            padding-bottom: 60px;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--dark) 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px;
            border-bottom: 5px solid var(--secondary);
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: '';
            position: absolute;
            top: -50px;
            left: -50px;
            width: 150px;
            height: 150px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 50%;
        }
        
        header::after {
            content: '';
            position: absolute;
            bottom: -80px;
            right: -80px;
            width: 200px;
            height: 200px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 50%;
        }
        
        header h1 {
            font-size: 2.8rem;
            margin-bottom: 15px;
            font-weight: 700;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        .nav-tabs {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            padding: 0 20px;
            display: flex;
            overflow-x: auto;
        }
        
        .nav-tab {
            padding: 18px 25px;
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--gray);
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }
        
        .nav-tab::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--tab-color);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        .nav-tab.active {
            color: var(--tab-color);
            font-weight: 600;
        }
        
        .nav-tab.active::after {
            transform: scaleX(1);
            transform-origin: left;
        }
        
        .nav-tab:hover:not(.active) {
            color: var(--primary);
        }
        
        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            transform: translateY(0);
            opacity: 1;
        }
        
        .card.hidden {
            transform: translateY(20px);
            opacity: 0;
            height: 0;
            margin-bottom: 0;
            overflow: hidden;
        }
        
        .card.visible {
            transform: translateY(0);
            opacity: 1;
            height: auto;
            margin-bottom: 30px;
        }
        
        .card:hover {
            transform: translateY(-7px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--secondary) 0%, #2980b9 100%);
            color: white;
            padding: 22px;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2c3e50);
        }
        
        .card-header i {
            margin-right: 15px;
            font-size: 1.8rem;
        }
        
        .card-body {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 28px;
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
            color: var(--primary);
            font-size: 1.15rem;
            display: flex;
            align-items: center;
        }
        
        label i {
            margin-right: 10px;
            color: var(--secondary);
            width: 24px;
            text-align: center;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 16px;
            border: 1px solid var(--border);
            border-radius: 10px;
            font-size: 1.1rem;
            transition: all 0.3s;
            background: #f9fbfd;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--secondary);
            outline: none;
            box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.2);
            background: white;
        }
        
        .btn {
            display: inline-block;
            padding: 16px 36px;
            background: linear-gradient(135deg, var(--secondary) 0%, #2980b9 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.15rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            transition: 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
        }
        
        .btn:active {
            transform: translateY(1px);
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid var(--secondary);
            color: var(--secondary);
            box-shadow: none;
        }
        
        .btn-outline:hover {
            background: rgba(52, 152, 219, 0.1);
        }
        
        .btn-success {
            background: linear-gradient(135deg, var(--success) 0%, #219653 100%);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }
        
        .btn-success:hover {
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .btn:disabled::before {
            display: none;
        }
        
        .timeline {
            position: relative;
            padding-left: 40px;
            margin-top: 20px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--secondary);
            border-radius: 3px;
        }
        
        .timeline-item {
            position: relative;
            padding-bottom: 35px;
            padding-left: 35px;
            opacity: 1;
            transform: translateY(0);
            transition: all 0.5s ease;
        }
        
        .timeline-item.hidden {
            opacity: 0;
            transform: translateY(20px);
            height: 0;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }
        
        .timeline-item.visible {
            opacity: 1;
            transform: translateY(0);
            height: auto;
            padding-bottom: 35px;
        }
        
        .timeline-dot {
            position: absolute;
            left: -1px;
            top: 5px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: white;
            border: 4px solid var(--secondary);
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .timeline-dot i {
            color: var(--secondary);
            font-size: 14px;
        }
        
        .timeline-content {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            position: relative;
            transition: all 0.3s ease;
        }
        
        .timeline-content:hover {
            transform: translateX(5px);
        }
        
        .timeline-content::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 20px;
            width: 0;
            height: 0;
            border-top: 12px solid transparent;
            border-bottom: 12px solid transparent;
            border-right: 20px solid #f8f9fa;
        }
        
        .timeline-date {
            font-size: 0.95rem;
            color: var(--gray);
            margin-bottom: 10px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .timeline-date i {
            margin-right: 8px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .status-badge::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            transition: 0.5s;
        }
        
        .status-badge:hover::after {
            left: 100%;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-processing {
            background: #d1ecff;
            color: #004085;
        }
        
        .status-resolved {
            background: #d4f5e0;
            color: #155724;
        }
        
        .merchant-response {
            background: #e3f2fd;
            border-left: 4px solid var(--secondary);
            padding: 18px;
            border-radius: 0 10px 10px 0;
            margin-top: 18px;
            position: relative;
        }
        
        .merchant-response::before {
            content: '商户回复';
            position: absolute;
            top: -12px;
            left: 0;
            background: var(--secondary);
            color: white;
            padding: 3px 12px;
            font-size: 0.85rem;
            font-weight: 600;
            border-radius: 0 0 5px 0;
        }
        
        .complaint-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 35px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
            border-left: 4px solid var(--secondary);
            transition: all 0.3s ease;
        }
        
        .info-item:hover {
            transform: translateY(-5px);
        }
        
        .info-item h4 {
            color: var(--gray);
            font-size: 1.05rem;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .info-item h4 i {
            margin-right: 10px;
            color: var(--secondary);
        }
        
        .info-item p {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
        }
        
        .d-none {
            display: none;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mt-4 {
            margin-top: 2rem;
        }
        
        .alert {
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
        }
        
        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 8px;
            height: 100%;
        }
        
        .alert-success {
            background: #d4f5e0;
            color: #155724;
        }
        
        .alert-success::before {
            background: var(--success);
        }
        
        .complaint-id {
            background: linear-gradient(135deg, #e3f2fd 0%, #d1ecff 100%);
            padding: 20px;
            border-radius: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-align: center;
            margin: 25px 0;
            border: 2px dashed var(--secondary);
            position: relative;
        }
        
        .section-title {
            font-size: 1.5rem;
            color: var(--primary);
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 120px;
            height: 3px;
            background: var(--secondary);
            border-radius: 3px;
        }
        
        .section-title i {
            margin-right: 15px;
            color: var(--secondary);
            background: #e3f2fd;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .action-buttons {
            display: flex;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .search-box {
            display: flex;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .search-box input {
            flex: 1;
            border: none;
            border-radius: 0;
            padding: 18px 25px;
            background: white;
            font-size: 1.1rem;
        }
        
        .search-box button {
            border-radius: 0;
            border: none;
            padding: 0 35px;
        }
        
        .progress-bar-container {
            height: 10px;
            background: #e0e6ed;
            border-radius: 5px;
            overflow: hidden;
            margin: 25px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary), #1a5276);
            border-radius: 5px;
            width: 30%;
            transition: width 0.8s ease;
        }
        
        @media (max-width: 768px) {
            .complaint-info {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            header h1 {
                font-size: 2.2rem;
            }
            
            .nav-tabs {
                flex-direction: column;
            }
            
            .nav-tab {
                padding: 15px;
                border-bottom: 1px solid var(--border);
            }
            
            .timeline {
                padding-left: 30px;
            }
        }
        
        /* 加载动画 */
        .loader {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 提交按钮加载状态 */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }
        
        .btn-loading::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            width: 20px;
            height: 20px;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        
        /* 闪烁提示 */
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .blink {
            animation: blink 1.5s infinite;
        }
        
        /* 按钮脉冲效果 */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
            100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1><i class="fas fa-shield-alt"></i> MIKA云寄售投诉工单系统</h1>
            <p>维护您的合法权益，保障交易安全，共建诚信发卡环境</p>
        </div>
    </header>
    
    <div class="container">
        <div class="nav-tabs">
            <div class="nav-tab active" onclick="showTab('complaint')">提交投诉</div>
            <div class="nav-tab" onclick="showTab('progress')">查询进度</div>
            <div class="nav-tab" onclick="showTab('history')">历史记录</div>
            <div class="nav-tab" onclick="showTab('guide')">投诉指南</div>
        </div>
        
        <!-- 新投诉表单 -->
        <div class="card visible" id="complaintTab">
            <div class="card-header">
                <i class="fas fa-edit"></i> 填写投诉信息
            </div>
            <div class="card-body">
                <form id="complaintForm">
                    <div class="form-group">
                        <label for="cardType"><i class="fas fa-credit-card"></i> 卡片类型</label>
                        <select id="cardType" required>
                            <option value="">请选择卡片类型</option>
                            <option value="game">游戏点卡</option>
                            <option value="gift">礼品卡</option>
                            <option value="member">会员卡</option>
                            <option value="recharge">充值卡</option>
                            <option value="other">其他类型</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="merchantName"><i class="fas fa-store"></i> 商户名称</label>
                        <input type="text" id="merchantName" placeholder="请输入商户全称" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="complaintReason"><i class="fas fa-exclamation-circle"></i> 投诉原因</label>
                        <select id="complaintReason" required>
                            <option value="">请选择投诉原因</option>
                            <option value="invalid">卡密无效/无法使用</option>
                            <option value="used">卡密已被使用</option>
                            <option value="not-delivered">未收到卡密</option>
                            <option value="value-error">面值不符/金额错误</option>
                            <option value="expired">卡密已过期</option>
                            <option value="fraud">欺诈行为/虚假宣传</option>
                            <option value="delay">卡密延迟发放</option>
                            <option value="service">客户服务问题</option>
                            <option value="refund">退款问题</option>
                            <option value="other">其他问题</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="complaintDetails"><i class="fas fa-file-alt"></i> 详细描述</label>
                        <textarea id="complaintDetails" rows="4" placeholder="请详细描述您遇到的问题、交易过程和期望的解决方案..." required></textarea>
                    </div>
                    
                    <div class="row" style="display: flex; flex-wrap: wrap; margin: 0 -15px;">
                        <div class="form-group" style="flex: 1 0 300px; padding: 0 15px;">
                            <label for="orderNumber"><i class="fas fa-receipt"></i> 订单号</label>
                            <input type="text" id="orderNumber" required>
                        </div>
                        
                        <div class="form-group" style="flex: 1 0 300px; padding: 0 15px;">
                            <label for="lossAmount"><i class="fas fa-money-bill-wave"></i> 损失金额 (元)</label>
                            <input type="number" id="lossAmount" min="0" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="row" style="display: flex; flex-wrap: wrap; margin: 0 -15px;">
                        <div class="form-group" style="flex: 1 0 300px; padding: 0 15px;">
                            <label for="contactInfo"><i class="fas fa-phone-alt"></i> 联系方式</label>
                            <input type="text" id="contactInfo" placeholder="电话/邮箱" required>
                        </div>
                        
                        <div class="form-group" style="flex: 1 0 300px; padding: 0 15px;">
                            <label for="paymentMethod"><i class="fas fa-wallet"></i> 支付方式</label>
                            <select id="paymentMethod" required>
                                <option value="">请选择支付方式</option>
                                <option value="alipay">支付宝</option>
                                <option value="wechat">微信支付</option>
                                <option value="bank">银行卡</option>
                                <option value="other">其他方式</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="attachment"><i class="fas fa-paperclip"></i> 上传凭证</label>
                        <input type="file" id="attachment" multiple>
                        <small style="display: block; margin-top: 8px; color: var(--gray);">可上传交易截图、聊天记录等证明文件（最多5个）</small>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" id="agreeTerms" required>
                            <label for="agreeTerms" style="display: inline; font-weight: normal;">
                                我已阅读并同意<a href="#" style="color: var(--secondary);">《投诉服务协议》</a>和<a href="#" style="color: var(--secondary);">《隐私政策》</a>
                            </label>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button type="reset" class="btn btn-outline">重置表单</button>
                        <button type="submit" class="btn pulse" id="submitBtn">提交投诉</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 提交成功显示 -->
        <div class="card hidden" id="submissionSuccess">
            <div class="card-header">
                <i class="fas fa-check-circle"></i> 投诉提交成功
            </div>
            <div class="card-body text-center">
                <div style="font-size: 6rem; color: var(--success); margin: 20px 0;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 style="margin-bottom: 20px; color: var(--primary);">您的投诉已成功提交！</h2>
                <p style="font-size: 1.3rem; margin-bottom: 30px; max-width: 700px; margin-left: auto; margin-right: auto;">
                    我们已收到您的投诉信息，平台客服将在24小时内受理您的投诉，请保持联系方式畅通。
                </p>
                
                <div class="complaint-id blink">
                    <i class="fas fa-ticket-alt"></i> 投诉单号：<span id="complaintIdDisplay">CT-20230715-0852</span>
                </div>
                
                <div class="alert alert-success">
                    <h3><i class="fas fa-lightbulb"></i> 下一步操作提示</h3>
                    <p>1. 请保存好投诉单号，用于查询处理进度</p>
                    <p>2. 您可以通过本系统随时查看投诉处理进展</p>
                    <p>3. 处理过程中可能需要您补充材料，请留意通知</p>
                </div>
                
                <div class="action-buttons" style="justify-content: center;">
                    <button class="btn btn-outline" onclick="showTab('complaint')">提交新投诉</button>
                    <button class="btn pulse" onclick="showTab('progress')">查询处理进度</button>
                </div>
            </div>
        </div>
        
        <!-- 投诉进度查询 -->
        <div class="card hidden" id="progressTab">
            <div class="card-header">
                <i class="fas fa-search"></i> 投诉进度查询
            </div>
            <div class="card-body">
                <div class="search-box">
                    <input type="text" id="searchComplaintId" placeholder="请输入投诉单号">
                    <button class="btn" onclick="searchComplaint()"><i class="fas fa-search"></i> 查询</button>
                </div>
                
                <div id="complaintProgress" class="d-none">
                    <div class="complaint-info">
                        <div class="info-item">
                            <h4><i class="fas fa-ticket-alt"></i> 投诉单号</h4>
                            <p id="progressComplaintId">CT-20230715-0852</p>
                        </div>
                        
                        <div class="info-item">
                            <h4><i class="fas fa-store"></i> 商户名称</h4>
                            <p id="progressMerchantName">极速发卡网</p>
                        </div>
                        
                        <div class="info-item">
                            <h4><i class="fas fa-exclamation-circle"></i> 投诉原因</h4>
                            <p id="progressReason">卡密无效/无法使用</p>
                        </div>
                        
                        <div class="info-item">
                            <h4><i class="fas fa-hourglass-half"></i> 当前状态</h4>
                            <p><span class="status-badge status-processing" id="progressStatus">处理中</span></p>
                        </div>
                    </div>
                    
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                    
                    <div class="section-title">
                        <i class="fas fa-history"></i> 处理时间线
                    </div>
                    
                    <div class="timeline" id="progressTimeline">
                        <div class="timeline-item visible">
                            <div class="timeline-dot"><i class="fas fa-plus"></i></div>
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-clock"></i> 2023-07-15 10:05:24</div>
                                <h3>投诉已提交</h3>
                                <p>您的投诉已成功提交至系统，等待平台审核处理。</p>
                                <p>投诉单号：CT-20230715-0852</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item visible">
                            <div class="timeline-dot"><i class="fas fa-check"></i></div>
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-clock"></i> 2023-07-15 10:35:18</div>
                                <h3>平台已受理</h3>
                                <p>平台客服已受理您的投诉，正在核实相关信息。</p>
                                <p>处理专员：王客服（工号 CS1082）</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item visible">
                            <div class="timeline-dot"><i class="fas fa-envelope"></i></div>
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-clock"></i> 2023-07-15 11:20:42</div>
                                <h3>联系商户处理</h3>
                                <p>平台已联系相关商户，要求其在24小时内核实并处理您的投诉。</p>
                                <div class="merchant-response">
                                    <p>已收到投诉信息，正在核实具体情况，将尽快处理。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-item hidden" id="step4">
                            <div class="timeline-dot"><i class="fas fa-cogs"></i></div>
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-clock"></i> 2023-07-15 14:15:33</div>
                                <h3>商户正在处理</h3>
                                <p>商户已确认收到您的投诉，正在进行内部调查和处理。</p>
                                <div class="merchant-response">
                                    <p>尊敬的客户，我们非常重视您的投诉，已安排专人核查此订单。对于给您带来的不便，我们深表歉意。核查完成后我们将第一时间联系您解决问题。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-item hidden" id="step5">
                            <div class="timeline-dot"><i class="fas fa-search"></i></div>
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-clock"></i> 2023-07-15 16:40:12</div>
                                <h3>平台介入调查</h3>
                                <p>平台风控部门已介入调查，正在核实交易记录和相关证据。</p>
                                <p>调查人员：风控部-张经理</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item hidden" id="step6">
                            <div class="timeline-dot"><i class="fas fa-gavel"></i></div>
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-clock"></i> 2023-07-16 09:30:45</div>
                                <h3>调查结果</h3>
                                <div class="alert alert-success">
                                    <h3><i class="fas fa-check-circle"></i> 处理结果</h3>
                                    <p>经平台调查核实，该商户确实存在违规行为，平台已采取以下措施：</p>
                                    <ul style="margin-left: 20px; margin-top: 10px;">
                                        <li>责令商户退还您损失金额 200 元</li>
                                        <li>对商户处以罚款并扣除信用分</li>
                                        <li>要求商户限期整改，加强内部管理</li>
                                    </ul>
                                    <p class="mt-4"><strong>最终处理：</strong>已经认定该商户存在违规行为，已安排整改，感谢配合！</p>
                                </div>
                                <div class="merchant-response">
                                    <p>尊敬的客户，我们已按照平台要求进行整改，并已退还您的损失金额。对于此次给您带来的不便，我们再次表示诚挚的歉意。感谢您的监督，我们将努力提供更好的服务。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn btn-outline" onclick="showTab('complaint')">
                            <i class="fas fa-plus"></i> 提交新投诉
                        </button>
                        <button class="btn" id="refreshBtn" onclick="updateProgress()">
                            <i class="fas fa-sync-alt"></i> 刷新进度
                        </button>
                        <button class="btn btn-success" id="withdrawBtn" onclick="withdrawComplaint()">
                            <i class="fas fa-times-circle"></i> 撤销投诉
                        </button>
                    </div>
                </div>
                
                <div id="noComplaint" class="text-center" style="padding: 50px 20px;">
                    <i class="fas fa-search" style="font-size: 5rem; color: #e0e6ed; margin-bottom: 20px;"></i>
                    <h3 style="color: var(--gray);">查询投诉进度</h3>
                    <p>请输入投诉单号查询您的投诉处理进度</p>
                </div>
            </div>
        </div>
        
        <!-- 投诉指南 -->
        <div class="card hidden" id="guideTab">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> 投诉指南与注意事项
            </div>
            <div class="card-body">
                <div class="section-title">
                    <i class="fas fa-lightbulb"></i> 如何有效投诉
                </div>
                
                <div class="complaint-info">
                    <div class="info-item">
                        <h4><i class="fas fa-clipboard-list"></i> 准备材料</h4>
                        <ul style="padding-left: 20px; list-style-type: none;">
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 订单号和交易时间</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 支付凭证和截图</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 问题详细描述</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 与商户的沟通记录</li>
                        </ul>
                    </div>
                    
                    <div class="info-item">
                        <h4><i class="fas fa-file-alt"></i> 填写要点</h4>
                        <ul style="padding-left: 20px; list-style-type: none;">
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 准确描述问题细节</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 提供完整订单信息</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 明确您的诉求</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 上传相关证明文件</li>
                        </ul>
                    </div>
                    
                    <div class="info-item">
                        <h4><i class="fas fa-clock"></i> 处理流程</h4>
                        <ul style="padding-left: 20px; list-style-type: none;">
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 提交投诉（即时）</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 平台受理（24小时内）</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 商户处理（1-3天）</li>
                            <li style="margin-bottom: 10px;"><i class="fas fa-check-circle" style="color: var(--success); margin-right: 8px;"></i> 平台裁决（3-7天）</li>
                        </ul>
                    </div>
                </div>
                
                <div class="section-title">
                    <i class="fas fa-shield-alt"></i> 权益保障
                </div>
                
                <div class="complaint-info">
                    <div class="info-item">
                        <h4><i class="fas fa-lock"></i> 安全保障</h4>
                        <p>所有投诉信息加密处理，保障您的隐私安全</p>
                    </div>
                    
                    <div class="info-item">
                        <h4><i class="fas fa-balance-scale"></i> 公平公正</h4>
                        <p>平台中立处理，不偏袒任何一方</p>
                    </div>
                    
                    <div class="info-item">
                        <h4><i class="fas fa-hand-holding-usd"></i> 先行赔付</h4>
                        <p>平台设立保证金，符合条件的投诉可先行赔付</p>
                    </div>
                </div>
                
                <div class="section-title">
                    <i class="fas fa-question-circle"></i> 常见问题
                </div>
                
                <div class="info-item">
                    <h4><i class="fas fa-question"></i> 投诉处理需要多长时间？</h4>
                    <p>普通投诉处理周期为3-7个工作日，复杂案件可能需要14个工作日。</p>
                </div>
                
                <div class="info-item">
                    <h4><i class="fas fa-question"></i> 如何撤销投诉？</h4>
                    <p>在投诉处理进度页面，点击"撤销投诉"按钮即可撤销投诉。</p>
                </div>
                
                <div class="info-item">
                    <h4><i class="fas fa-question"></i> 投诉后商户不配合怎么办？</h4>
                    <p>平台将根据商户协议采取相应措施，包括但不限于冻结账户、扣除保证金等。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示指定标签页
        function showTab(tabId) {
            // 隐藏所有标签页
            document.getElementById('complaintTab').classList.remove('visible');
            document.getElementById('complaintTab').classList.add('hidden');
            document.getElementById('submissionSuccess').classList.remove('visible');
            document.getElementById('submissionSuccess').classList.add('hidden');
            document.getElementById('progressTab').classList.remove('visible');
            document.getElementById('progressTab').classList.add('hidden');
            document.getElementById('guideTab').classList.remove('visible');
            document.getElementById('guideTab').classList.add('hidden');
            
            // 显示指定标签页
            if(tabId === 'complaint') {
                document.getElementById('complaintTab').classList.remove('hidden');
                document.getElementById('complaintTab').classList.add('visible');
            } else if(tabId === 'progress') {
                document.getElementById('progressTab').classList.remove('hidden');
                document.getElementById('progressTab').classList.add('visible');
            } else if(tabId === 'guide') {
                document.getElementById('guideTab').classList.remove('hidden');
                document.getElementById('guideTab').classList.add('visible');
            }
            
            // 更新导航标签状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            if(tabId === 'complaint') {
                document.querySelector('.nav-tab:nth-child(1)').classList.add('active');
            } else if(tabId === 'progress') {
                document.querySelector('.nav-tab:nth-child(2)').classList.add('active');
            } else if(tabId === 'guide') {
                document.querySelector('.nav-tab:nth-child(4)').classList.add('active');
            }
        }
        
        // 提交投诉表单
        document.getElementById('complaintForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.classList.add('btn-loading');
            
            // 模拟提交延迟
            setTimeout(function() {
                // 生成随机投诉单号
                const date = new Date();
                const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
                const complaintId = `CT-${date.getFullYear()}${(date.getMonth()+1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}-${randomNum}`;
                
                // 更新显示信息
                document.getElementById('complaintIdDisplay').textContent = complaintId;
                document.getElementById('progressComplaintId').textContent = complaintId;
                document.getElementById('progressMerchantName').textContent = document.getElementById('merchantName').value || "未提供";
                
                const reasonSelect = document.getElementById('complaintReason');
                document.getElementById('progressReason').textContent = reasonSelect.options[reasonSelect.selectedIndex].text;
                
                // 隐藏表单，显示成功页面
                document.getElementById('complaintTab').classList.remove('visible');
                document.getElementById('complaintTab').classList.add('hidden');
                
                document.getElementById('submissionSuccess').classList.remove('hidden');
                document.getElementById('submissionSuccess').classList.add('visible');
                
                // 更新导航标签
                const tabs = document.querySelectorAll('.nav-tab');
                tabs.forEach(tab => tab.classList.remove('active'));
                document.querySelector('.nav-tab:nth-child(1)').classList.add('active');
                
                submitBtn.disabled = false;
                submitBtn.classList.remove('btn-loading');
            }, 2000); // 2秒延迟模拟提交过程
        });
        
        // 查询投诉进度
        function searchComplaint() {
            const complaintId = document.getElementById('searchComplaintId').value.trim();
            
            if(complaintId) {
                document.getElementById('noComplaint').classList.add('d-none');
                document.getElementById('complaintProgress').classList.remove('d-none');
                
                // 重置进度
                document.getElementById('step4').classList.remove('visible');
                document.getElementById('step4').classList.add('hidden');
                document.getElementById('step5').classList.remove('visible');
                document.getElementById('step5').classList.add('hidden');
                document.getElementById('step6').classList.remove('visible');
                document.getElementById('step6').classList.add('hidden');
                document.getElementById('progressStatus').className = 'status-badge status-processing';
                document.getElementById('progressStatus').textContent = '处理中';
                document.getElementById('progressBar').style.width = '30%';
                document.getElementById('withdrawBtn').disabled = false;
                document.getElementById('refreshBtn').disabled = false;
                
                // 更新投诉单号显示
                document.getElementById('progressComplaintId').textContent = complaintId;
            } else {
                alert('请输入投诉单号');
            }
        }
        
        // 更新进度
        function updateProgress() {
            const refreshBtn = document.getElementById('refreshBtn');
            refreshBtn.disabled = true;
            refreshBtn.classList.add('btn-loading');
            
            // 随机决定显示哪些进度
            const step4 = document.getElementById('step4');
            const step5 = document.getElementById('step5');
            const step6 = document.getElementById('step6');
            const progressBar = document.getElementById('progressBar');
            
            // 随机延迟1-3秒
            const delay = 1000 + Math.random() * 2000;
            
            setTimeout(function() {
                if (step4.classList.contains('hidden')) {
                    step4.classList.remove('hidden');
                    step4.classList.add('visible');
                    progressBar.style.width = '50%';
                } else if (step5.classList.contains('hidden')) {
                    step5.classList.remove('hidden');
                    step5.classList.add('visible');
                    progressBar.style.width = '70%';
                } else if (step6.classList.contains('hidden')) {
                    step6.classList.remove('hidden');
                    step6.classList.add('visible');
                    document.getElementById('progressStatus').className = 'status-badge status-resolved';
                    document.getElementById('progressStatus').textContent = '已解决';
                    progressBar.style.width = '100%';
                    
                    // 禁用刷新按钮
                    document.getElementById('refreshBtn').disabled = true;
                }
                
                refreshBtn.disabled = false;
                refreshBtn.classList.remove('btn-loading');
                
                // 滚动到最新进度
                const timelineItems = document.querySelectorAll('.timeline-item.visible');
                if(timelineItems.length > 0) {
                    timelineItems[timelineItems.length - 1].scrollIntoView({behavior: 'smooth', block: 'nearest'});
                }
            }, delay);
        }
        
        // 撤销投诉
        function withdrawComplaint() {
            if (confirm("确定要撤销此投诉吗？撤销后将无法恢复！")) {
                // 添加撤销记录
                const timeline = document.getElementById('progressTimeline');
                const newItem = document.createElement('div');
                newItem.className = 'timeline-item visible';
                newItem.innerHTML = `
                    <div class="timeline-dot"><i class="fas fa-ban"></i></div>
                    <div class="timeline-content">
                        <div class="timeline-date"><i class="fas fa-clock"></i> ${new Date().toLocaleString()}</div>
                        <h3>投诉已撤销</h3>
                        <p>您已主动撤销此投诉。</p>
                        <div class="alert" style="background: #f8d7da; color: #721c24; border-left: 4px solid var(--danger);">
                            投诉撤销后，平台将终止处理此投诉。如有需要，您可以重新提交投诉。
                        </div>
                    </div>
                `;
                timeline.appendChild(newItem);
                
                // 更新状态
                document.getElementById('progressStatus').className = 'status-badge status-pending';
                document.getElementById('progressStatus').textContent = '已撤销';
                
                // 禁用撤销按钮
                document.getElementById('withdrawBtn').disabled = true;
                
                // 滚动到底部
                newItem.scrollIntoView({behavior: 'smooth'});
            }
        }
        
        // 自动更新进度（模拟后台更新）
        setInterval(function() {
            const progressSection = document.getElementById('complaintProgress');
            if (!progressSection.classList.contains('d-none')) {
                // 随机决定是否更新进度（30%概率）
                if(Math.random() < 0.3) {
                    updateProgress();
                }
            }
        }, 30000); // 每30秒检查一次
    </script>
</body>
</html>