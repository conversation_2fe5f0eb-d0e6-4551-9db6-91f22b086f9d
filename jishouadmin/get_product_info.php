<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

$product_id = $_GET['product_id'] ?? '';

if (empty($product_id) || !is_numeric($product_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品ID不能为空且必须为数字',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 查询商品信息
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ? AND status != 'deleted'");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode([
            'status' => 'error',
            'message' => '商品不存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    echo json_encode([
        'status' => 'success',
        'message' => '获取商品信息成功',
        'data' => $product
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取商品信息失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 