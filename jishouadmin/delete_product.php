<?php
/**
 * API 10: 删除商品
 * 所需参数: 商户秘钥, 商品ID
 * 返回: 删除状态
 */

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');require_once 'config.php';

// 获取参数
$merchant_secret = $_GET['merchant_secret'] ?? '';
$product_id = $_GET['product_id'] ?? '';

// 验证参数
if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($product_id) || !is_numeric($product_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品ID不能为空且必须为数字',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 验证商户
    $stmt = $pdo->prepare("SELECT * FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户秘钥无效',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证商品是否属于该商户
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ? AND merchant_id = ? AND status != 'deleted'");
    $stmt->execute([$product_id, $merchant['merchant_id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode([
            'status' => 'error',
            'message' => '商品不存在或无权限操作',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 软删除商品（将状态设置为deleted）
    $stmt = $pdo->prepare("UPDATE products SET status = 'deleted' WHERE product_id = ?");
    $stmt->execute([$product_id]);
    
    $result = [
        'product_id' => $product_id,
        'product_name' => $product['product_name'],
        'delete_status' => 'success',
        'message' => '商品删除成功'
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '商品删除成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '删除商品失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 