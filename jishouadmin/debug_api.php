<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>API调试测试</h1>";

// 1. 创建商品
echo "<h2>1. 创建商品</h2>";
$add_product_url = "add_product.php?merchant_secret=secret_key_001&product_name=测试商品&product_price=99.00&product_description=这是一个测试商品";
echo "调用URL: " . $add_product_url . "<br>";

$add_product_result = file_get_contents($add_product_url);
echo "返回结果: <pre>" . htmlspecialchars($add_product_result) . "</pre><br>";

$add_product_data = json_decode($add_product_result, true);
if ($add_product_data && $add_product_data['status'] === 'success') {
    $product_id = $add_product_data['data']['product_id'];
    echo "✅ 商品创建成功，商品ID: " . $product_id . "<br><br>";
} else {
    echo "❌ 商品创建失败<br><br>";
    exit;
}

// 2. 新增卡密
echo "<h2>2. 新增卡密</h2>";
$add_card_url = "add_card_keys.php?merchant_secret=secret_key_001&product_id=" . $product_id . "&card_key_content=CARD001-XXXX-YYYY-ZZZZ-CARD002-XXXX-YYYY-ZZZZ";
echo "调用URL: " . $add_card_url . "<br>";

$add_card_result = file_get_contents($add_card_url);
echo "返回结果: <pre>" . htmlspecialchars($add_card_result) . "</pre><br>";

$add_card_data = json_decode($add_card_result, true);
if ($add_card_data && $add_card_data['status'] === 'success') {
    echo "✅ 卡密添加成功<br><br>";
} else {
    echo "❌ 卡密添加失败<br><br>";
}

// 3. 发起订单
echo "<h2>3. 发起订单</h2>";
$create_order_url = "create_order.php?customer_contact=<EMAIL>&product_id=" . $product_id;
echo "调用URL: " . $create_order_url . "<br>";

$create_order_result = file_get_contents($create_order_url);
echo "返回结果: <pre>" . htmlspecialchars($create_order_result) . "</pre><br>";

$create_order_data = json_decode($create_order_result, true);
if ($create_order_data && $create_order_data['status'] === 'success') {
    $order_id = $create_order_data['data']['order_info']['order_id'];
    $payment_url = $create_order_data['data']['payment_url'];
    echo "✅ 订单创建成功<br>";
    echo "订单ID: " . $order_id . "<br>";
    echo "支付链接: " . $payment_url . "<br><br>";
} else {
    echo "❌ 订单创建失败<br><br>";
    exit;
}

// 4. 检查支付状态
echo "<h2>4. 检查支付状态</h2>";
$check_payment_url = "check_payment_status.php?order_id=" . $order_id;
echo "调用URL: " . $check_payment_url . "<br>";

$check_payment_result = file_get_contents($check_payment_url);
echo "返回结果: <pre>" . htmlspecialchars($check_payment_result) . "</pre><br>";

$check_payment_data = json_decode($check_payment_result, true);
if ($check_payment_data && $check_payment_data['status'] === 'success') {
    echo "✅ 支付状态检查成功<br>";
    echo "订单状态: " . $check_payment_data['data']['order_status'] . "<br>";
} else {
    echo "❌ 支付状态检查失败<br>";
}

echo "<br><h2>调试完成</h2>";
?> 